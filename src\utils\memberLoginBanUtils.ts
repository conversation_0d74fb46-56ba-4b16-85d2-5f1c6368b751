import { RoleTypeEnum } from "@/enums/role"
import { SystemVarEnum } from "@/enums/systemVar"
import { getSystemVarValueByList, getUserIpInfo } from "@/services/storeApi"

interface BanConfigInterface {
    /**是否开启会员限制功能 */
    enable: boolean,
    /**省份限制 */
    pL: Array<{
        /**省份名 */
        n: string,
        /**忽略的经销商ID */
        idL: Array<string>,
    }>,
    /**城市限制 */
    cL: Array<{
        /**城市名 */
        n: string,
        /**忽略的经销商ID */
        idL: Array<string>,
    }>,
    /**是否忽略已注册的用户 */
    eI: boolean,
}

/**判断会员是否被区域限制 */
export async function checkMemberLoginBan(userInfo,ipInfo):Promise<boolean>{
    try {
        const [banConfigResp] = await getSystemVarValueByList([SystemVarEnum.loginBanConfig])
        const banConfig:BanConfigInterface = JSON.parse(banConfigResp.value)
        if(banConfig.enable){
            const {type,groupMgrId,dealerId} = userInfo
            if(type == RoleTypeEnum.Member && (groupMgrId == '1000' || !groupMgrId || !banConfig.eI) && (location.pathname.indexOf('/signup/dealer') == -1 && location.pathname.indexOf('/signup/groupMgr') == -1)){
                try {
                    //省份判断
                    for(let provCount = 0; provCount<banConfig.pL.length;provCount++){
                        const provItem = banConfig.pL[provCount]
                        if(ipInfo.data.prov == provItem.n && !provItem.idL.includes(dealerId)){
                            return true
                        }
                    }
                    //市判断
                    for(let cityCount = 0; cityCount<banConfig.cL.length;cityCount++){
                        const cityItem = banConfig.cL[cityCount]
                        if(ipInfo.data.city == cityItem.n && !cityItem.idL.includes(dealerId)){
                            return true
                        }
                    }
                    return false
                }
                catch (e) {
                    return false
                }
            }
        }
        else{
            return false
        }
    }
    catch (e) {
        return false
    }
}