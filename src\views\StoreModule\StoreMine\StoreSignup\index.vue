<template>
  <JLoadingWrapper :show="isPageLoadingRef" class="store_signup_wrapper">
    <div v-if="!isShowException" class="store_signup_container">
      <BannerContainer style="background: #FFFFFF;">
        <template #title>
          <span class="title">申请信息</span>
        </template>
        <template
          v-if="[OrgApplyTypeEnum.REGIONAL_MANAGER_APPLY, OrgApplyTypeEnum.DISTRIBUTOR_APPLY, OrgApplyTypeEnum.STORE_APPLY].includes(Number(applyTypeRef))"
        >
          <!-- 区域部门名称 -->
          <div v-if="applyTypeRef == OrgApplyTypeEnum.DISTRIBUTOR_APPLY && model.higherStructureId" class="field_item_wrapper">
            <span class="label_title" style="width: 100px;">区域部门名称</span>
            <VanField
              class="field"
              :border="false"
              readonly
              v-model="model.higherStructureName"
              input-align="right"
              placeholder="请输入"
            />
          </div>
          <!-- 姓名 -->
          <div class="field_item_wrapper">
            <span class="label_title">姓名</span>
            <VanField
              class="field"
              :border="false"
              v-model="model.applyCsName"
              input-align="right"
              placeholder="请输入姓名"
            />
          </div>
          <!-- 手机号 -->
          <div class="field_item_wrapper">
            <span class="label_title">手机号</span>
            <VanField
              class="field"
              :border="false"
              v-model="model.applyCsPhone"
              input-align="right"
              placeholder="请输入手机号"
            />
          </div>
        </template>
        <template v-if="[OrgApplyTypeEnum.STORE_APPLY].includes(Number(applyTypeRef))">
          <!-- 经销商部门名称 -->
          <div class="field_item_wrapper">
            <span class="label_title" style="width: 100px;">经销商部门名称</span>
            <VanField
              class="field"
              :border="false"
              readonly
              v-model="model.higherStructureName"
              input-align="right"
              placeholder="请输入"
            />
          </div>
          <!-- 店铺名称 -->
          <div class="field_item_wrapper">
            <span class="label_title">店铺名称</span>
            <VanField
              class="field"
              :border="false"
              v-model="model.applyStructureName"
              input-align="right"
              placeholder="请输入店铺名称"
            />
          </div>
          <!-- 收货区域 -->
          <div class="field_item_wrapper">
            <span class="label_title">收货区域</span>
            <VanField
              class="field"
              :border="false"
              v-model="addressStr"
              readonly
              input-align="right"
              placeholder="请输入收货区域"
              @click="showAddress = true"
            />
          </div>
          <!-- 详细地址 -->
          <div class="field_item_wrapper">
            <span class="label_title">详细地址</span>
            <VanField
              class="field"
              :border="false"
              v-model="model.addressDetail"
              input-align="right"
              placeholder="请输入详细地址"
            />
          </div>
          <!-- 申请备注 -->
          <div class="field_item_wrapper">
            <span class="label_title">申请备注</span>
            <VanField
              class="field"
              :border="false"
              v-model="model.applyRemark"
              input-align="right"
              placeholder="请输入备注"
            />
          </div>
        </template>
      </BannerContainer>
      <!-- 提交 -->
      <div class="footer">
        <VanButton type="danger" block round @click="handleSubmit">提交</VanButton>
      </div>
      <!-- 地址选择 -->
      <JStoreAddress v-model:show="showAddress" @confirm="handleAddressConfirm" />
    </div>
    <!-- 显示已提交 -->
    <ApplyException v-else :code="exceptionCodeRef" :customNotice="exceptionCodeTextRef" @goBack="handleGoBack" />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed, watch } from "vue";
import { useRoute } from 'vue-router';
import { showToast } from "vant";
import { OrgApplyTypeEnum, OrgApplyStatusEnum } from "@/views/StoreModule/enums";
import { isEmpty, isObject, isNUllString, isArray } from "@/utils/isUtils";
import { useMessages } from "@/hooks/useMessage";
import useGetApplicationRecord from "./hooks/useGetApplicationRecord";
import { addStoreSignupApi } from "@/services/storeApi";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import BannerContainer from "@/views/StoreModule/components/BannerContainer.vue";
import JStoreAddress from "@/views/StoreModule/components/JStoreAddress.vue";
import ApplyException from "./components/ApplyException.vue";

defineOptions({ name: 'StoreSignup' });

/** props */
const props = defineProps<{
  state: string | number;
}>();

const route = useRoute();
const applyTypeRef = ref<OrgApplyTypeEnum>(Number(route.params?.applyType ?? OrgApplyTypeEnum.REGIONAL_MANAGER_APPLY));
console.log("applyType", applyTypeRef.value);

const { isPageLoadingRef, getApplicationRecord } = useGetApplicationRecord();
/** 是否显示缺省页 */
const isShowException = ref(false);
const exceptionCodeRef = ref("5001");
const exceptionCodeTextRef = ref(null);

const { createMessageSuccess, createMessageError } = useMessages();

/** 参数初始化 */
const initParams = {
  higherStructureId: null, // 上级组织ID
  higherStructureName: '', // 上级组织名称

  applyCsName: '',
  applyCsPhone: '',
  applyStructureName: '',
  addressDetail: '',
  applyRemark: '',
  province: '',
  provinceId: null,
  city: '',
  cityId: null,
  district: '',
  districtId: null,
  town: '',
  townId: null,
};
const model = ref({...initParams});

const showAddress = ref(false);
function handleAddressConfirm(addressData: {
  area: string;
  areaId: string;
  city: string;
  cityId: string;
  province: string;
  provinceId: string;
  town: string;
  townId: string;
}) {
  const { province, provinceId, city, cityId, area, areaId, town, townId } = addressData;
  Object.assign(model.value, {
    province,
    provinceId,
    city,
    cityId,
    district: area,
    districtId: areaId,
    town,
    townId
  });
}

function handleGoBack() {
  isShowException.value = false;
}

/** 收货区域 */
const addressStr = computed(() => {
  const { province, city, district, town } = model.value;
  return [province, city, district, town].filter(Boolean).join('/');
});

/**
 * 获取提交参数
 * 根据申请类型返回不同的参数对象，并进行必要字段校验
 */
function _getApplyParams() {
  const {
    higherStructureId,
    applyCsName,
    applyCsPhone,
    applyStructureName,
    addressDetail,
    applyRemark,
    province,
    provinceId,
    city,
    cityId,
    district,
    districtId,
    town,
    townId
  } = model.value;

  const applyType = applyTypeRef.value;
  const baseParams = { higherStructureId, applyType, applyCsName, applyCsPhone };

  if (isEmpty(applyCsName) || isEmpty(applyCsPhone)) {
    showToast('请填写完整信息!');
    return null;
  }
  
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(applyCsPhone)) {
    showToast('请输入正确的手机号码!');
    return null;
  }

  switch (applyType) {
    case OrgApplyTypeEnum.REGIONAL_MANAGER_APPLY:
    case OrgApplyTypeEnum.DISTRIBUTOR_APPLY:
      return baseParams;

    case OrgApplyTypeEnum.STORE_APPLY:
      // 门店申请额外字段校验
      if (isEmpty(province) || isEmpty(applyStructureName) || isEmpty(addressDetail)) {
        showToast('请填写完整信息!');
        return null;
      }
      return {
        ...baseParams,
        applyStructureName,
        addressDetail,
        applyRemark,
        province,
        provinceId,
        city,
        cityId,
        district,
        districtId,
        town,
        townId
      };

    default:
      return null;
  }
}

/** 提交 */
async function handleSubmit() {
  try {
    const _params = _getApplyParams();
    if (!_params) return;
    const resp = await addStoreSignupApi(_params);
    if (resp) {
      showToast('申请成功');
      exceptionCodeRef.value = "5001";
      isShowException.value = true;
    }
  } catch (error) {
    createMessageError("申请失败：" + error);
  }
}

/** 监听 */
watch(() => applyTypeRef.value, (newVal) => {
  if (newVal) {
    if (newVal == OrgApplyTypeEnum.REGIONAL_MANAGER_APPLY) document.title = '区域经理申请';
    if (newVal == OrgApplyTypeEnum.DISTRIBUTOR_APPLY) document.title = '经销商申请';
    if (newVal == OrgApplyTypeEnum.STORE_APPLY) document.title = '店长申请';
  }
}, { immediate: true });

/** 监听 */
watch(() => props.state, async (newVal) => {
  if (newVal || isNUllString(newVal)) {
    model.value.higherStructureId = isNUllString(newVal) ? null : newVal;
    try {
      let _params = {
        structureId: isNUllString(newVal) ? null : newVal,
        applyType: isArray(applyTypeRef.value) ? applyTypeRef.value[0] : applyTypeRef.value
      };
      const resp = await getApplicationRecord(_params);
      if (resp) {
        const { higherStructureName, structureApplyDTO, isBindStore } = resp;
        // 判断是否已绑定店铺
        if (isBindStore) {
          exceptionCodeRef.value = "5006";
          isShowException.value = true;
          return;
        }
        model.value.higherStructureName = higherStructureName ?? '';
        if (isObject(structureApplyDTO)) {
          // 审核中
          if (structureApplyDTO?.auditStatus == OrgApplyStatusEnum.PENDING) {
            exceptionCodeRef.value = "5002";
            isShowException.value = true;
          }
          // 审核通过
          if (structureApplyDTO?.auditStatus == OrgApplyStatusEnum.APPROVED) {
            exceptionCodeRef.value = "5003";
            isShowException.value = true;
            
          }
          // 审核不通过
          if (structureApplyDTO?.auditStatus == OrgApplyStatusEnum.REJECTED) {
            exceptionCodeRef.value = "5004";
            isShowException.value = true;
          }
          const { 
            applyCsName, 
            applyCsPhone, 
            applyStructureName, 
            province, 
            provinceId, 
            city, 
            cityId,
            district,
            districtId,
            town,
            townId,
            addressDetail,
            applyRemark
          } = structureApplyDTO;
          Object.assign(model.value, {
            applyCsName, 
            applyCsPhone, 
            applyStructureName, 
            province, 
            provinceId, 
            city, 
            cityId,
            district,
            districtId,
            town,
            townId,
            addressDetail,
            applyRemark
          });
        }
      }
    } catch (error) {
      exceptionCodeRef.value = "5005";
      exceptionCodeTextRef.value = error;
      isShowException.value = true;
    }
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.store_signup_wrapper {
  width: 100%;
  height: 100vh;
  background: #F8F8F8;
  .store_signup_container {
    width: 100%;
    height: 100%;
    background: #F8F8F8;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 12px;
    }
    .field_item_wrapper {
      display: flex;
      align-items: center;
      height: 52px;
      border-bottom: 1px solid #EEEEEE;
      .label_title {
        width: 80px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .field {
        flex: 1;
        padding: 0;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
      .address_wrapper {
        display: flex;
        align-items: center;
      }
    }
    .footer {
      box-sizing: border-box;
      :deep(.van-button__text) {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
