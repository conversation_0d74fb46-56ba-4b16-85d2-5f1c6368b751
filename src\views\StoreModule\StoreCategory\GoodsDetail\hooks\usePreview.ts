import { ref, reactive, watch, computed, type Ref } from "vue";
import { MediaTypeEnum } from "@/enums/storeGoods";
import type { SwipeInstance } from "vant";
import { isArray, isNullOrUnDef } from "@/utils/isUtils";
type MediaType = 0 | 1;
interface SwiperReactive {
  currentIndex: number;
  type: MediaType;
}
export default function usePreview(productImgDTOList: Ref<any[]>) {
  const showPreview = ref<boolean>(false);
  const previewList = ref<any[]>([]);
  //视频组件实例
  const goodsVideoRef = ref(null);
  //轮播图实例
  const swiperRef = ref<SwipeInstance>(null)
  const videoPlayStatus = reactive({
    currentTime: 0,
  });
  const curSwiperStatus = reactive<SwiperReactive>({
    currentIndex: 0,
    type: 0,
  });
  const videoIndexMap = ref<Record<number | string, number>>({
    0: 0,
  });
  const curVideoIndex = computed(() => {
    return videoIndexMap.value[curSwiperStatus.currentIndex];
  });
  const pauseAllVideo = () => {
    if (goodsVideoRef.value && isArray(goodsVideoRef.value)) {
      goodsVideoRef.value.forEach((item: any) => {
        item.pause();
      });
    }
  };
  const playVideo = (index: number) => {
    goodsVideoRef.value && goodsVideoRef.value[index].play();
  }
  const handlePreview = (index: number, type: MediaType) => {
    if (type == MediaTypeEnum.Video) {
      //获取视频当前时间
      videoPlayStatus.currentTime = goodsVideoRef.value
        ? goodsVideoRef.value[curVideoIndex.value].getCurrentTime()
        : 0;
    }
    handlePreViewMedia({
      currentIndex: index,
      type,
    });
    if (curSwiperStatus.type == MediaTypeEnum.Video) {
      goodsVideoRef.value && goodsVideoRef.value[curVideoIndex.value].init();
    }
  };
  const handlePreViewMedia = ({ currentIndex, type }: SwiperReactive) => {
    previewList.value = productImgDTOList.value;
    curSwiperStatus.currentIndex = currentIndex;
    curSwiperStatus.type = type;
    showPreview.value = true;
  };
  const handleSwiperChange = (index:number) => {
    const curInfo = productImgDTOList.value[curSwiperStatus.currentIndex];
    curSwiperStatus.currentIndex = index;
    curSwiperStatus.type = curInfo.type;
    if (isNullOrUnDef(curVideoIndex.value)) {
      pauseAllVideo();
    }
  };
  const closePreview = (params) => {
    //自动滚到到指定位置
    swiperRef.value && swiperRef.value.swipeTo(curSwiperStatus.currentIndex);
  };
  watch(
    () => productImgDTOList.value,
    (val) => {
      //设置视频映射索引
      let i = 0;
      val.forEach((item, index) => {
        if (item.type == MediaTypeEnum.Video) {
          videoIndexMap.value[index] = i;
          i++;
        }
      });
    },
    {
      deep: true,
    }
  );
  return {
    previewList,
    showPreview,
    curSwiperStatus,
    videoPlayStatus,
    goodsVideoRef,
    playVideo,
    handlePreViewMedia,
    handleSwiperChange,
    handlePreview,
    closePreview,
    swiperRef,
  };
}
