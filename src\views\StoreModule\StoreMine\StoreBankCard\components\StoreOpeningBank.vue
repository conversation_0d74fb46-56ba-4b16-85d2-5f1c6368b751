<template>
  <VanPopup v-model:show="showRef" round position="bottom" @close="emit('update:show', false)" style="height: 60vh;">
    <div class="store_opening_bank_wrapper">
      <!-- header -->
      <div class="header">
        <div
          v-for="item in selectOptions"
          :key="item.type"
          class="select_title"
          :class="{ 'select_active': item.type == currentSelectType }"
          @click="handleSelect(item)"
        >
          <span class="van-ellipsis">{{ item.title }}</span>
          <VanIcon
            name="arrow-down"
            style="font-size: 12px;"
            :class="{'rotate-180': item.type == currentSelectType }"
          />
        </div>
      </div>
      <!-- 内容 -->
      <div class="select_content">
        <template v-if="currentOptions.length > 0">
          <div v-for="item in currentOptions" :key="item['id']" class="select_item" @click="handleSelectItem(item)">
            <span class="van-ellipsis">{{ item['name'] }}</span>
            <VanIcon v-if="currentSelectId == item['id']" name="success" style="color: #EF1115;" />
          </div>
          <VanLoading v-if="isPageLoadingRef" size="24px" vertical>加载中...</VanLoading>
        </template>
        <template v-else>
          <div class="select_empty">暂无数据</div>
        </template>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { getAddress, storeBankListApi } from "@/services/storeApi";
/**  相关组件 */
import { isArray } from "@/utils/isUtils";

defineOptions({ name: "StoreOpeningBank" });

/** 选择类型 */
const enum SelectTypeEnum {
  /** 省份 */
  PROVINCE = "province",
  /** 城市 */
  CITY = "city",
  /** 银行 */
  BANK = "bank",
  /** 开户行 */
  OPENING_BANK = "opening_bank",
}

/** 检索开户行 */
const enum SearchOpeningBankEnum {
  /** 开户银行 */
  OPENING_BANK = 1,
  /** 开户行支行 */
  OPENING_BANK_BRANCH = 2,
}

/** props */
const props = withDefaults(defineProps<{
  show: boolean;
}>(), {
});

/** emit */
const emit = defineEmits<{
  (e: 'update:show', show: boolean): void;
  (e: 'select', data: {
    id: string;
    name: string;
    no: string;
  }): void;
}>();

const showRef = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

const isPageLoadingRef = ref(false);

/** 当前选中 */
const currentSelectType = ref(SelectTypeEnum.PROVINCE);

/** 当前列表 */
const currentOptions = computed(() => {
  return selectOptions.value.find(item => item.type === currentSelectType.value).optionsList;
});

/** 当前选中项 */
const currentSelect = ref([
{
    type: SelectTypeEnum.PROVINCE,
    currentSelected: {},
  },
  {
    type: SelectTypeEnum.CITY,
    currentSelected: {},
  },
  {
    type: SelectTypeEnum.BANK,
    currentSelected: {},
  },
  {
    type: SelectTypeEnum.OPENING_BANK,
    currentSelected: {},
  },
]);

/** selectOptions */
const selectOptions = ref([
  {
    type: SelectTypeEnum.PROVINCE,
    title: "请选择",
    optionsList: [],
  },
  {
    type: SelectTypeEnum.CITY,
    title: "请选择",
    optionsList: [],
  },
  {
    type: SelectTypeEnum.BANK,
    title: "请选择",
    optionsList: [],
  },
  {
    type: SelectTypeEnum.OPENING_BANK,
    title: "请选择",
    optionsList: [],
  },
]);

/** 点击头部选择类型 */
function handleSelect(item) {
  currentSelectType.value = item.type;
}

/** 点击选项 */
async function handleSelectItem(selectItem) {
  let tempCurrentSelect = currentSelect.value.find(item => item.type === selectItem?.type);
  let tempSelectOptions = selectOptions.value.find(item => item.type === selectItem?.type);
  if (tempCurrentSelect) {
    tempCurrentSelect.currentSelected = selectItem;
  }
  if (tempSelectOptions) {
    tempSelectOptions.title = selectItem.name;
  }

  // 点击省份
  if (selectItem.type === SelectTypeEnum.PROVINCE) {
    currentSelectType.value = SelectTypeEnum.CITY;
    let tempCurrentSelect = currentSelect.value.find(item => item.type === SelectTypeEnum.CITY);
    let tempSelectOptions = selectOptions.value.find(item => item.type === SelectTypeEnum.CITY);
    if (tempCurrentSelect) {
      tempCurrentSelect.currentSelected = {}; // 清空当前选中
    }
    if (tempSelectOptions) {
      tempSelectOptions.title = "请选择";
    }
    await getCityList(selectItem['code']);
  }

  // 点击城市
  if (selectItem.type === SelectTypeEnum.CITY) {
    currentSelectType.value = SelectTypeEnum.BANK;

    let tempCurrentSelect = currentSelect.value.find(item => item.type === SelectTypeEnum.BANK);
    let tempSelectOptions = selectOptions.value.find(item => item.type === SelectTypeEnum.BANK);
    if (tempCurrentSelect) {
      tempCurrentSelect.currentSelected = {}; // 清空当前选中
    }
    if (tempSelectOptions) {
      tempSelectOptions.title = "请选择";
    }

    await getBankList();
  }

  // 点击银行
  if (selectItem.type === SelectTypeEnum.BANK) {
    currentSelectType.value = SelectTypeEnum.OPENING_BANK;

    let tempCurrentSelect = currentSelect.value.find(item => item.type === SelectTypeEnum.OPENING_BANK);
    let tempSelectOptions = selectOptions.value.find(item => item.type === SelectTypeEnum.OPENING_BANK);
    if (tempCurrentSelect) {
      tempCurrentSelect.currentSelected = {}; // 清空当前选中
    }
    if (tempSelectOptions) {
      tempSelectOptions.title = "请选择";
    }

    // 省份id
    let provinceId = currentSelect.value.find(item => item.type === SelectTypeEnum.PROVINCE)?.currentSelected['id'] ?? null;
    // 城市id
    let cityId = currentSelect.value.find(item => item.type === SelectTypeEnum.CITY)?.currentSelected['id'] ?? null;
    // 父级银行ID
    let parentId = currentSelect.value.find(item => item.type === SelectTypeEnum.BANK)?.currentSelected['id'] ?? null;

    await getOpeningBankList(parentId, provinceId, cityId);

  }

  // 点击开户支行
  if (selectItem.type === SelectTypeEnum.OPENING_BANK) {
    emit('select', {
      id: selectItem['id'],
      name: selectItem['name'],
      no: selectItem['no'],
    });


    emit("update:show", false);
  }
}

/** 当前选中的selectId */
const currentSelectId = computed(() => {
  let tempCurrentSelect = currentSelect.value.find(item => item.type === currentSelectType.value);
  return tempCurrentSelect?.currentSelected['id'];
});

/** 获取开户支行 */
async function getOpeningBankList(parentId?: string, provinceId?: string, cityId?: string) {
  try {
    isPageLoadingRef.value = true;
    const _params = {
      data: {
        cateType: SearchOpeningBankEnum.OPENING_BANK_BRANCH,
        parentId,
        provinceId,
        cityId
      },
      pageVO: {
        current: 1,
        size: 1000,
      }
    }
    const { records } = await storeBankListApi(_params);
    if (records && isArray(records)) {
      const openingBankList = records.map(item => {
        return {
          id: item.id,
          name: item.name,
          no: item.no,
          type: SelectTypeEnum.OPENING_BANK,
        }
      });
      let tempSelectOptions = selectOptions.value.find(item => item.type === SelectTypeEnum.OPENING_BANK);
      if (tempSelectOptions) {
        tempSelectOptions.optionsList = openingBankList;
      }
    }
  } catch (error) {
    console.log("获取开户支行：", error);
  } finally {
    isPageLoadingRef.value = false;
  }
}

/** 获取开户银行 */
async function getBankList() {
  try {
    isPageLoadingRef.value = true;
    const _params = {
      data: {
        cateType: SearchOpeningBankEnum.OPENING_BANK,
      },
      pageVO: {
        current: 1,
        size: 1000,
      }
    }
    const { records } = await storeBankListApi(_params);
    if (records && isArray(records)) {
      const bankList = records.map(item => {
        return {
          id: item.id,
          name: item.name,
          type: SelectTypeEnum.BANK,
        }
      });
      let tempSelectOptions = selectOptions.value.find(item => item.type === SelectTypeEnum.BANK);
      if (tempSelectOptions) {
        tempSelectOptions.optionsList = bankList;
      }
    }
  } catch (error) {
    console.log("获取开户银行: ", error);
  } finally {
    isPageLoadingRef.value = false;
  }
}

/** 获取城市信息 */
async function getCityList(parentCode: string) {
  try {
    isPageLoadingRef.value = true;
    const res = await getAddress({
      parentCode,
      cateType: 2,
    });
    if (res && isArray(res)) {
      const cityOptions = res.map((item) => {
        const { code, name, id } = item;
        return {
          code,
          name,
          id,
          type: SelectTypeEnum.CITY,
        };
      });
      let tempSelectOptions = selectOptions.value.find(item => item.type === SelectTypeEnum.CITY);
      if (tempSelectOptions) {
        tempSelectOptions.optionsList = cityOptions;
      }
    }
    isPageLoadingRef.value = false;
  } catch (error) {
    console.log("获取城市信息失败：" + error);
    isPageLoadingRef.value = false;
  }
}

/** 获取省份信息 */
async function getProvince() {
  try {
    isPageLoadingRef.value = true;
    const res = await getAddress({
      parentCode: "0",
      cateType: 1,
    });
    if (res && isArray(res)) {
      const provinceOptions = res.map((item) => {
        const { code, name, id } = item;
        return {
          code,
          name,
          id,
          type: SelectTypeEnum.PROVINCE,
        };
      });
      let temp = selectOptions.value.find((item) => item.type === SelectTypeEnum.PROVINCE);
      temp.optionsList = provinceOptions;
    }
    isPageLoadingRef.value = false;
  } catch (error) {
    console.log("获取省份信息失败：" + error);
    isPageLoadingRef.value = false;
  }
}

getProvince();
</script>

<style lang="less" scoped>
.store_opening_bank_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .header {
    height: 50px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0px 12px;
    box-sizing: border-box;
    .select_title {
      min-width: 23%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 0.9rem;
      color: #333333;
      line-height: 24px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      transition: all 0.3s;
    }
    .select_active {
      color: #EF1115 !important;
    }
    .rotate-180 {
      transform: rotate(180deg);
      transition: transform 0.3s ease;
    }
  }
  .select_content {
    flex: 1;
    overflow-y: auto;
    padding: 8px 16px;
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;
    .select_item {
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0px 8px;
      span {
        flex: 1;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      &:hover {
        background: #F5F5F5;
        border-radius: 4px;
      }
    }
    .select_empty {
      width: 100%;
      height: 100%;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
