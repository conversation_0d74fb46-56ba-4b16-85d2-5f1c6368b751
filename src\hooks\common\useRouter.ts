import { navigateTo, reLaunch } from '@/routes/utils/navigateUtils'
import { RoutesName } from '@/routes/enums/routeNameEnum'

export type JunmpKeyType = 'Login' | 'MyOrders' | 'StoreDetail'
export type JunmpSwitchKeyType = 'Home' | 'IntegralHome' | 'Cart' | 'User' | 'Therapy' | 'Cate'

/**
 * @description 切换页面
 */
export default function useRouter() {
  /** 路由字典 */
  const RouteNameMap = {
    Login: RoutesName.StoreLogin,
    MyOrders: RoutesName.StoreMyOrders,
    StoreDetail: RoutesName.StoreDetail,
  }

  /** tabBar 页面 */
  const tabBarSwitchMap = {
    Home: RoutesName.StoreHome,
  }

  /**
   * @description 跳转页面(保留当前页面，跳转到应用内的某个页面)
   * @param name 页面名称
   * @param props 页面参数
   */
  function routerPushByKey(type: JunmpKeyType, props?: Record<string, string | number>) {
    navigateTo({
      url: RouteNameMap[type],
      props,
    })
  }

  /**
   * @description 跳转页面(关闭所有页面，打开到应用内的某个页面)
   */
  function redirectToUrl(
    type: JunmpKeyType | JunmpSwitchKeyType,
    props?: Record<string, string | number>,
  ) {
    const url = RouteNameMap[type] || tabBarSwitchMap[type]
    reLaunch({
      url,
      props,
    })
  }

  /**
   * @description 跳转页面(先判断是否已经登录，未登录则跳转到登录页,保留当前页面，跳转到应用内的某个页面)
   */
  function routerPushByKeyWithLoginCheck(
    isLogin: boolean,
    type: JunmpKeyType,
    props?: Record<string, string | number>,
  ) {
    if (!isLogin) {
      routerPushByKey('Login')
      return
    }
    routerPushByKey(type, props)
  }

  return {
    routerPushByKey,
    redirectToUrl,
    routerPushByKeyWithLoginCheck,
  }
}
