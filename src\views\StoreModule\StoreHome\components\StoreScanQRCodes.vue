<template>
  <div class="store-scan-QrCodes">
    <div class="store-scan-QrCodes__item" v-for="item in qrCodeOptions" :key="item.title" @click="handleClick(item.type)">
        <img :src="item.icon" alt="" :class="{ 'img_small': isSamllMobile, 'img_default': !isSamllMobile }" />
        <span :class="{ 'title_small': isSamllMobile, 'title_default': !isSamllMobile }">{{ item.title }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { QRCodeTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import pointsCode from "@/assets/storeImage/storeHome/pointsCode.png";
import couponCode from "@/assets/storeImage/storeHome/couponCode.png";
import durationCode from "@/assets/storeImage/storeHome/durationCode.png";
import pickupCode from "@/assets/storeImage/storeHome/pickupCode.png";

defineOptions({ name: 'StoreScanQRCodes' });

/** emit */
const emit = defineEmits<{
    (e: 'onScan', type: QRCodeTypeEnum): void;
}>();

/** QRCode */
const qrCodeOptions = [
    {
        title: '提货码',
        icon: pickupCode,
        type: QRCodeTypeEnum.PICK_UP_CODE
    },
    {
        title: '时长码',
        icon: durationCode,
        type: QRCodeTypeEnum.TIME_CODE
    },
     {
        title: '福利券码',
        icon: couponCode,
        type: QRCodeTypeEnum.COUPON_CODE
    },
    {
        title: '积分码',
        icon: pointsCode,
        type: QRCodeTypeEnum.INTEGRAL_CODE
    },
];

function handleClick(type: QRCodeTypeEnum) {
    emit('onScan', type);
}

const isSamllMobile = computed(() => {
  if (window.innerWidth <= 320) {
    return true;
  }
  return false;
});
</script>

<style lang="less" scoped>
.store-scan-QrCodes {
    height: 118px;
    border-radius: 12px;
    padding: 16px 18px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    .store-scan-QrCodes__item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        .img_small {
            width: 48px;
            height: 48px;
        }
        .title_small {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 22px;
            text-align: center;
            font-style: normal;
            text-transform: none;
            margin-top: -4px;
        }
        .img_default {
            width: 64px;
            height: 64px;
        }
        .title_default {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
            text-align: center;
            font-style: normal;
            text-transform: none;
            margin-top: -4px;
        }
    }
}
</style>
