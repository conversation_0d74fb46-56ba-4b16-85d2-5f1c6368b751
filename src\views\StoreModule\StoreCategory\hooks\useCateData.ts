import { ref, reactive, watch } from "vue";
import type { Ref } from "vue";
import allCateIcon from "@/assets/storeImage/product/allCate.png";
import { queryCategoryList } from "@/services/storeApi/store-category";
import { isNullOrUnDef } from "@/utils/isUtils";
import { useMessages } from "@/hooks/useMessage";
interface CateProps {
  isAll?: boolean;
  spliceNum?: number;
  isFilterShow?: boolean;
  modal: Ref<any>;
  callback?: () => void;
}
interface ReactiveInfo {
  patentId: string;
  childId: string;
  parentName: string;
}
export default function (props: CateProps) {
  const message = useMessages();
  const { modal, isAll, isFilterShow, spliceNum, callback } = props;
  const cateList = ref<any[]>([]);
  const tempCateInfo = reactive<ReactiveInfo>({
    patentId: "",
    childId: "",
    parentName: "全部商品",
  });
  const cateLoading = ref<boolean>(false);
  //二级分类
  const curChildrenCate = ref<any[]>([]);
  const getCateList = async (callback?: () => void) => {
    try {
      const params: any = {
        data: {
          type: modal.value.type,
        },
      };
      cateLoading.value = true;
      let res = await queryCategoryList(params);
      res = res.map((item: any) => {
        return {
          ...item,
          parentId: item.id,
        };
      });
      if (isFilterShow) {
        //扁平化数据
        let flatArr = res.reduce((pre, item, index) => {
          pre.push(item);
          if (item.children && item.children.length) {
            pre = [...pre, ...item.children];
          }
          return pre;
        }, []);
        flatArr.sort((a, b) => b.sort - a.sort);
        flatArr = flatArr.filter((item: any) => item.isShow == 1);
        if (spliceNum) {
          res = flatArr.splice(0, spliceNum);
        }
      }
      cateList.value = res;
      if (isAll) {
        cateList.value.unshift({
          id: "",
          name: "全部商品",
        });
      }
      callback && callback();
    } catch (e) {
      message.createMessageError(`获取失败：${e}`);
    } finally {
      cateLoading.value = false;
    }
  };
  function initCateInfo() {
    getCateList(() => {
      const info = cateList.value.find(item => item.id === tempCateInfo.patentId) || {};
      tempCateInfo.parentName = info.name;
      //获取二级分类
      if (info.children && info.children.length) {
        //获取二级分类
        curChildrenCate.value = [
          {
            id: tempCateInfo.patentId,
            name: "全部商品",
            iconPath: allCateIcon,
          },
          ...(info.children || []),
        ];
      } else {
        curChildrenCate.value = [];
      }
      //添加参数id
      modal.value.cateId = tempCateInfo.childId;
      callback && callback();
    });
  }
  const handleParentClick = (id: string) => {
    const info = cateList.value.find(item => item.id === id) || {};
    tempCateInfo.parentName = info.name;
    tempCateInfo.patentId = id;
    if (info.children && info.children.length) {
      //获取二级分类
      curChildrenCate.value = [
        {
          id,
          name: "全部商品",
          iconPath: allCateIcon,
        },
        ...(info.children || []),
      ];
    } else {
      curChildrenCate.value = [];
    }
    tempCateInfo.childId = id;
    loadData();
  };
  const handleChildClick = (id: string) => {
    tempCateInfo.childId = id;
    loadData();
  };
  function loadData() {
    modal.value.cateId = tempCateInfo.childId;
    callback && callback();
  }
  return {
    cateList,
    curChildrenCate,
    getCateList,
    handleChildClick,
    handleParentClick,
    tempCateInfo,
    initCateInfo,
    cateLoading,
  };
}
