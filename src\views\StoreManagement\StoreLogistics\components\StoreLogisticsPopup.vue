<template>
  <van-popup :show="props.show" teleport="body" position="bottom" round closeable close-icon="close"
    @click-close-icon="handleClose" @click-overlay="handleClose" style="height: auto" safe-area-inset-bottom>
    <JLoadingWrapper :show="isPageLoadingRef">
      <div class="search-popup-content">
        <div style="text-align: center;margin-bottom: 15px;font-weight: 600;">物流轨迹</div>
        <div class="logClass">
          <van-tabs v-model:active="active" shrink @change="onClickGroupTab">
            <van-tab v-for="panel in logisticsCode" :title="panel.trackingNo">
              <div v-if="!isNoLog">
                <van-steps direction="vertical" :active="0">
                  <van-step v-for="item in logisticsInfo">
                    <h3>{{ ExpressStatusText[item.state] }}&nbsp;&nbsp;{{ item.acceptTime }}</h3>
                    <p>{{ item.acceptStation }}</p>
                  </van-step>
                </van-steps>
              </div>
              <div v-else v-for="item in noLogList" class="noLogListStyle">
                <div class="shipCompanyName">{{ item.shipCompanyName }}</div>
                <div class="code">
                  快递单号：{{ item.trackingNo }}
                  <div class="copyBtn" @click="(e) => handleCopy(e, item.trackingNo)">
                    <van-image round width="15" height="15" fit="contain" :src="copyBtn" />
                  </div>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </JLoadingWrapper>
  </van-popup>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getLogisticTraces } from "@/services/storeApi/store-logistics";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue"
import { useMessages } from "@/hooks/useMessage"
import copyBtn from "@/assets/storeImage/storeMine/copyRed.png";
import { copyText } from "@/utils/clipboardUtils";
import { ExpressStatusText } from "../type"
const { createMessageError, createMessageSuccess } = useMessages()
const props = withDefaults(defineProps<{ show: boolean, orderId: string }>(), {
  show: false,
  orderId: ''
});
const noLogList = ref([])
const logisticsInfo = ref([])
const logisticsCode = ref([])
const active = ref(0)
// const statusLable = [
//   '',
//   '已揽收',
//   '运输中',
//   '已签收',
//   '问题件',
//   '转寄',
//   '清关'
// ]
const emits = defineEmits<{
  (e: "update:show", val: boolean): void;
  (e: "close"): void;
  (e: "update:value", val: any): void;
}>();
const handleClose = () => {
  emits("update:show", false);
};
const isPageLoadingRef = ref(false)
onMounted(() => {
  let params = {
    id: props.orderId
  }
  getLogTraces(params, true)

})
const isNoLog = ref(false)
const getLogTraces = async (params, flag) => {
  logisticsInfo.value = []
  isPageLoadingRef.value = true
  try {
    let res = await getLogisticTraces(params)
    if (res.isMoreNumbers) {
      active.value = null
    } else {
      // active.value = 0
    }
    if (flag) {
      logisticsCode.value = res.logisticsNumbers.map(trackingNo => ({ trackingNo }));
    }
    if (res?.queryState) {
      isNoLog.value = false
      logisticsInfo.value = res.traces
    } else {
      isNoLog.value = true
      noLogList.value = res.traces
    }
  } catch (error) {
    createMessageError(error)
    isPageLoadingRef.value = false
  } finally{
    isPageLoadingRef.value = false
  }
}
const onClickGroupTab = (val, title) => {
  let params = {
    id: props.orderId,
    logisticsNumber: title
  }
  getLogTraces(params, false)
}
// 复制
function handleCopy(e, con) {
  e.stopPropagation()
  try {
    copyText(con)
    createMessageSuccess('复制ID成功')
  }
  catch (e) {
    createMessageError('复制ID失败')
  }
}
</script>
<style scoped lang="less">
.search-popup-content {
  height: 350px;
  padding: 20px 5px 0px 5px;
  // margin-top: 20px;
}

.logClass {
  height: calc(100% - 30px);
  overflow: hidden;
  overflow-y: scroll;
}

:deeo(.van-tabs) {
  height: 100% !important;
}

:deep(.van-tab--active) {
  color: #EF1115;
}

:deep(.van-tabs__line) {
  background: #EF1115;
}

:deep(.van-step__title--active) {
  color: #EF1115;
}

:deep(.van-step__icon--active) {
  color: #EF1115;
}

.noLogListStyle {
  width: 100%;
  height: 100%;
  padding: 15px 0px 0px 15px;

  .shipCompanyName {
    font-weight: 600;
  }
  div {
    margin-bottom: 10px;
  }
  .code {
    display: flex;
  }
}
</style>
