"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prodConfig = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
const maps_1 = require("@/routes/maps");
exports.prodConfig = {
    "pages": [
        maps_1.routesMap[routeNameEnum_1.RoutesName.StoreHome],
        // routesMap[RoutesName.StoreLogin],
        maps_1.routesMap[routeNameEnum_1.RoutesName.StoreCategory],
        maps_1.routesMap[routeNameEnum_1.RoutesName.StoreMine],
        maps_1.routesMap[routeNameEnum_1.RoutesName.StoreMyOrders],
        maps_1.routesMap[routeNameEnum_1.RoutesName.StoreDetail],
    ],
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "微医馆",
        "navigationBarBackgroundColor": "#F8F8F8",
        "backgroundColor": "#F8F8F8",
        "usingComponents": {
            "mp-html": "/wxcomponents/mp-html/index",
            "van-icon": "/wxcomponents/vant/icon/index",
            "van-row": "/wxcomponents/vant/row/index",
            "van-col": "/wxcomponents/vant/col/index",
            "van-button": "/wxcomponents/vant/button/index",
            "van-field": "/wxcomponents/vant/field/index",
            "van-search": "/wxcomponents/vant/search/index",
            "van-tab": "/wxcomponents/vant/tab/index",
            "van-tabs": "/wxcomponents/vant/tabs/index",
            "van-cell": "/wxcomponents/vant/cell/index",
            "van-cell-group": "/wxcomponents/vant/cell-group/index",
            "van-divider": "/wxcomponents/vant/divider/index",
            "van-tag": "/wxcomponents/vant/tag/index",
            "van-loading": "/wxcomponents/vant/loading/index",
            "van-empty": "/wxcomponents/vant/empty/index",
            "van-notify": "/wxcomponents/vant/notify/index",
            "van-dropdown-menu": "/wxcomponents/vant/dropdown-menu/index",
            "van-dropdown-item": "/wxcomponents/vant/dropdown-item/index",
            "van-transition": "/wxcomponents/vant/transition/index",
            "van-sidebar": "/wxcomponents/vant/sidebar/index",
            "van-sidebar-item": "/wxcomponents/vant/sidebar-item/index",
            "van-switch": "/wxcomponents/vant/switch/index",
            "van-action-sheet": "/wxcomponents/vant/action-sheet/index",
            "van-popup": "/wxcomponents/vant/popup/index",
            "van-picker": "/wxcomponents/vant/picker/index",
            "van-checkbox": "/wxcomponents/vant/checkbox/index",
            "van-checkbox-group": "/wxcomponents/vant/checkbox-group/index",
            "van-slider": "/wxcomponents/vant/slider/index",
            "van-image": "/wxcomponents/vant/image/index",
            "van-overlay": "/wxcomponents/vant/overlay/index",
            "van-toast": "/wxcomponents/vant/toast/index",
            "van-card": "/wxcomponents/vant/card/index",
            "van-uploader": "/wxcomponents/vant/uploader/index",
            "van-dialog": "/wxcomponents/vant/dialog/index",
            "van-stepper": "/wxcomponents/vant/stepper/index",
            "van-cascader": "/wxcomponents/vant/cascader/index",
            "van-radio": "/wxcomponents/vant/radio/index",
            "van-radio-group": "/wxcomponents/vant/radio-group/index",
            "van-swipe-cell": "/wxcomponents/vant/swipe-cell/index",
            "van-steps": "/wxcomponents/vant/steps/index",
            "van-nav-bar": "/wxcomponents/vant/nav-bar/index",
            "van-datetime-picker": "/wxcomponents/vant/datetime-picker/index",
            "van-count-down": "/wxcomponents/vant/count-down/index",
            "van-progress": "/wxcomponents/vant/progress/index",
            "van-config-provider": "/wxcomponents/vant/config-provider/index",
            "van-grid": "/wxcomponents/vant/grid/index",
            "van-grid-item": "/wxcomponents/vant/grid-item/index",
        }
    },
    "tabBar": {
        "custom": true,
        "selectedColor": "#333333",
        "list": [
            {
                "pagePath": maps_1.routesMap[routeNameEnum_1.RoutesName.StoreHome].path,
                "text": "首页",
                "iconPath": "static/images/storeTabbar/home.png",
                "selectedIconPath": "static/images/storeTabbar/home-active.png"
            },
            {
                "pagePath": maps_1.routesMap[routeNameEnum_1.RoutesName.StoreCategory].path,
                "text": "分类",
                "iconPath": "static/images/storeTabbar/cate.png",
                "selectedIconPath": "static/images/storeTabbar/cate-active.png"
            },
            {
                "pagePath": maps_1.routesMap[routeNameEnum_1.RoutesName.StoreMine].path,
                "text": "我的",
                "iconPath": "static/images/storeTabbar/mine.png",
                "selectedIconPath": "static/images/storeTabbar/mine-active.png"
            },
        ]
    }
};
