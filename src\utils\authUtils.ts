import { isAuthKeyType, type AuthKeyType } from "@/auth/authKeys";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { isArray } from "./isUtils";
export type AuthModeType = 'every' | 'some'
export function hasAuth(authKey:string | Array<string> | AuthKeyType):boolean{
    const userStore = useUserStoreWithoutSetup()
    const _authKeysList = userStore.optAuthKeys || [];
    if(isAuthKeyType(authKey)){
        return _authKeysList.includes(authKey.key)
    }
    else if(isArray(authKey)){
        let flag = true;
        for(let i =0;i<authKey.length;i++){
            const _key = authKey[i]
            if(!_authKeysList.includes(_key)){
                flag = false;
                break;
            }
        }
        return flag;
    }
    else{
        return _authKeysList.includes(authKey)
    }
}
