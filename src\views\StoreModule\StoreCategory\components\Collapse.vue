<template>
    <div class="collapse-box">
        <div class="collapse-header">
            <div class="header-left">
                <slot name="left">
                    {{ title }}
                </slot>
            </div>
            <div class="header-right" v-if="list.length > 3" @click="handleCollapse">
                <div class="right-text">{{ `${!isShow ? '展开' : '收起'}分类` }}</div>
                <van-icon :name="isShow ? 'arrow-up' : 'arrow-down'" size="12px" />
            </div>
        </div>
        <div class="collapse-content" v-if="list.length > 1" ref="productView" :style="customStyle">
            <div class="content-item" :class="{ active: cateId === item.id }" @click="handleClick(item.id)"
                v-for="item in list" :key="item.id">
                <img :src="item.iconPath" class="logo">
                <div class="content-item_text">{{ item.name }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, reactive,type StyleValue } from "vue";
interface Props {
    title: string;
    list: any[];
    cateId: string;
}
const props = withDefaults(defineProps<Props>(), {
    title: "全部",
    list: () => ([]),
    cateId: null,
});
const emits = defineEmits<{
    (e: 'update:cateId', cateId: string): void;

}>()
const productViewH = ref<number>(0);
const ProductDropH = ref<number>(0);
const row = ref<number>(1);
const column = ref<number>(3);
const isShow = ref(false);
const customStyle = computed<StyleValue>(() => {
    const statusH = isShow.value ? `${productViewH.value}px` : `${ProductDropH.value}px`
    return {
        height: props.list.length > row.value && productViewH.value ? statusH : 'auto',
    }
})
const handleCollapse = () => {
    isShow.value = !isShow.value;
};
const handleClick = (id: string) => {
    isShow.value = false
    emits('update:cateId', id)
}
const getViewH = async (selector: string): Promise<number> => {
    return new Promise<number>((resolve, reject) => {
        const el = document.querySelector(selector)
        if (!el) {
           return reject('获取高度失败')
        }
        const h = el.getBoundingClientRect().height
        resolve(h)
    })
}
watch(() => props.list, () => {
    console.log(props.list);
    isShow.value = false
    productViewH.value = 0
    ProductDropH.value = 0
    if (!props.list.length) {
        return
    }
    nextTick(async () => {
        try {
            const rows = Math.ceil(props.list.length / column.value)
            const childH = await getViewH('.collapse-content .content-item')
            productViewH.value = childH * rows + (10 * rows)
            ProductDropH.value = childH * row.value
        } catch (error) {
            console.log(error)
        }
    })
}, {
    deep: true,
    immediate: true
})
</script>

<style lang="less" scoped>
@import url('@/styles/storeVar.less');

.active {
    color: @error-color !important;
    border: 1px solid @error-color !important;
}

.collapse-box {
    width: 100%;
    margin-bottom: 16px;

    .collapse-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-left {
            font-size: 16px;
            font-weight: bold;
            margin-right: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .header-right {
            display: flex;
            align-items: center;
            cursor: pointer;

            .right-text {
                color: #666666;
                margin-right: 5px;
            }
        }
    }

    .collapse-content {
        display: grid;
        margin-top: 16px;
        grid-template-columns: repeat(3, 32%);
        gap: 5px;
        box-sizing: border-box;
        overflow: hidden;
        transition: all 0.3s;

        .content-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            padding: 6px;
            color: #333333;
            border-radius: 8px;
            border: 1px solid transparent;

            img {
                width: 36px;
                height: 36px;
                margin-bottom: 8px;
            }

            .content-item_text {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                max-width: 100%;
            }
        }
    }
}
</style>
