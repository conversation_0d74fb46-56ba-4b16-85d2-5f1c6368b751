/**
 * @description 深度克隆
 */
export function deepClone<T>(value: T): T {
  /** 空 */
  if (!value) return value
  /** 数组 */
  if (Array.isArray(value)) return value.map((item) => deepClone(item)) as unknown as T
  /** 日期 */
  if (value instanceof Date) return new Date(value) as unknown as T
  /** 普通对象 */
  if (typeof value === 'object') {
    return Object.fromEntries(
      Object.entries(value).map(([k, v]: [string, any]) => {
        return [k, deepClone(v)]
      }),
    ) as unknown as T
  }
  /** 基本类型 */
  return value
}

/**
 *
 * @param length `uuid` 长度
 * @param radix `uuid` 基数
 * @returns `uuid`
 */
export const uuid = (length = 16, radix = 62) => {
  // 定义可用的字符集，即 0-9, A-Z, a-z
  const availableChars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  // 定义存储随机字符串的数组
  const arr = []
  // 获取加密对象，兼容 IE11
  const cryptoObj = uni.getSystemInfoSync().platform === 'web' ? window.crypto : null
  let i = 0

  // 循环 length 次，生成随机字符，并添加到数组中
  for (i = 0; i < length; i++) {
    // 生成一个随机数
    const randomValues = new Uint32Array(1)

    if (cryptoObj) {
      cryptoObj.getRandomValues(randomValues)
    } else {
      // Fallback for environments without crypto support
      randomValues[0] = Math.floor(Math.random() * 0xffffffff)
    }

    // 根据随机数生成对应的字符，并添加到数组中
    const index = randomValues[0] % radix

    arr.push(availableChars[index])
  }

  // 将数组中的字符连接起来，返回最终的字符串
  return arr.join('')
}
