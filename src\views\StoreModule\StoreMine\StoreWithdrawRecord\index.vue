<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <div class="store_withdraw_record_wrapper">
      <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="store_withdraw_record_content">
        <template v-if="storeWithdrawRecordList.length">
          <VanList v-model:loading="isLoadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
            <StoreWithdrawRecordCard v-for="item in storeWithdrawRecordList" :key="item.id" :withdrawRecord="item" />
          </VanList>
        </template>
        <template v-else>
          <EmptyData style="min-height: 400px;" />
        </template>
      </VanPullRefresh>
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import useStoreWithdrawRecord from "./hooks/useStoreWithdrawRecord";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import StoreWithdrawRecordCard from "./components/StoreWithdrawRecordCard.vue";

defineOptions({ name: 'StoreWithdrawRecord' });

const {
  storeWithdrawRecordList,
  isLoadingRef,
  isFinishedRef,
  onLoad,
  onRefresh,
  refreshingRef,
  isPageLoadingRef,
  initStoreWithdrawRecordList
} = useStoreWithdrawRecord();

onMounted(() => {
  initStoreWithdrawRecordList();
});
</script>

<style lang="less" scoped>
.store_withdraw_record_wrapper {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #F8F8F8;
    .store_withdraw_record_content {
        width: 100%;
        height: 100%;
        padding: 12px 10px;
        box-sizing: border-box;
        overflow-y: auto;
    }
}
</style>
