<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh">
    <div class="store_order_detail_wrapper">
      <div class="store_order_detail_bg" :style="backgroundStyle"></div>
      <div class="store_order_detail_content" :class="className">
        <!-- 订单状态 -->
        <StoreOrderStatus :orderInfo="orderDetailRef" />
        <!-- 收货地址信息 -->
        <StoreDeliveryInfo v-if="isHomeDelivery" :addressInfo="orderDetailRef?.customerAddressDTO" />
        <!-- 发货信息 -->
        <StoreLogisticsInfo
          v-if="isHomeDelivery && orderDetailRef?.status == StoreOrderStatusEnum.WAIT_RECEIVE"
          :orderInfo="orderDetailRef"
        />
        <!-- 商品信息 -->
        <StoreCommodityInfo
          :orderInfo="orderDetailRef"
          @clickVerificationCodeStatus="handleClickVerificationCodeStatus"
        />
        <!-- 核销码 -->
        <StoreVerificationCode
          v-if="orderDetailRef?.status === StoreOrderStatusEnum.WAIT_SEND && props.routeType != StoreOrderDetailRouteTypeEnum.SCAN && isSelfPickUp"
          :orderInfo="orderDetailRef"
        />
        <!-- 门店地址 -->
        <StoreAddress v-if="isSelfPickUp" :storeEntity="orderDetailRef?.storeEntityDTO" />
        <!-- 支付信息 -->
        <StorePaymentInfo :orderInfo="orderDetailRef" />
        <!-- 订单信息 -->
        <StoreOrderInfo :orderInfo="orderDetailRef" />
      </div>
      <!-- 操作 -->
      <div class="footer">
        <!-- 待提货 -->
        <template
          v-if="[StoreOrderStatusEnum.WAIT_SEND].includes(orderDetailRef?.status) 
            && props.routeType == StoreOrderDetailRouteTypeEnum.SCAN 
            && isSelfPickUp 
            && !isAutoVerification
          "
        >
          <VanButton
            type="primary"
            size="small"
            block
            round
            style="width: 120px;margin: 8px 12px;"
            @click="handleWriteOffOrder"
          >
            确认核销
          </VanButton>
        </template>
        <!-- 待付款 -->
        <template
          v-if="[StoreOrderStatusEnum.WAIT_PAY].includes(orderDetailRef?.status) && props.routeType == StoreOrderDetailRouteTypeEnum.MY_ORDER"
        >
          <div class="btn-group">
            <!-- 取消订单 -->
            <div class="cancellation-order" @click.stop="handleCancelOrder(orderDetailRef?.code)">取消订单</div>
            <!-- 付款 -->
            <div class="payment-order" @click.stop="handlePayOrder">付款</div>
          </div>
        </template>
        <!-- 申请退款 (我的订单：待发货、待收货、已完成、不锁单、在线支付) -->
        <template v-if="isShowApplyRefund">
          <div class="btn-group">
            <!-- 申请退款 -->
            <div class="refund-order" @click.stop="handleApplyRefund(orderDetailRef?.code)">申请退款</div>
          </div>
        </template>
      </div>
    </div>
    <!-- 使用记录 -->
    <StoreCodeUsageRecords
      v-model:show="showCodeUsageRecords"
      :orderVerification="orderDetailRef?.orderVerificationDTO"
    />
    <!-- 核销订单 -->
    <WriteOffOrder v-model:show="isShowWriteOffOrderRef" @confirm="handleConfirmWriteOffOrder" />
    <!-- 取消订单 -->
    <CancelOrder v-model:show="CancelOrderRef" @confirm="handleConfirmCancelOrder" />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import useGetOrderDetail from "../hooks/useGetOrderDetail";
import { useMessages } from "@/hooks/useMessage";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import {
  StoreOrderStatusEnum,
  StoreOrderDetailRouteTypeEnum,
  StoreOrderTypeEnum,
  ProductPickupModeEnum,
  StoreOrderPayTypeEnum,
  StoreOrderIsLockEnum,
  OrderVerificationTypeEnum,
  CustomerRoleOperationEnum
} from "@/views/StoreModule/enums";
import { isArray, isIOSEnv } from "@/utils/isUtils";
import { RoutesName } from "@/enums/routes";
import { verifyOrder, cancelOrder } from "@/services/storeApi";
/** 静态资源 */
import OrderDetailBg from "@/assets/storeImage/storeHome/orderDetails/order_detail_bg.png";
import OrderDetailCancelledBg from "@/assets/storeImage/storeHome/orderDetails/order_detail_cancelled_bg.png";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreOrderStatus from "./components/StoreOrderStatus.vue";
import StoreCommodityInfo from "./components/StoreCommodityInfo.vue";
import StoreVerificationCode from "./components/StoreVerificationCode.vue";
import StoreAddress from "./components/StoreAddress.vue";
import StorePaymentInfo from "./components/StorePaymentInfo.vue";
import StoreOrderInfo from "./components/StoreOrderInfo.vue";
import StoreCodeUsageRecords from "./components/StoreCodeUsageRecords.vue";
import WriteOffOrder from "@/views/StoreModule/components/WriteOffOrder.vue";
import CancelOrder from "@/views/StoreModule/components/CancelOrder.vue";
import StoreDeliveryInfo from "./components/StoreDeliveryInfo.vue";
import StoreLogisticsInfo from "./components/StoreLogisticsInfo.vue";
import { isInFrame } from "@/utils/envUtils";
import { useRoute } from "vue-router";
import { MessageEventEnum, useWindowMessage } from "@/hooks/useWindowMessage";
const {sendMessageToWindows} = useWindowMessage()
defineOptions({ name: 'StoreOrderDetail' });
const route = useRoute()
/** props */
const props = defineProps<{
  routeType: StoreOrderDetailRouteTypeEnum;
  orderCode: string;
}>();

/** 订单详情 */
const { isPageLoadingRef, getOrderDetail, orderDetailRef } = useGetOrderDetail({
  orderCode: props.orderCode
});
const { createMessageError, createMessageSuccess } = useMessages();
const { routerPushByRouteName } = useRouterUtils();

const CancelOrderRef = ref(false);
const cancelOrderCodeRef = ref('');

const showCodeUsageRecords = ref(false);
const isShowWriteOffOrderRef = ref(false);

const handleWriteOffOrder = () => {
  isShowWriteOffOrderRef.value = true;
};

function handleClickVerificationCodeStatus() {
  showCodeUsageRecords.value = true;
}

function _getVerifyOrderParams() {
  const { code: orderCode, orderVerificationDTO: { code: verificationCode } } = orderDetailRef.value;

  return {
    code: orderCode,
    verifyCode: verificationCode
  }
}

/** 核销订单 */
 async function handleConfirmWriteOffOrder() {
  try {
    const _params = _getVerifyOrderParams();
    const resp = await verifyOrder(_params);
    if (resp) {
      createMessageSuccess("核销成功");
      getOrderDetail();
    }
  } catch (error) {
    createMessageError("核销失败：" + error);
  }
}

function handleCancelOrder(orderCode: string) {
  CancelOrderRef.value = true;
  cancelOrderCodeRef.value = orderCode;
}

/** 取消订单 */
async function handleConfirmCancelOrder() {
  try {
    const res = await cancelOrder(cancelOrderCodeRef.value);
    if (res) {
      createMessageSuccess("取消订单成功");
      CancelOrderRef.value = false;
      cancelOrderCodeRef.value = "";
      getOrderDetail();
    }
  } catch (error) {
    createMessageError("取消订单失败：" + error);
  }
}

/** 付款 */
function handlePayOrder() {
  const typeMap = {
    [StoreOrderTypeEnum.NORMAL]: StoreGoodsEnum.Goods,
    [StoreOrderTypeEnum.INTEGRAL]: StoreGoodsEnum.IntegralGoods,
    [StoreOrderTypeEnum.COUPON]: StoreGoodsEnum.WelfareTicket,
  };
  if(isInFrame()){
    const url = `${location.origin}/st/cashier?orderCode=${orderDetailRef.value?.code}&state=${route.query.state}&type=${typeMap[orderDetailRef.value?.type]}`
    if(isIOSEnv()){
        sendMessageToWindows(MessageEventEnum.sendUrlToWindow,url)
    }
    else{
        window.open(url)
    }
  }
  else{
    routerPushByRouteName(RoutesName.StoreCashier, {
      type: typeMap[orderDetailRef.value?.type],
      orderCode: orderDetailRef.value?.code,
    });
  }
  
}

/** 申请退款 */
function handleApplyRefund(orderCode: string) {
  routerPushByRouteName(RoutesName.StoreApplyRefund, {
    orderCode,
  });
}

/**
 * @description 计算属性
 */

/** 是否显示申请退款 */
const isShowApplyRefund = computed(() => {
  return [StoreOrderStatusEnum.WAIT_SEND, StoreOrderStatusEnum.WAIT_RECEIVE, StoreOrderStatusEnum.FINISHED].includes(orderDetailRef.value?.status) &&
  props.routeType == StoreOrderDetailRouteTypeEnum.MY_ORDER && 
  [StoreOrderPayTypeEnum.ONLINE].includes(orderDetailRef.value?.payType) &&
  isArray(orderDetailRef.value?.action) && 
  orderDetailRef.value?.action.includes(CustomerRoleOperationEnum.APPLY_FOR_REFUND);
});

/** 商品是否快递到家 */
const isHomeDelivery= computed(() => {
  return orderDetailRef.value?.pickupType == ProductPickupModeEnum.HOME_DELIVERY;
});

/** 商品是否自提 */
const isSelfPickUp = computed(() => {
  return orderDetailRef.value?.pickupType == ProductPickupModeEnum.STORE_PICKUP;
});

/** 是否显示底部按钮 */
const isShowBottomBtnRef = computed(() => {
  if ([StoreOrderStatusEnum.WAIT_SEND, StoreOrderStatusEnum.WAIT_PAY].includes(orderDetailRef.value?.status)) {
    return true;
  }
  return false;
});

/** 是否下单自动核销 */
const isAutoVerification = computed(() => {
  return orderDetailRef.value?.verificationType == OrderVerificationTypeEnum.AUTO_VERIFICATION;
});

/** 背景图 */
const backgroundStyle = computed(() => {
  const backgroundImage = orderDetailRef.value?.status === StoreOrderStatusEnum.CANCELLED
    ? OrderDetailCancelledBg
    : OrderDetailBg

  return {
    background: `url(${backgroundImage}) no-repeat`,
    backgroundSize: '100% 100%'
  }
})

/** 类名 */
const className = computed(() => {
  if (orderDetailRef.value?.status == StoreOrderStatusEnum.WAIT_SEND && props.routeType == StoreOrderDetailRouteTypeEnum.SCAN) {
    return "store_order_detail_content_h1";
  }
  return "store_order_detail_content_h2";
});

/** 组件挂载 */
onMounted(() => {
  if (!props.orderCode) {
    createMessageError("订单号不能为空");
    return;
  }
  getOrderDetail();
});
</script>

<style lang="less" scoped>
.store_order_detail_wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;

  .store_order_detail_bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 280px;
    z-index: 1;
  }

  .store_order_detail_content {
    width: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  .store_order_detail_content_h1 {
    height: calc(100vh - 48px);
  }
  .store_order_detail_content_h2 {
    height: 100%;
  }
  .footer {
    width: 100%;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    box-sizing: border-box;
    margin-bottom: env(safe-area-inset-bottom);

    .btn-group {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 8px 12px;
      .cancellation-order,
      .payment-order,
      .refund-order {
        min-height: 32px;
        height: 100%;
        border-radius: 999px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .cancellation-order {
        width: 80px;
        background: #ECF5FF;
        color: #4DA4FF;
      }
      .payment-order {
        width: 80px;
        background: #FFF4F4;
        color: #EF1115;
      }
      .refund-order {
        width: 120px;
        background: #EF1115;
        color: #FFFFFF;
      }
    }

    :deep(.van-button__text) {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 24px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
