<template>
  <div class="exception-wrapper">
    <img :src="exceptionInfo.imgSrc" alt="内容开发中，敬请期待" />
    <p class="title">{{ exceptionInfo.notice }}</p>
    <p v-if="exceptionInfo.subContent" class="sub-content">{{ exceptionInfo.subContent }}</p>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
/** 静态资源 */
import StayTunedSrc from '@/assets/storeImage/storeMine/StayTuned.png';

defineOptions({
  name: "StayTuned",
});

interface ExceptionInfo {
  imgSrc: string;
  notice: string;
  subContent?: string;
}

const props = withDefaults(defineProps<{
  code: string;
}>(), { code: "1001" });

/** 缺省信息 */
const exceptionMap = {
  "1001": {
    imgSrc: StayTunedSrc,
    notice: "内容开发中，敬请期待",
    subContent: ""
  }
} as Record<string, ExceptionInfo>;

// 计算属性获取异常信息
const exceptionInfo = computed(() => {
  return exceptionMap[props.code] || exceptionMap["1001"];
});
</script>

<style lang="less" scoped>
.exception-wrapper {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 48px;
  box-sizing: border-box;

  img {
    width: 200px;
    height: 200px;
  }

  .title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .sub-content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666;
    line-height: 20px;
    text-align: center;
    max-width: 80%;
  }
}
</style>
