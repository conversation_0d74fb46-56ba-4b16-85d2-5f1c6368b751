import { Cache_Key, StorageType } from "@/enums/cache";
import type { Cache } from "./type";

export const CacheConfig: Record<string, Cache> = {
  Token: {
    key: Cache_Key.Token,
    type: StorageType.LOCAL,
    expire: 60 * 60 * 24 * 15,
  },
  UserInfo: {
    key: Cache_Key.UserInfo,
    type: StorageType.SESSION,
  },
  RouteConfig: {
    key: Cache_Key.RouteConfig,
    type: StorageType.LOCAL,
    expire: 60 * 60 * 24 * 15,
  },
  System: {
    key: Cache_Key.System,
    type: StorageType.LOCAL,
  },
  AdvertiserInfo: {
    key: Cache_Key.AdvertiserInfo,
    type: StorageType.LOCAL,
  },
  State: {
    key: Cache_Key.State,
    type: StorageType.SESSION,
  },
  MemberInfo: {
    key: Cache_Key.MemberInfo,
    type: StorageType.SESSION,
  },
  VideoProcess: {
    key: Cache_Key.VideoProcess,
    type: StorageType.LOCAL,
    hasEncrypt: false,
  },
  VideoFinished: {
    key: Cache_Key.VideoFinished,
    type: StorageType.LOCAL,
    hasEncrypt: false,
  },
  LoginRetryTime: {
    key: Cache_Key.LoginRetryTime,
    type: StorageType.LOCAL,
  },
  UnResiterRetryTime: {
    key: Cache_Key.UnResiterRetryTime,
    type: StorageType.LOCAL,
  },
  Error: {
    key: Cache_Key.Error,
    type: StorageType.LOCAL,
  },
  CampConfig: {
    key: Cache_Key.Camp,
    type: StorageType.SESSION,
    hasEncrypt: false,
  },
  SystemVar: {
    key: Cache_Key.SystemVar,
    type: StorageType.SESSION,
    hasEncrypt: true,
  },
  TempInput: {
    key: Cache_Key.TempInput,
    type: StorageType.LOCAL,
  },
  StreamProcessSeconds: {
    key: Cache_Key.StreamProcessSeconds,
    type: StorageType.LOCAL,
    hasEncrypt: false,
  },
  IsAuthVaild: {
    key: Cache_Key.IsAuthVaild,
    type: StorageType.LOCAL,
    hasEncrypt: false,
    expire: 60 * 60 * 24 * 3,
  },
  StateCache: {
    key: Cache_Key.StateCache,
    type: StorageType.LOCAL,
    hasEncrypt: false,
    expire: 3 * 60,
  },
  IsWatched: {
    key: Cache_Key.StateCache,
    type: StorageType.LOCAL,
    hasEncrypt: false,
    expire: 60 * 60 * 24 * 2,
  },
  IsSkipAddCoursePlanNotice: {
    key: Cache_Key.IsSkipAddCoursePlanNotice,
    type: StorageType.LOCAL,
    hasEncrypt: false,
  },
  ApiPrefix: {
    key: Cache_Key.ApiPrefix,
    type: StorageType.SESSION,
    hasEncrypt: false,
  },
  FatherOrigin: {
    key: Cache_Key.FatherOrigin,
    type: StorageType.SESSION,
    hasEncrypt: false,
  },

  /** 门店 start */
  StoreToken: {
    key: Cache_Key.StoreToken,
    type: StorageType.LOCAL,
    hasEncrypt: false,
  },
  StoreUserInfo: {
    key: Cache_Key.StoreUserInfo,
    type: StorageType.LOCAL,
    hasEncrypt: false,
  },
  StorePathName: {
    key: Cache_Key.StorePathName,
    type: StorageType.LOCAL,
    hasEncrypt: false,
    expire: 5 * 60,
  },
  StoreOrderCache: {
    key: Cache_Key.StoreOrderCode,
    type: StorageType.LOCAL,
  },
  StoreHasShowFillAddressForm: {
    key: Cache_Key.StoreHasShowFillAddressForm,
    type: StorageType.LOCAL,
    hasEncrypt: false,
  }
  /** 门店 end */
};
