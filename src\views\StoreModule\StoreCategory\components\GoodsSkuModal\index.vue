<template>
	<!-- 自定义图标 -->
	<van-popup round v-model:show="_show" closeable teleport="body" close-icon="close" position="bottom"
		@close="onClose" :safe-area-inset-bottom='safeBottom' lock-scroll>
		<JLoadingWrapper :show="isLoading">
			<div class="goods-sku-box">
				<div class="goods-content">
					<div class="goods-img">
						<MaskBanner :is-use-type-mask="true"
							:type="curGoodsInfo.availStocks == 0 ? 'stock' : curGoodsInfo.isDeleted == 1 ? 'specDeleted' : tempRef.isPres == 1 ? 'pres' : null"
							v-if="curGoodsInfo.disabled || tempRef.isPres == 1">
						</MaskBanner>
						<img :src="tempRef.path || tempRef.firstImg" @click="handlePreviewImage(0)">
					</div>
					<div class="content-detail">
						<GoodsTitle :state="tempRef"></GoodsTitle>
						<PriceContent :appletProductSpecDTOList='skuList' :is-auto-computed="isAutoComputedPrice"
							price-key="minPrice" :sku-info="curGoodsInfo" />
					</div>
				</div>
				<div class="sku-list">
					<div class="spec-group" v-for="(group, groupIndex) in SpecAttrList" :key="groupIndex">
						<div class="spec-title">{{ group.attributeName }}</div>
						<div class="spec-options">
							<div
								class="spec-option"
								:class="{ 'spec-option-active': specValue.id === selectedSpecIds[Object.keys(selectedSpecIds)[groupIndex]] }"
								@click='handleSkuClick(specValue,Object.keys(selectedSpecIds)[groupIndex])'
								v-for="(specValue, valueIndex) in group.specValue"
								:key="valueIndex"
							>
								{{ specValue.attributeValue }}
							</div>
						</div>
					</div>
				</div>
				<div class="add-cart">
					<div class="add-title">数量</div>
					<div class="add-opt">
						<NumberBox :value="cartNum" :max-stock="curGoodsInfo.availStocks"
							@update:value='handleStepChange' :maxSize='curGoodsInfo.upper' />
					</div>  
				</div>
				<div class="footer-box">
					<van-button type="default" class="btn" round @click="handleClick"
						:disabled="!cartNum">去下单</van-button>
				</div>
			</div>
		</JLoadingWrapper>
	</van-popup>
</template>

<script setup lang="ts">
import { ref, reactive, toRef, computed, watch } from "vue"
import { showImagePreview } from 'vant';
import GoodsTitle from "../GoodsTitle.vue"
import NumberBox from "../NumberBox.vue";
import PriceContent from "../PriceContent.vue"
import MaskBanner from "../MaskBanner/index.vue";
import TagBtn from "../TagBtn/index.vue";
import { queryGoodsDetail } from "@/services/storeApi/store-category"
import { isObject } from "@/utils/isUtils";
import useCart from "./hooks/useCart";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import { useMessages } from "@/hooks/useMessage";
import { useRoute,useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";
import type { StyleValue } from "vue";
interface Props {
	isShowInitCartCount: boolean;
	productId?: string;
	show: boolean;
	state: any;
	safeBottom: boolean;
	isAutoComputedPrice: boolean;
	skuId: string | null;
	productIdField?:string;
	isWatchGoods:boolean;
}
const props = withDefaults(defineProps<Props>(), {
	isShowInitCartCount: false,
	show: false,
	safeBottom: true,
	state: () => ({}),
	isAutoComputedPrice: false,
	skuId: null,
	productId: null,
	productIdField:'id',
	isWatchGoods:true
})
const emits = defineEmits<{
	(e: 'update:show', val: boolean): void;
	(e: 'refresh', info: any): void;
	(e: 'update:skuId', val: string): void;
}>()
const route = useRoute()
const router = useRouter()
const tempRef = ref({ ...props.state })
const message = useMessages()
const { handleSave, cartNum, curSku, curGoodsInfo, skuList, isLoading, SpecAttrList, selectedSpecIds,handleSelectedSpecIds } = useCart({ goodsInfo: tempRef,productIdField:props.productIdField,isWatchGoods:props.isWatchGoods })
const _show = computed({
	get: () => props.show,
	set: (val) => emits('update:show', val)
})
const customStyleTag = computed<StyleValue>(() => {
	return {
		backgroundColor: '#F8F8F8',
		borderRadius: '4px',
		borderColor: '#F8F8F8',
		color: '#000',
	}
})
const handleClick = () => {
	handleSave((info: any) => {
		_show.value = false
		//跳转下单页面
		router.push({
		    name:RoutesName.StoreConfirmOrder,
		    query:{
		        ...route.query,
		        ...info
		    }
		})
	})
}
const onClose = () => {
	_show.value = false
}
const handlePreviewImage = (index: number) => {
	showImagePreview({
		images: [tempRef.value.path || tempRef.value.firstImg],
		startPosition: index
	})
}
const handleStepChange = (val: number) => {
	cartNum.value = val
}
const handleSkuClick = (val: any,key:string) => {
	// if (val.disabled) return
	// curSku.value = val.id
	handleSelectedSpecIds(val,key)
	cartNum.value = 1
	emits('update:skuId', curSku.value)
}
const getDetail = async () => {
	try {
		isLoading.value = true
		const res = await queryGoodsDetail(props.productId)
		res[props.productIdField] = res?.id
		tempRef.value = res
	} catch (e) {
		message.createMessageError(`获取失败：${e}`)
	} finally {
		isLoading.value = false
	}
}

watch(() => props.state, (newVal) => {
	if (isObject(newVal)) {
		tempRef.value = newVal
	}
}, {
	deep: true,
})

watch(() => props.productId, (newVal) => {
	if (newVal) {
		getDetail()
	}
})
watch(() => props.show, (newVal) => {
	if (newVal) {
		if (props.isShowInitCartCount) {
			cartNum.value = 1
		}
	}
})
watch(() => props.skuId, (newVal) => {
	if (newVal) {
		curSku.value = newVal
	}
})
</script>

<style scoped lang="less">
@import '@/styles/storeVar.less';
.shelve {
	filter: opacity(50%);
}

.goods-sku-box {
	font-size: 14px;
	padding: 30px 12px 12px;
	box-sizing: border-box;
	position: relative;
	height: 100%;


	.goods-content {
		margin-top: 15px;
		display: flex;
		align-items: center;

		.goods-img {
			position: relative;
			margin-right: 10px;
			box-sizing: border-box;
			width: 80px;
			height: 80px;

			img {
				width: 100%;
				height: 100%;
				min-width: 80px;
				border-radius: 10px;
				object-fit: contain;
			}
		}

		.content-detail {
			height: 80px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}
	}

	.sku-list {
		margin-top: 24px;
		max-height: 300px;
		overflow: auto;
		padding: 5px;
		box-sizing: border-box;
		.spec-group {
			margin-bottom: 20px;

			.spec-title {
				font-size: 14px;
				font-weight: 600;
				color: #333;
				margin-bottom: 12px;
				position: relative;
				padding-left: 12px;

				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 3px;
					height: 14px;
					background-color: #ff4444;
					border-radius: 2px;
				}
			}

			.spec-options {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;

				.spec-option {
					padding: 8px 16px;
					box-sizing: border-box;
					border-radius: 4px;
					background-color: #f8f8f8;
					color: #333;
					font-size: 14px;
					cursor: pointer;
					transition: all 0.2s ease;
					min-width: 67px;
					text-align: center;
					&:hover {
						border-color: #ff4444;
					}

					&.spec-option-active {
						// border: 1px solid #ff4444;
						background-color: #FFEDED;
						color: #ff4444;
						/** 边框不占宽高 */
						box-shadow: 0 0 0 1px #ff4444;
					}
				}
			}
		}
	}



	.add-cart {
		margin-top: 15px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.add-title {}

		.add-opt {}
	}

	.footer-box {
		margin-top: 19px;
		width: 100%;
		box-sizing: border-box;

		.btn {
			background: @error-color;
			width: 100%;
			color: #fff;
		}
	}
}
</style>