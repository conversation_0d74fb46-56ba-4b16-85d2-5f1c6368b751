// import type { <PERSON><PERSON><PERSON>ordR<PERSON>, RouteMeta } from 'vue-router';
// import type { defineComponent } from 'vue';
// import { RoutesName } from '@/enums/routes';
// export type Component<T = any> =
//   | ReturnType<typeof defineComponent>
//   | (() => Promise<T>);

// interface RoutesConfigObject{
//     name: RoutesName;
//     meta?: RouteMeta;
//     children?: RoutesConfig[];
//     fullPath?: string;
//   }

// export type RoutesConfig = string:RoutesName | {RoutesConfigObject}
