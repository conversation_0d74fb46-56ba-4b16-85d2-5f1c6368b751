<template>
  <van-popup
    round
    v-model:show="showRef"
    :style="{ 
        padding: '0px' ,
        background: 'transparent',
        overflow:'initial'
      }"
    lock-scroll
  >
    <JLoadingWrapper :show="false">
      <div class="welfare-bg">
        <div class="welfare-title">
          <p class="welfare-title-text" style="">{{ title }}</p>
        </div>
        <div class="welfare-content" style="flex:1;">
          <p class="welfare-tip">{{ tip }}</p>
          <div class="product-content">
            <div class="welfare-name">
              <p class="welfare-name-text" style="">
                {{ couponData?.categoryName || "" }}
              </p>
              <p style="">{{ "*1" }}</p>
            </div>
            <div class="welfare-product" style="">
              <img :src="couponData.imageUrl || emptyImg" alt="" />
            </div>
          </div>
        </div>
        <div class="welfare-bottom-btn-wrapper">
          <van-button class="welfare-bottom-btn" @click.stop="onClose" round type="danger">
            {{ btnText }}
          </van-button>
        </div>
        <div class="close-btn">
          <img :src="CloseIconSrc" @click="onClose" alt="" />
        </div>
      </div>
    </JLoadingWrapper>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, reactive, toRef, computed, watch, nextTick, toRefs } from "vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import CloseIconSrc from "@/assets/image/popup/closeBtn.png";
import type { LiveCouponInfoResponse } from "@/services/storeApi";
import emptyImg from "@/assets/image/exception/emptyImg.jpg";

interface Props {
  /**是否显示弹窗 */
  show?: boolean;
  /* 福利券数据 */
  couponData?: LiveCouponInfoResponse;
}


const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const emits = defineEmits<{
  (e: "update:show", val: boolean): void;
  (e: "update:couponData", val: LiveCouponInfoResponse): void
}>();

const showRef = ref<boolean>(props.show);
const couponData = ref<LiveCouponInfoResponse>(props.couponData);

const onClose = () => {
  showRef.value = false;
};

const acceptParams = async (param: {
  show: boolean,
  couponData: LiveCouponInfoResponse
}) => {
  showRef.value = param.show;
  couponData.value = param.couponData;
};

const title = computed(() => {
  return "福利券";
});

const tip = computed(() => {
  return couponData.value.receiveFlag == 1
    ? "恭喜你达成观看时长，送你福利券"
    : "达成观看时长后可领取";
});

const btnText = computed(() => {
  return couponData.value.receiveFlag == 1
    ? "领取成功"
    : "继续观看";
});

watch(() => props.show, (newVal) => {
  showRef.value = newVal;
});

watch(() => props.couponData, (newVal) => {
  couponData.value = newVal;
});

defineExpose({
  acceptParams,
});
</script>

<style scoped lang="less">

.welfare-bg {
  position: relative;
  width: 258px;
  height: 338px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url("@/assets/store0602Image/welfareBg.png") no-repeat center/cover;
}

.welfare-title {
  margin-top: 12px;
  width: 130px;
  height: 26px;
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center
}

.welfare-title-text {
  color: #FFF3CE;
  font-weight: 400;
  font-size: 20px;
  letter-spacing: 3px;
  font-style: normal;
  text-transform: none;
}

.welfare-tip {
  margin-top: 16px;
  height: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 12px;
  color: #666666;
  line-height: 16px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.product-content {
  margin-top: 5px;
  height: 165px;
  display: flex;
  flex-direction: column;
  align-items: center
}

.welfare-name {
  padding: 0 12px;
  display: flex;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #333333;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.welfare-name-text {
  max-width: 180px;
  height: 28px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden
}

.welfare-product {
  flex: 1;
  display: flex;
  align-items: center;

  img {
    width: 120px;
    height: 120px;
    border-radius: 8px 8px 8px 8px;
  }
}

.welfare-bottom-btn-wrapper {
  width: 100%;
  height: 100px;
  margin-bottom: 14px;
  display: flex;
  justify-content: center;
  align-items: center
}

.welfare-bottom-btn {
  width: 176px;
  height: 40px;
}

:deep(.j-loading-wrapper-content) {
  overflow: visible
}

:deep(.close-btn) {
  bottom: -32px;
  width: 40px;
  height: 40px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
