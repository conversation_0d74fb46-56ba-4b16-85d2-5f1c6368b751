<template>
  <div class="store_mine_order_wrapper">
    <JLoadingWrapper :show="isPageLoadingRef">
      <!-- tabs -->
      <VanTabs
        :style="{ '--van-tabs-line-height': '40px', height: '100%' }"
        v-model:active="activeTabRef"
        color="#EF1115"
        title-active-color="#EF1115"
        title-inactive-color="#333333"
        line-width="36px"
        line-height="2px"
        swipeable
      >
        <VanTab v-for="item in orderStatusList" :key="item.value" :name="item.value">
          <template #title>
            <span class="tab-title" :class="{ 'tab-active': activeTabRef === item.value }">{{ item.label }}</span>
          </template>
          <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="tab-content" :class="`tab-content_${item.value}`" @scroll="onScroll">
            <template v-if="storeMineOrderList.length">
              <VanList
                v-model:loading="isLoadingRef"
                :finished="isFinishedRef"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <template v-if="[OrderStatusEnum.REFUND].includes(activeTabRef)">
                  <StoreAfterSalesOrderCard
                    v-for="item in storeMineOrderList"
                    :key="item.id"
                    :orderInfo="item"
                    @click="handleToAfterSaleDetail(item)"
                  />
                </template>
                <template v-else>
                  <StoreMineOrderCard
                    v-for="item in storeMineOrderList"
                    :key="item.id"
                    :orderInfo="item"
                    @writeOffCode="handleWriteOffCode"
                    @cancelOrder="handleCancelOrder"
                    @click="handleToOrderDetail(item)"
                    @confirmReceipt="handleConfirmReceipt"
                    @viewLogistics="handleViewLogistics(item)"
                  />
                </template>
              </VanList>
            </template>
            <template v-else>
              <EmptyData style="min-height: 400px;" />
            </template>
          </VanPullRefresh>
        </VanTab>
      </VanTabs>
    </JLoadingWrapper>
    <!-- 扫码 -->
    <StoreQrCode v-model:show="StoreQrCodeRef" :qrCodeUrl="qrCodeUrlRef" />
    <!-- 取消订单 -->
    <CancelOrder v-model:show="cancelOrderRef" @confirm="handleConfirmCancelOrder" />
    <!-- 二次确认 -->
    <JDoubleConfirm
      v-model:show="showDoubleConfirmRef"
      tip="请确认是否收到该货物？"
      @confirm="handleDoubleConfirmReceipt"
    />
    <!-- 物流信息 -->
    <JLogistics
      v-model:show="showLogistics"
      :shipTracesList="shipTracesList"
      :shipCompanyName="logisticsInfo?.shipCompanyName"
      :trackingNo="logisticsInfo?.trackingNo"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onActivated, nextTick } from "vue";
import QRcode from "qrcode";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { 
  OrderStatusEnum, 
  StoreOrderDetailRouteTypeEnum, 
  StoreScanTypeEnum, 
  KeepAliveRouteNameEnum,
  CustomerRoleOperationEnum,
  StoreAfterSaleDetailRouteTypeEnum
} from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import useGetMineOrder from "./hooks/useGetMineOrder";
import useGetLogistics from "./hooks/useGetLogistics";
import { RoutesName } from "@/enums/routes";
import { cancelOrder, confirmReceipt } from "@/services/storeApi";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import StoreMineOrderCard from "./components/StoreMineOrderCard.vue";
import StoreAfterSalesOrderCard from "./components/StoreAfterSalesOrderCard.vue";
import StoreQrCode from "@/views/StoreModule/components/StoreQrCode.vue";
import CancelOrder from "@/views/StoreModule/components/CancelOrder.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import JDoubleConfirm from "@/views/StoreModule/components/JDoubleConfirm.vue";
import JLogistics from "@/views/StoreModule/components/JLogistics.vue";

defineOptions({ name: KeepAliveRouteNameEnum.MY_ORDER });

/** props */
const props = withDefaults(defineProps<{
  orderType?: OrderStatusEnum;
}>(), {
  orderType: OrderStatusEnum.ALL
});

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom, _findIndex } = useKeepAliveRoute();
const { routerPushByRouteName } = useRouterUtils();
const { createMessageSuccess, createMessageError } = useMessages();
const {
  activeTabRef,
  orderStatusList,
  isPageLoadingRef,
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  storeMineOrderList,
  onRefresh,
  onLoad,
  initStoreMineOrderList
} = useGetMineOrder({
  orderType: props.orderType
});
const { showLogistics, shipTracesList, logisticsInfo, getOrderDetailAndGShipTraces } = useGetLogistics();
const StoreQrCodeRef = ref(false);
const qrCodeUrlRef = ref('');

const cancelOrderRef = ref(false);
const cancelOrderCodeRef = ref('');

async function handleWriteOffCode(orderCode: string) {
  let prefixUrl = window.location.origin;
  const qrcodeLink = `${prefixUrl}/verify?orderCode=${orderCode}&routeType=${StoreOrderDetailRouteTypeEnum.SCAN}&scanType=${StoreScanTypeEnum.ORDER}`;
  qrCodeUrlRef.value = await generateQRCode(qrcodeLink);
  StoreQrCodeRef.value = true;
}

/** 取消订单 */
function handleCancelOrder(orderCode: string) {
  cancelOrderRef.value = true;
  cancelOrderCodeRef.value = orderCode;
}

/** 生成二维码 */
async function generateQRCode(url: string): Promise<string | null> {
  try {
    const qrCodeDataUrl = await QRcode.toDataURL(url, {
      width: 156,
      height: 156,
      margin: 2,
    });
    return qrCodeDataUrl;
  } catch (err) {
    createMessageError("生成二维码失败：" + err);
    return null;
  }
}

/** 跳转订单详情 */
function handleToOrderDetail(orderInfo) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.MY_ORDER);
  routerPushByRouteName(RoutesName.StoreOrderDetail, { orderCode: orderInfo?.code, routeType: StoreOrderDetailRouteTypeEnum.MY_ORDER });
}

/** 跳转售后详情 */
function handleToAfterSaleDetail(orderInfo) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.MY_ORDER);
  routerPushByRouteName(RoutesName.StoreAfterSaleDetail, { recordNo: orderInfo?.recordNo, routeType: StoreAfterSaleDetailRouteTypeEnum.MY_AFTER_SALE });
}

/** 取消订单 */
async function handleConfirmCancelOrder() {
  try {
    const res = await cancelOrder(cancelOrderCodeRef.value);
    if (res) {
      createMessageSuccess("取消订单成功");
      cancelOrderRef.value = false;
      cancelOrderCodeRef.value = "";
      initStoreMineOrderList();
    }
  } catch (error) {
    createMessageError("取消订单失败：" + error);
  }
}

/** 确认收货 */
const showDoubleConfirmRef = ref(false);
const confirmReceiptOrderCodeRef = ref('');
function handleConfirmReceipt(orderCode: string) {
  confirmReceiptOrderCodeRef.value = orderCode;
  showDoubleConfirmRef.value = true;
}
async function handleDoubleConfirmReceipt() {
  try {
    const res = await confirmReceipt(confirmReceiptOrderCodeRef.value);
    if (res) {
      showToast("确认收货成功");
      showDoubleConfirmRef.value = false;
      confirmReceiptOrderCodeRef.value = "";
      initStoreMineOrderList();
    }
  } catch (error) {
    createMessageError("确认收货失败：" + error);
  }
}

/** 查看物流 */
async function handleViewLogistics(orderInfo) {
  try {
    const res = await getOrderDetailAndGShipTraces(orderInfo?.code);
    if (res) {
      showLogistics.value = true;
    }
  } catch (error) {
    console.log("error", error);
  }
}

/** 滚动触发 */
function onScroll(e) {
  scrollEventHandler(e, KeepAliveRouteNameEnum.MY_ORDER);
}

onActivated(() => {
  nextTick(() => {
    if (_findIndex(KeepAliveRouteNameEnum.MY_ORDER) !== -1) {
      onRefresh(); // 刷新
    }
    const el = document.getElementsByClassName(`tab-content_${activeTabRef.value}`)[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.MY_ORDER);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.MY_ORDER);
  });
});
</script>

<style lang="less" scoped>
.store_mine_order_wrapper {
  width: 100%;
  height: 100vh;
  :deep(.van-tab) {
    flex: auto;
  }
  :deep(.van-tabs__line) {
    bottom: 18px;
  }
  .tab-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .tab-active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #EF1115;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  :deep(.van-tabs) {
    &__wrap {
      height: 40px;
    }

    &__content {
      height: calc(100% - 40px);
      .van-tab__panel {
        height: 100%;
      }
    }
   }
  .tab-content {
    height: 100%;
    padding: 12px 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }
}
</style>
