import type{ GlobalThemeOverrides } from 'naive-ui';
import type { ConfigProviderThemeVars } from 'vant';
export const themeOverrides: ConfigProviderThemeVars = {
};
export const naiveUIThemeOverrides:GlobalThemeOverrides = {
    DataTable:{
        thColor:"#fff",
        fontSizeSmall:'12px',
        loadingColor:'#1677ff'
    },
    Pagination:{
        itemTextColorActive:'#1677ff',
        itemBorderActive:'1px solid #1677ff',
    }
}