<template>
  <view class="store_order_info">
    <view class="store_order_info_title">订单信息</view>
    <!-- 订单编号 -->
    <view class="store_order_info_item">
      <text class="store_order_info_item_label">订单编号</text>
      <text class="store_order_info_item_value">{{ orderInfoRef?.code ?? `-` }}</text>
    </view>
    <!-- 微信昵称 -->
    <!-- <view class="store_order_info_item">
            <text class="store_order_info_item_label">微信昵称</text>
            <text class="store_order_info_item_value">-</text>
        </view> -->
    <!-- 创建时间 -->
    <view class="store_order_info_item">
      <text class="store_order_info_item_label">创建时间</text>
      <text class="store_order_info_item_value">{{ orderInfoRef?.createTime ?? `-` }}</text>
    </view>
    <!-- 支付时间 -->
    <view class="store_order_info_item">
      <text class="store_order_info_item_label">支付时间</text>
      <text class="store_order_info_item_value">{{ orderInfoRef?.payTime ?? `-` }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { toRefs } from 'vue'
import {
  StoreOrderTypeEnum,
  StoreOrderStatusEnum,
  StoreOrderPayTypeEnum,
  StoreOrderPayStatusEnum,
} from '@/enum'

defineOptions({ name: 'StoreOrderInfo' })

/** props */
const props = defineProps<{
  orderInfo: {
    type?: StoreOrderTypeEnum
    status?: StoreOrderStatusEnum
    code?: string
    pickupType?: number
    money?: number
    goodsAmount?: number
    shippingFee?: number
    payType?: StoreOrderPayTypeEnum
    payStatus?: StoreOrderPayStatusEnum
    afterSaleState?: number
    payTime?: string
    createTime?: string
    orderItemDTOList?: Array<{
      type?: 1 | 2 | 3
      orderId?: string
      productImgPath?: string
      productFrontName?: string
      specName?: string
      price?: number
      count?: number
      exchangePoints?: number
      exchangePrice?: number
    }>
  }
}>()

const { orderInfo: orderInfoRef } = toRefs(props)
</script>

<style lang="scss" scoped>
.store_order_info {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  .store_order_info_title {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 24rpx;
  }
  .store_order_info_item {
    height: 64rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .store_order_info_item_label {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .store_order_info_item_value {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
