import { ref, computed } from "vue";
import { showToast } from "vant";
import { LogisticsStatusEnum } from "@/views/StoreModule/enums";
import { isArray } from "@/utils/isUtils";
import { queryOrderLogistics, getOrderDetailByOrderCode } from "@/services/storeApi";

interface ShipTrace {
  orderCode?: string;
  trackingNo?: string;
  shipCompanyCode?: string;
  state?: LogisticsStatusEnum;
  acceptStation?: string;
  acceptTime?: string;
  location?: string;
  station?: string;
  stationTel?: string;
  stationAdd?: string;
  deliveryMan?: string;
  deliveryManTel?: string;
}

export default function useGetLogistics() {
  /** 显隐物流信息弹窗 */
  const showLogistics = ref(false);

  /** 物流公司与物流单号 */
  const logisticsInfo = ref({
    shipCompanyName: "",
    trackingNo: "",
  });

  /** 物流信息 */
  const shipTracesList = ref<ShipTrace[] | null>(null);

  /** 获取物流信息 */
  const getShipTraces = async (_params: { orderCode: string; trackingNo: string; shipCompanyCode: string }) => {
    try {
      const resp = await queryOrderLogistics(_params);
      if (isArray(resp) && resp?.length > 1) {
        return resp;
      } else {
        return null;
      }
    } catch (error) {
      showToast(`获取物流信息失败`);
      return null;
    }
  };

  /** 获取订单详情 --- 物流 */
  async function getOrderDetailAndGShipTraces(orderCode: string) {
    try {
      const resp = await getOrderDetailByOrderCode({ orderCode: orderCode });
      if (resp) {
        logisticsInfo.value = {
          shipCompanyName: resp.shipCompanyName,
          trackingNo: resp.trackingNo,
        };
        if (resp.shipCompanyCode && resp.trackingNo) {
          const shipTracesResp = await getShipTraces({
            orderCode: orderCode,
            trackingNo: resp.trackingNo,
            shipCompanyCode: resp.shipCompanyCode,
          });
          if (isArray(shipTracesResp) && shipTracesResp?.length > 1) {
            shipTracesList.value = shipTracesResp;
            return true;
          }
        }
      }
    } catch (error) {
      showToast("获取订单详情");
      return false;
    }
  }

  return {
    showLogistics,
    shipTracesList,
    logisticsInfo,
    getShipTraces,
    getOrderDetailAndGShipTraces
  };
}
