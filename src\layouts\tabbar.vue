<template>
  <van-config-provider :theme-vars="theme">
    <view class="default-layout">
      <slot></slot>
      <!-- 注意下面，多了一个自定义tabbar -->
      <Tabbar />
    </view>
  </van-config-provider>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
/** 相关组件 */
import Tabbar from '@/components/Tabbar/index.vue'

/** props */
const props = withDefaults(
  defineProps<{
    themeVars?: object
  }>(),
  {
    themeVars: () => ({}),
  },
)

/** 自定义页面主题 */
const theme = computed(() => {
  return {
    // rateIconFullColor: '#07c160',
    // sliderBarHeight: '4px',
    // sliderButtonWidth: '20px',
    // sliderButtonHeight: '20px',
    // sliderActiveBackgroundColor: '#07c160',
    // buttonPrimaryBorderColor: '#07c160',
    ...props.themeVars,
  }
})
</script>

<style lang="scss" scoped>
@import '@/style/tabbar.scss';
.default-layout {
  width: 100vw;
  height: calc(100vh - $tabbar-height);
  display: flex;
  flex-direction: column;
}
</style>
