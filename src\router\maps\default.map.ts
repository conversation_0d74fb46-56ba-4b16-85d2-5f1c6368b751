import MainLayout from "@/layout/MainLayout/index.vue";
import { RoutesName } from "@/enums/routes";

export const Default = {
  [RoutesName.Root]: {
    path: "/",
    redirect: "/check",
    component: MainLayout,
    meta: {
      isMenu: false,
      isShow: false,
    },
  },
  [RoutesName.Check]: {
    path: "/check",
    component: () => import("@/views/default/Check/index.vue"),
    meta: {
      isMenu: false,
      title: "加载中",
    },
  },
  [RoutesName.Exception403]: {
    path: "/403",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "403",
      title: "无权限查看该页面",
    },
  },
  [RoutesName.Exception404]: {
    path: "/404",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "404",
      title: "页面丢失",
    },
  },
  [RoutesName.Exception9000]: {
    path: "/9000",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9000",
      title: "链接异常",
    },
  },
  [RoutesName.Exception9001]: {
    path: "/9001",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9001",
      title: "请先关注公众号",
    },
  },
  [RoutesName.Exception9002]: {
    path: "/9002",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9002",
      title: "获取用户信息异常",
    },
  },
  [RoutesName.Exception9003]: {
    path: "/9003",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9003",
      title: "访问公众号异常",
    },
  },
  [RoutesName.Exception9004]: {
    path: "/9004",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9004",
      title: "课程已结束",
    },
  },
  [RoutesName.Exception9005]: {
    path: "/9005",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9005",
      title: "链接失效",
    },
  },
  [RoutesName.GetCode]: {
    path: "/getCode",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9005",
      title: "链接失效",
    },
  },
  [RoutesName.UnResiter]: {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
    meta: {
      isMenu: false,
    },
  },
  [RoutesName.Exception9006]: {
    path: "/9006",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9006",
      title: "请授权使用完整服务",
    },
  },
  [RoutesName.Exception9008]: {
    path: "/9008",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9008",
      title: "请在企微环境内打开",
    },
  },
  [RoutesName.Exception9007]: {
    path: "/9007",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9007",
      title: "服务受限",
    },
  },
  [RoutesName.Exception9009]: {
    path: "/9009",
    component: () => import("@/views/default/Exception/index.vue"),
    meta: {
      isMenu: false,
      code: "9009",
      title: "服务受限",
    },
  },
};
