<template>
  <van-popup
    :show="props.show"
    teleport="body"
    position="bottom"
    round
    closeable
    close-icon="close"
    @click-close-icon="handleClose"
    @click-overlay="handleClose"
    @open="handleSearch"
    style="height: auto"
    safe-area-inset-bottom
  >
    <div class="welfare-voucher-popup-content">
      <div class="header">选择福利券</div>
      <JLoadingWrapper :show="isPageLoadingRef">
        <div class="search">
          <van-search
            @search="handleSearch"
            @click-left-icon="handleSearch"
            @clear="handleSearch"
            shape="round"
            v-model="model.name"
            placeholder="输入福利券分类名称"
          />
        </div>
        <van-pull-refresh
          class="content-wrapper"
          v-model="groupMgrListStatusReactive.isPullLoading"
          @refresh="onGroupMgrListRefresh"
        >
          <van-list
            v-if="listData.length"
            v-model:loading="groupMgrListStatusReactive.isNextPageLoading"
            @load="onGroupMgrListNextPageLoad"
            :finished="groupMgrListStatusReactive.isNextPageFinished"
          >
            <div
              v-for="item in listData"
              class="welfare-voucher-card"
              :class="{ active: model.couponBatchId === item.id }"
              @click="handleSelect(item)"
            >
              <div class="welfare-voucher-card-wrapper">
                <div class="title">{{ item.categoryName }}</div>
                <div v-if="item.description" class="description">
                  {{ item.description }}
                </div>
                <div class="row">
                  <span class="label">发放批次ID：</span>
                  <span class="value">{{ item.batchId }}</span>
                </div>
                <div class="row">
                  <span class="label">有效期：</span>
                  <span class="value">{{ item.validUntil }}</span>
                </div>
              </div>
              <div class="number">{{ item.remainingQuantity }}张</div>
            </div>
          </van-list>
          <van-empty v-else description="暂无数据" :image="emptyImg" :image-size="[180, 180]" />
        </van-pull-refresh>
        <div class="stepper-wrapper">
          <div>发送数量</div>
          <van-stepper v-model="model.sendNum" :max="selectedNum" :disabled="!model.couponBatchId" theme="round" button-size="22" disable-input />
        </div>
        <div class="footer">
          <van-button style="width: 100%" @click="handleConfirm" round type="danger">确定</van-button>
        </div>
      </JLoadingWrapper>
    </div>
  </van-popup>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import emptyImg from "@/assets/store0602Image/emptyImg.png";
import { getWelfareVoucherPage, sendWelfareVoucher } from "@/services/storeApi";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import { useUserRole } from "@/views/StoreModule/hooks";
import { useMessages } from "@/hooks/useMessage";
const { storeId } = useUserRole();
const { createMessageError, createMessageSuccess } = useMessages();
const props = withDefaults(defineProps<{ show: boolean; userId: number | string }>(), {
  show: false,
  userId: "",
});
const emits = defineEmits<{
  (e: "update:show", val: boolean): void;
  (e: "close"): void;
  (e: "refresh"): void;
}>();
const initParams = {
  name: null,
  sendNum: 1,
  couponBatchId: null,
  couponId:null
};
const listData = ref([]);
const model = ref({ ...initParams });
const groupMgrListStatusReactive = reactive({
  isPullLoading: false,
  isNextPageLoading: false,
  isNextPageFinished: true,
});
const welfareVoucherPageVO = {
  current: 1,
  size: 30,
  total: 1,
};
const isPageLoadingRef = ref(false);
const loadData = async (isClear = false) => {
  isPageLoadingRef.value = true;
  groupMgrListStatusReactive.isNextPageFinished = false;
  let param = {
    data: {
      name: model.value.name,
      storeId: storeId.value,
    },
    pageVO: {
      current: welfareVoucherPageVO.current,
      size: welfareVoucherPageVO.size,
    },
  };
  try {
    const { current, size, total, records } = await getWelfareVoucherPage(param);
    isClear && (listData.value = []);
    welfareVoucherPageVO.current = Number(current);
    welfareVoucherPageVO.size = Number(size);
    welfareVoucherPageVO.total = Number(total);
    listData.value.push(...records);
    if (
      Number(welfareVoucherPageVO.current) * Number(welfareVoucherPageVO.size) >=
      Number(welfareVoucherPageVO.total)
    ) {
      groupMgrListStatusReactive.isNextPageFinished = true;
    }
  } catch (error) {
    createMessageError("获取福利券数据异常");
  } finally {
    groupMgrListStatusReactive.isNextPageFinished = true;
    groupMgrListStatusReactive.isNextPageLoading = false;
    groupMgrListStatusReactive.isPullLoading = false;
    isPageLoadingRef.value = false;
  }
};
const handleConfirm = async () => {
  try {
    if (!model.value.couponBatchId) {
      createMessageError("请选择福利券");
      return;
    }
    const selectNum = listData.value.find(item => item.id === model.value.couponBatchId)?.remainingQuantity;
    if (model.value.sendNum > selectNum) {
      createMessageError("券余额不足");
      return;
    }
    await sendWelfareVoucher({
      data: {
        userId: props.userId,
        unionId: "",
        couponId: model.value.couponId,
        storeId: storeId.value,
        sendNum: model.value.sendNum,
        couponBatchId: model.value.couponBatchId,
      },
    });
    createMessageSuccess("发放福利券成功");
    emits("refresh");
    handleClose();
  } catch (error) {
    createMessageError(error);
  }
};
const handleClose = () => {
  model.value = { ...initParams };

  emits("update:show", false);
};
const handleSearch = () => {
  welfareVoucherPageVO.current = 1;
  loadData(true);
};
const onGroupMgrListRefresh = () => {
  groupMgrListStatusReactive.isPullLoading = true;
  loadData(true);
};
const onGroupMgrListNextPageLoad = () => {
  if (Number(welfareVoucherPageVO.current) * Number(welfareVoucherPageVO.size) < Number(welfareVoucherPageVO.total)) {
    welfareVoucherPageVO.current++;
    groupMgrListStatusReactive.isNextPageFinished = true;
    loadData();
  }
};
const selectedNum = ref(0);
const handleSelect = item => {
  if (item.id === model.value.couponBatchId) {
    model.value.couponBatchId = null;
    model.value.couponId = null;
    selectedNum.value = 0;
  } else {
    model.value.couponBatchId = item.id;
    selectedNum.value = item.remainingQuantity;
    model.value.couponId = item.categoryId;
  }
};

</script>
<style scoped lang="less">
.welfare-voucher-popup-content {
  height: 70vh;
  overflow: hidden;
  .header {
    text-align: center;
    line-height: 52px;
    height: 52px;
    font-size: 16px;
    font-weight: 700;
  }
  .search {
    margin-bottom: 10px;
  }
  .stepper-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    height: 30px;
    margin-bottom: 30px;
    .van-stepper {
      :deep(.van-stepper__minus) {
        color: #000;
        border-color: #eee;
      }
      :deep(.van-stepper__plus) {
        background: #ef1115;
      }
    }
  }
  .footer {
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    padding: 0 12px;
  }
  .content-wrapper {
    width: calc(100% - 24px);
    height: calc(100% - 52px - 64px - 44px - 8px - 60px);
    overflow-y: scroll;
    padding: 0 12px;
    margin-bottom: 8px;
    .welfare-voucher-card {
      position: relative;
      background-image: url("@/assets/store0602Image/welfareVoucherBg.png");
      background-repeat: no-repeat;
      background-size: 100% calc(100% - 2px);
      height: 142px;
      &-wrapper {
        width: calc(100% - 16px * 2);
        padding: 16px 12px 16px;
        .title {
          position: relative;
          height: 30px;
          line-height: 30px;
          font-size: 20px;
          padding-left: 12px;
          margin-bottom: 8px;
          max-width: 260px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          &::before {
            content: " ";
            display: block;
            position: absolute;
            top: 50%;
            left: 0px;
            width: 4px;
            height: 16px;
            transform: translateY(-50%);
            background: #ef1115;
            border-radius: 99px;
          }
        }
        .description {
          font-size: 14px;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
          height: 20px;
          line-height: 20px;
          margin-bottom: 6px;
        }
        .row {
          height: 20px;
          line-height: 20px;
          margin-bottom: 6px;
          font-size: 14px;
          .label {
            color: #999;
          }
        }
      }
      .number {
        position: absolute;
        top: 28px;
        right: 32px;
        color: #ef1115;
        font-size: 24px;
      }
    }
    .active.welfare-voucher-card {
      background-image: url("@/assets/store0602Image/welfareVoucherActiveBg.png");
      .welfare-voucher-card-wrapper {
        .title {
          color: #fff;
          &::before {
            background: #fff;
          }
        }
        .row {
          color: #fff;
          .label {
            color: #fff;
          }
        }
      }
      .number {
        color: #fff;
      }
    }
    .welfare-voucher-card {
      margin-bottom: 8px;
    }
  }
}
</style>
