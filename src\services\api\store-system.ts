import { JRequest } from '@/services/index'
const enum SystemApiEnum {
  getAgreementByKey = '/applet/globalConfigs/getAgreement',
  getStoreLogo = '/h5/homeLogo/getLogo',
  acceptStoreBelong = '/applet/store/personal/acceptStoreBelong',
}
// 查询协议
export async function getAgreementByKey(params = {}) {
  return JRequest.get({
    url: SystemApiEnum.getAgreementByKey,
    params,
    requestConfig: {
      withToken: true,
    },
  })
}

interface StoreInfoResponse {
  createTime: string
  homeImgPath: string
  id: string
  imgPath: string
  isEnableImg: number
  isEnableName: number
  name: string
  slogan: string
  updateTime: string
}
export async function getStoreLogo() {
  return JRequest.get<StoreInfoResponse>({
    url: SystemApiEnum.getStoreLogo,
    requestConfig: {
      withToken: true,
    },
  })
}
export async function acceptStoreBelong(state: string) {
  return JRequest.get({
    url: SystemApiEnum.acceptStoreBelong,
    params: {
      state,
    },
  })
}
