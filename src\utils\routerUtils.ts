import { useRouter } from "vue-router";
import type { NavigationHookAfter, NavigationGuard } from "vue-router";
import { isString, isObject, isNullAndUnDef, isArray, isBoolean, isFunction, isNullOrUnDef } from "@/utils/isUtils";
import { deepmerge } from "deepmerge-ts";

import SvgIcon from "@/components/SvgIcon/index.vue";
import { h } from "vue";
import { baseRoutesConfig } from "@/router/config/base.config";
export function mergeRoutesConfig(target, base) {
  const tempRoutesConfig = [...base];
  target.forEach(route => {
    if (isString(route) && !base.includes(route)) {
      tempRoutesConfig.push({ name: route });
    } else if (isObject(route)) {
      const index = tempRoutesConfig.findIndex(item => route.name === item.name);
      if (index !== -1) {
        tempRoutesConfig[index] = deepmerge(tempRoutesConfig[index], route);
      } else tempRoutesConfig.push(route);
    }
  });
  return tempRoutesConfig;
}

export function getRoutesMapByConfig(routesConfig, baseRoutes) {
  const _routes = [];
  const defaultRouteMetaConfig = {
    meta: {
      auth: "*",
      isMenu: true,
    },
  };
  for(let i=0;i<routesConfig.length;i++){
    let route = routesConfig[i];
  // routesConfig.forEach(route => {
    if (isString(route)) {
      if (isNullOrUnDef(baseRoutes[route])) {
        console.warn(`${route}未在routes里面注册`);
        continue
      } else {
        _routes.push(
          deepmerge(defaultRouteMetaConfig, baseRoutes[route]),
          //     {
          //     ...{
          //         meta:{
          //             auth:'*',
          //             isMenu: true
          //         },

          //     },
          //     ...baseRoutes[route]
          // }
        );
      }
    } else {
      const routeName = route.name;
      if (isNullAndUnDef(routeName)) {
        throw new Error(`routeName不能为空`);
      } else if (isNullOrUnDef(baseRoutes[routeName])) {
        console.warn(`${route}未在routes里面注册`);
        continue
      }
      let _route = {
        name: baseRoutes[routeName].name,
        path: baseRoutes[routeName].path,
        redirect: baseRoutes[routeName].redirect,
        component: baseRoutes[routeName].component,
        children: [],
        meta: {
          auth: route.auth || baseRoutes[routeName].meta.auth || "*",
          title: route.title || baseRoutes[routeName].meta.title,
          icon: baseRoutes[routeName].meta.icon || "",
          isMenu: true,
          authMap: baseRoutes[routeName].meta.authMap || {},
        },
      };
      if (isBoolean(route.meta?.isMenu)) {
        _route.meta.isMenu = route.meta.isMenu;
      }
      if (isBoolean(route.meta?.isShow)) {
        _route.meta.isShow = route.meta.isShow;
      } else if (isBoolean(baseRoutes[routeName].meta?.isMenu)) {
        _route.meta.isMenu = baseRoutes[routeName].meta.isMenu;
      }
      if (isArray(route.children) && route.children.length) {
        _route.children = getRoutesMapByConfig(route.children, baseRoutes);
      }
      _routes.push(_route);
    }
  // });
  }
  return _routes;
}

export function loadRoutes(routes, router) {
  routes.forEach(route => router.addRoute(route));
}

export function loadGuards(guardsObj) {
  const { beforeEach = [], afterEach = [] } = guardsObj;
  const router = useRouter();
  beforeEach.forEach((guard: NavigationGuard) => {
    if (isFunction(guard)) {
      router.beforeEach(guard);
    }
  });
  afterEach.forEach((guard: NavigationHookAfter) => {
    if (isFunction(guard)) {
      router.afterEach(guard);
    }
  });
}


export function getFirstChildrenRoute(routerList){
  let firstRoute = null;
  for(let i = 0; i<routerList.length; i++){
    if(routerList[i].children && routerList[i].children.length){
      firstRoute = getFirstChildrenRoute(routerList[i].children);
      if(firstRoute){
        break;
      }
    }else{
      firstRoute = routerList[i];
      break;
    }
  }
  return firstRoute;
}
export function syncBaseConfig(routeConfig:[]){
  try{
    if(isArray(routeConfig)){
      _baseRoutesConfig.forEach(item=>{
        if(!routeConfig.includes(item)){
          const position = routeConfig.length - 1
          routeConfig.splice(position,0,item)
        }
      })
    }
  }
  catch(e){

  }
}