import { ref, reactive, type Ref } from "vue";
import { queryIntergralList, queryPointSiftList } from "@/services/storeApi/store-category";
import { useMessages } from "@/hooks/useMessage";
export function useIntergralData(modal: Ref<any>) {
  const message = useMessages();
  const dataList = ref<any>([]);
  const tabList = ref([]);
  const listLoadingRef = ref<boolean>(false);
  const listFinishedRef = ref<boolean>(false);
  const pageVO = ref({
    size: 30,
    current: 1,
    total: 0,
  });
  async function getList() {
    try {
      const params: any = {
        data: {},
        pageVO: {
          current: pageVO.value.current,
          size: pageVO.value.size,
        },
      };
      if (modal.value.frontName) {
        params.data.frontName = modal.value.keyword;
      }
      if (modal.value.cateId) {
        params.data.cateId = modal.value.cateId;
      }
      if (modal.value.minPoints) {
        params.data.minPoints = modal.value.minPoints;
      }
      if (modal.value.maxPoints) {
        params.data.maxPoints = modal.value.maxPoints;
      }
      const { records, total } = await queryIntergralList(params);
      if (pageVO.value.current == 1) {
        dataList.value = records;
      } else {
        dataList.value.push(...records);
      }
      pageVO.value.total = Number(total) || 0;
      //加载完成
      if (pageVO.value.current * pageVO.value.size >= pageVO.value.total) {
        listFinishedRef.value = true;
      }
    } catch (e) {
      message.createMessageError(`获取失败：${e}`);
    } finally {
      listLoadingRef.value = false;
    }
  }
  const reloadData = () => {
    dataList.value = [];
    listFinishedRef.value = false;
    pageVO.value.current = 1;
    pageVO.value.total = 0;
    getList();
  };
  const getTabList = async () => {
    try {
      const res = await queryPointSiftList();
      tabList.value = res;
      tabList.value.unshift({ id: "", siftName: "全部" });
      reloadData();
    } catch (error) {
      message.createMessageError(`获取失败：${error}`);
    }
  };
  return {
    dataList,
    listLoadingRef,
    listFinishedRef,
    reloadData,
    getTabList,
    tabList,
  };
}
