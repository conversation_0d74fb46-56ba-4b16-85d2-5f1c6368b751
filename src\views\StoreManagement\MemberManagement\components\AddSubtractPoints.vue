<template>
  <VanPopup
    :show="showRef"
    @update:show="handleUpdateShow"
    round
    position="bottom"
    closeable
    safe-area-inset-bottom
    close-icon="close"
    teleport="body"
    style="height: auto"
  >
    <div class="wrapper">
      <!-- header -->
      <div class="header-container">
        <span class="title">加减积分</span>
      </div>
      <div class="point-contianer">
        <!-- 类型 -->
        <div class="coupon-redeem-content">
          <div class="coupon-redeem-content-item">类型</div>
          <VanRadioGroup v-model="model.changeType" direction="horizontal">
            <VanRadio :name="13" checked-color="#EF1115">
              <span class="radio-title">增加</span>
            </VanRadio>
            <VanRadio :name="14" checked-color="#EF1115">
              <span class="radio-title">减少</span>
            </VanRadio>
          </VanRadioGroup>
        </div>
        <!-- 数量 -->
        <div class="coupon-redeem-content">
          <div class="coupon-redeem-content-item">数量</div>
          <VanStepper
            v-model="model.changeValue"
            theme="round"
            button-size="22"
            :style="{ '--van-stepper-button-round-theme-color': '#EF1115' }"
            integer
            input-width="50px"
          />
        </div>
        <!-- 备注 -->
        <div class="coupon-redeem-content">
          <div class="coupon-redeem-content-item">备注</div>
          <VanField
            v-model="model.reason"
            rows="1"
            autosize
            type="textarea"
            placeholder="请输入加减原因，客户可见"
            :maxlength="15"
            style="flex: 1; background: #f8f8f8; padding: 6px 8px; border-radius: 8px"
          />
        </div>
      </div>

      <VanButton type="danger" block round @click="handleConfirm">确定</VanButton>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { addOrSubtractIntegral } from "@/services/storeApi";
import { useMessages } from "@/hooks/useMessage";
import { useUserRole } from "@/views/StoreModule/hooks";
defineOptions({ name: "AddSubtractPoints" });
const { createMessageSuccess, createMessageError } = useMessages();
const { storeId } = useUserRole();
/** props */
const props = defineProps<{
  show: boolean;
  customerId: number | string;
  unionId: string;
}>();

/** emit */
const emits = defineEmits<{
  (e: "update:show", val: boolean): void;
  (e: "refresh"): void;
}>();

const showRef = computed({
  get: () => props.show,
  set: val => emits("update:show", val),
});

const initParams = {
  /** 类型 */
  changeType: 13,
  /** 积分数量 */
  changeValue: 1,
  /** 备注 */
  reason: "",
};
const model = ref({ ...initParams });

const handleUpdateShow = (val: boolean) => {
  emits("update:show", val);
};
const handleConfirm = async () => {
  try {
    await addOrSubtractIntegral({
        customerId: props.customerId,
        unionId: props.unionId,
        changeType: model.value.changeType,
        changeValue: model.value.changeValue,
        reason: model.value.reason,
        storeId: storeId.value,
    });
    createMessageSuccess("操作积分成功");
    handleUpdateShow(false);
    model.value = { ...initParams };
    emits("refresh");
  } catch (error) {
    createMessageError("操作积分失败:" + error);
  }
};
</script>

<style lang="less" scoped>
.wrapper {
  padding: 8px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  .header-container {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
  .point-contianer {
    display: flex;
    flex-direction: column;
    gap: 20px;
    .coupon-redeem-content {
      display: flex;
      align-items: center;
      gap: 24px;
      .coupon-redeem-content-item {
        width: 32px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.radio-title {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #ffffff;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
