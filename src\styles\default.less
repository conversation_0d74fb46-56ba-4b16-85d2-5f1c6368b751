@import "@/styles/defaultVar.less";
@import "@/styles/fonts.less";
body {
  background-color: @blank-background-color;
}
.main-bg {
  height: 100vh;
  width: 100vw;
}

#nprogress {
  pointer-events: none;
  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}

.tabbar-H{
  height: calc(50px + constant(safe-area-inset-bottom));
  height: calc(50px + env(safe-area-inset-bottom));
}

.isIPhoneXRegexBottom {
  padding-bottom: constant(safe-area-inset-bottom) !important;   /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom) !important;  /*兼容 IOS>11.2*/
}

.inner-page-height {
  height: @inner-bg-height;
}
:root:root {
  --van-black: #000;
  --van-white: #fff;
  --van-gray-1: #f7f8fa;
  --van-gray-2: #f2f3f5;
  --van-gray-3: #ebedf0;
  --van-gray-4: #dcdee0;
  --van-gray-5: #c8c9cc;
  --van-gray-6: #969799;
  --van-gray-7: #646566;
  --van-gray-8: #323233;
  --van-red: #ee0a24;
  --van-blue: #1677FF;
  --van-orange: #ff976a;
  --van-orange-dark: #ed6a0c;
  --van-orange-light: #fffbe8;
  --van-green: #00b42a;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}