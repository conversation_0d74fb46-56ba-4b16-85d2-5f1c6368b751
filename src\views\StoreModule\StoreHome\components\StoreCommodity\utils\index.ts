import { isString, isNullOrUnDef, isArray } from "@/utils/isUtils";

/**
 * @description 获取具有最低价格（考虑活动价）的商品规格
 * @param {Array} skuList 商品规格列表
 * @returns {Object} 最低价格的商品规格对象
 * @throws {Error} 当输入不是数组时抛出错误
 */
export const getLowestPriceSku = (skuList: any[]) => {
  if (!Array.isArray(skuList)) {
    throw new Error("Input must be an array");
  }

  if (skuList.length === 0) {
    return {};
  }

  // 创建副本避免修改原数组
  const processedList = skuList.map(sku => {
    const minPrice = sku.activityPrice < sku.price ? sku.activityPrice : sku.price;
    return {
      ...sku,
      minPrice: sku.minPrice ?? minPrice,
    };
  });

  // 找到最低价格
  const lowestPrice = Math.min(...processedList.map(sku => sku.minPrice));

  // 返回第一个匹配的最低价格商品
  return processedList.find(sku => sku.minPrice === lowestPrice) || {};
};
