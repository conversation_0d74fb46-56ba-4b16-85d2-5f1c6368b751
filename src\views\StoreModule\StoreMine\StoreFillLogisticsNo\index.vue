<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <div class="store_fill_logistics_wrapper">
      <!-- 表单 -->
      <div class="store_fill_logistics_form">
        <!-- 物流公司 -->
        <div class="form-item">
          <div class="form-item-label">
            <span class="tip">*</span>
            <span style="margin-right: 8px;width: 56px;">物流公司</span>
          </div>
          <VanField
            v-model="formValue.shipCompanyName"
            placeholder="请选择"
            readonly
            :maxlength="200"
            @click="showLogisticsCompany = true"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
          <span class="logistics">
            <VanIcon name="arrow-down" />
          </span>
        </div>
        <!-- 物流单号 -->
        <div class="form-item">
          <div class="form-item-label">
            <span class="tip">*</span>
            <span style="margin-right: 8px;width: 56px;">物流单号</span>
          </div>
          <VanField
            v-model="formValue.trackingNo"
            placeholder="请填写物流单号"
            :maxlength="30"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
        </div>
      </div>
      <!-- 提交 -->
      <div class="footer">
        <VanButton
          type="danger"
          block
          round
          @click="handleSubmit"
          :disabled="isPageLoadingRef"
          style="width: 100%;height: 36px;"
        >
          提交
        </VanButton>
      </div>
    </div>
    <!-- 物流公司 -->
    <VanActionSheet v-model:show="showLogisticsCompany" title="选择物流公司" :closeable="false">
      <div class="refund_reason_list">
        <div
          class="refund_reason_item"
          v-for="item in logisticsCompanyList"
          :key="item.value"
          @click="onSelectRefundReason(item.value, item.label)"
        >
          <span :class="{'active_title': formValue.shipCompanyName == item.label }">{{ item.label }}</span>
        </div>
      </div>
    </VanActionSheet>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs } from "vue";
import { useRouter } from "vue-router";
import { showToast } from 'vant';
import { useMessages } from "@/hooks/useMessage";
import { CustomerRoleOperationEnum } from "@/views/StoreModule/enums";
import { getLogisticsCompany, executeAfterSaleActionApi } from "@/services/storeApi";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

defineOptions({ name: "StoreFillLogisticsNo" });

/** props */
const props = defineProps<{
  recordNo: string;
}>();

const { recordNo: recordNoRef } = toRefs(props);
const router = useRouter();
const { createMessageError, createMessageSuccess } = useMessages();
const isPageLoadingRef = ref<boolean>(false);

/** 表单参数 */
const initparams = {
  shipCompanyName: null, // 物流公司
  shipCompanyCode: null, // 退货快递公司编码
  trackingNo: null, // 退货快递单号
};
const formValue = ref({ ...initparams });

/** 验证表单 */
function _validateForm() {
  const requiredFields = [
    { field: 'shipCompanyCode', message: '请选择物流公司' },
    { field: 'trackingNo', message: '请输入物流单号' },
  ];

  for (const { field, message } of requiredFields) {
    if (!formValue.value[field] || (Array.isArray(formValue.value[field]) && formValue.value[field].length === 0)) {
      showToast(message);
      return false;
    }
  }

  return true;
}

/** 获取参数 */
function _getParams() {
  const { shipCompanyCode, trackingNo } = formValue.value;
  return {
    recordNo: recordNoRef.value,
    action: CustomerRoleOperationEnum.SUBMIT_SHIPPING_INFO,
    shipCompanyCode,
    trackingNo
  }
}

/** 提交 */
async function handleSubmit() {
  try {
    // 先验证表单
    let valid =  _validateForm();
    if (!valid) return;
    isPageLoadingRef.value = true;
    await executeAfterSaleActionApi(_getParams());
    showToast("提交成功");
    isPageLoadingRef.value = false;
    router.back();
  } catch (error) {
    createMessageError("提交失败：" + error);
    isPageLoadingRef.value = false;
  }
}

/** 物流公司显隐 */
const showLogisticsCompany = ref(false);
const logisticsCompanyList = ref([]);
/** 选择物流公司 */
function onSelectRefundReason(value: string, label: string) {
  formValue.value.shipCompanyName = label;
  formValue.value.shipCompanyCode = value;
  showLogisticsCompany.value = false;
}

/** 查询物流公司 */
async function getLogisticsCompanyList() {
  try {
    let _params = {
      pageVO:{
        current: 1,
        size: 100
      }
    };
    const { records } = await getLogisticsCompany(_params);
    if (records?.length > 0 ) {
      logisticsCompanyList.value = records.map((item: any) => {
        return { label: item.name, value: item.code };
      });
    }
  } catch (error) {
    console.log("error", error);
  }
}

/** 组件挂载 */
onMounted(async () => {
  /** 获取物流公司 */
  await getLogisticsCompanyList();
});
</script>

<style lang="less" scoped>
.store_fill_logistics_wrapper {
    width: 100%;
    height: 100vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .store_fill_logistics_form {
        flex: 1;
        height: calc(100% - 36px);
        padding: 12px 12px 0px 12px;
        box-sizing: border-box;
        overflow-y: auto;
        .form-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;
            position: relative;
            .form-item-label {
                margin-bottom: 8px;
                .tip {
                  color: #FF3E3E;
                  margin-top: 4px;
                }

                span {
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 400;
                  font-size: 14px;
                  color: #333333;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                }
            }
            &:last-child {
                margin-bottom: 0px;
            }
            .logistics {
                position: absolute;
                top: 36px;
                right: 12px;
            }
        }
    }
    .footer {
        padding: 12px;
        box-sizing: border-box;
        padding-bottom: env(safe-area-inset-bottom);
        :deep(.van-button__text) {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 14px;
          color: #FFFFFF;
          line-height: 24px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
    }
}
.refund_reason_list {
    height: 60vh;
    .refund_reason_item {
        width: 100%;
        height: 54px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #E5E6EB;
        box-sizing: border-box;
        &:first-child {
            border-top: 1px solid #E5E6EB;
        }
    }
}
.active_title {
  color: #4DA4FF;
}
</style>
