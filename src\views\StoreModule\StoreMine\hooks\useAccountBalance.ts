import { ref, computed } from "vue";
import { storeBalanceApi } from "@/services/storeApi";

export default function useAccountBalance() {
  /** 账户余额（单位：分） */
  const accountBalanceInCents = ref(0);

  /** 账户余额（单位：元） */
  const accountBalanceInYuan = computed(() => {
    return accountBalanceInCents.value / 100;
  });

  /** 获取账户余额 */
  async function getAccountBalance() {
    try {
      const resp = await storeBalanceApi();
      if (resp) {
        accountBalanceInCents.value = Number(resp?.accountBalance ?? 0);
      }
    } catch (error) {
      console.log("获取账户余额失败：" + error);
    }
  }

  return {
    accountBalance: accountBalanceInYuan,
    accountBalanceInCents,
    getAccountBalance,
  };
}