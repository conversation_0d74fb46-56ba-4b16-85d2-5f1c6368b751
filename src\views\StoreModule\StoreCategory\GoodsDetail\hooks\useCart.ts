import { ref, computed, watch, reactive } from "vue";
import type { Ref } from "vue";
import { storeToRefs } from "pinia";
import { isNullOrUnDef } from "@/utils/isUtils";
import { useUserStore } from "@/stores/modules/user";
import { useMessages } from "@/hooks/useMessage";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { contrastMinPriceSku, filterSkuMinIntegral, filterSkuMinWelfare } from "@/utils/storeUtils";
import { nonPres } from "@/services/storeApi";
interface Props {
  goodsInfo: Ref<any>;
  isWatchGoods?: boolean;
  productIdField?: string;
  type?: StoreGoodsEnum;
}

export type BtnType = "cart" | "shop";

export default function ({ goodsInfo, isWatchGoods = true, productIdField = "id", type }: Props) {
  
  const message = useMessages();
  const cartNum = ref<number>(1);
  const curSku = ref<string>("");
  const isLoading = ref<boolean>(false);
  const skuList = computed<any[]>(() => {
    let list = [];
    if (type == StoreGoodsEnum.IntegralGoods) {
      list = goodsInfo.value?.appletPointSpecDTOS || [];
    }
    if (type == StoreGoodsEnum.Goods) {
      list = goodsInfo.value?.appletProductSpecDTOList || [];
    }
    if (type == StoreGoodsEnum.WelfareTicket) {
      list = goodsInfo.value?.couponProductSpecList || [];
    }
    list = JSON.parse(JSON.stringify(list));
    //添加规格最小售价
    list.forEach(item => {
      item.minPrice = item.price;
      if (!isNullOrUnDef(item.activityPrice) && item.activityPrice < item.price) {
        item.minPrice = item.activityPrice;
      }
    });
    return list.map(item => {
      return {
        ...item,
        disabled: !item.availStocks || item.isDeleted == 1 || goodsInfo.value.isPublish == 0,
      };
    });
  });
  const curGoodsInfo = computed(() => {
    let info = skuList.value.find(item => item.id == curSku.value) || {};
    return {
      ...info,
      availStocks: info?.availStocks || 0,
      price: info?.price || 0,
      minPrice: info?.minPrice || 0,
      exchangePrice: info?.exchangePrice || 0,
      upper: info?.upper || 0,
      exchangeCount: info?.exchangeCount || 0,
    };
  });
  //可省价格
  const discountPrice = computed(() => {
    const price = curGoodsInfo.value.price || 0;
    const activePrice = curGoodsInfo.value.activityPrice || 0;
    const result = price - activePrice;
    return (result / 100).toFixed(2);
  });
  //是否存在活动价格
  const isExistActivePrice = computed(() => {
    return !isNullOrUnDef(curGoodsInfo.value.activityPrice);
  });
  const setMinSkuData = () => {
    let info: any = {};
    if (type == StoreGoodsEnum.IntegralGoods) {
      info = filterSkuMinIntegral(skuList.value);
    }
    if (type == StoreGoodsEnum.Goods) {
      info = contrastMinPriceSku(skuList.value);
    }
    if (type == StoreGoodsEnum.WelfareTicket) {
      info = filterSkuMinWelfare(skuList.value);
    }
    curSku.value = info.id;
  };
  const setSkuInfo = (skuId: string) => {
    curSku.value = skuId;
  };

  //初始化sku数据
  watch(
    () => goodsInfo.value,
    () => {
      //初始化sku参数
      if (!isWatchGoods) {
        return;
      }
      curSku.value = "";
      setMinSkuData();
    },
    {
      deep: true,
    },
  );
  return {
    curSku,
    cartNum,
    curGoodsInfo,
    skuList,
    discountPrice,
    isExistActivePrice,
    setMinSkuData,
    setSkuInfo,
    isLoading,
  };
}
