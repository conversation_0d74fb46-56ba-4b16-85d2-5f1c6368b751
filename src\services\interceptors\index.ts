// import {createLocalStorage} from "@/utils/cache/storageCache";
import { axiosConfigTransform } from "@/utils/http/Axios/transform";
import type {
  ResponseInterceptor,
  TAxiosInterceptors,
  RequestInterceptor,
  TAxiosConfig,
  ResponseResult,
} from "@/utils/http/Axios/type";
import { downloadFileFromStream } from "@/utils/fileUtils";
import { useMessages } from "@/hooks/useMessage";
import{ CanceledError, AxiosError } from "axios";


function isAxiosError(error): error is AxiosError<ResponseResult> {
  return error.isAxiosError;
}


import { isProdEnv } from "@/utils/envUtils";
import { encryption } from "@/utils/crypto";
import { isArray, isNullOrUnDef, isObject } from "@/utils/isUtils";


//重写响应的key
const tranfromKeyMap = {
  "request-no": "request-No",
};
const tranfromKeys = Reflect.ownKeys(tranfromKeyMap);

const cryptoKey = '********************************'
const cryptoIV = "5ad1ff0d20074e65"

const requestHeaderConfigHandler: RequestInterceptor = {
  onFulfilled(config) {
    let _configAfterTransform: TAxiosConfig = axiosConfigTransform(config);
    return _configAfterTransform;
  },
};

const cryptoHandler: RequestInterceptor = {
  onFulfilled(config) {
    if(config.requestOptions.requestContentType !== 'json'){
      return config
    }
    else{
      if((config.method == 'post' || config.method == 'put') && !config.data){
        config.data = {}
      }
      if(config.data && /(applet\/).*/.test(config.url) && !config.requestOptions.skipCrypto){
        console.log(JSON.parse(JSON.stringify(config.data)));
        console.log(JSON.stringify(config.data));
        const _encConfig = encryption(
          JSON.stringify(config.data),
          cryptoKey,
          cryptoIV
        )
        config.data = {"dataJson":_encConfig}
  
      }
      return config
    }
   
  },
};

const streamDownloadHandler: ResponseInterceptor = {
  onFulfilled(response) {
    const { headers, data, config: { requestOptions }} = response;
    const AttachmentFlag = "attachment;";
    if (headers["content-disposition"] && requestOptions.responeseType === "stream") {
      if (headers["content-disposition"].indexOf(AttachmentFlag) != -1) {
        let filename: string;
        if (headers.filename) filename = headers.filename;
        else {
          const regResult = /filename=(.*)/.exec(headers["content-disposition"]);
          filename = regResult ? regResult[1] : "未命名文件";
        }
        downloadFileFromStream({
          stream: data,
          filename: decodeURIComponent(filename),
        });
        response.data = {
          code: "200",
          data: "下载成功",
          message: "处理成功"
        }
        return response;
      }
    } else return response;
  },
};

const responseCodeFilterHandler: ResponseInterceptor = {
  onFulfilled(response) {
    const requestHeaders = response.headers
    const requestConfig = response.config.requestOptions
    const realResponse = response.data;
    if((!isObject(realResponse) && requestConfig.isReturnRawResponse) || requestConfig.skipInterceptors){
      return realResponse
    }
    switch (realResponse.code) {
      case "200":
      if(requestConfig.isReturnRawResponse){
        return realResponse
      }
      else{
        if(!isNullOrUnDef(realResponse.data) && isArray(requestConfig.extendResHeaders) && requestConfig.extendResHeaders.length){
            requestConfig.extendResHeaders.forEach(key=>{
              // const _value = requestHeaders[storeHeaderUniqueKeysMap[key]]
              // if( isNullOrUnDef(_value) ) {
              //   realResponse.data[key] = _value
              // }
              if (
                tranfromKeys.includes(key) &&
                requestHeaders[tranfromKeyMap[key]]
              ) {
                realResponse.data[key] = requestHeaders[tranfromKeyMap[key]];
              } else {
                if (!isNullOrUnDef(requestHeaders[key])) {
                  if (isObject(realResponse.data)) {
                    realResponse.data[key] = requestHeaders[key];
                  }else{
                    realResponse.data = {
                      value:realResponse.data,
                      [key]:requestHeaders[key]
                    }
                  }
                }
              }
            })
          }
          if(!isNullOrUnDef(realResponse.data) && isArray(requestConfig.extendResData) && requestConfig.extendResData.length){
            requestConfig.extendResData.forEach(key=>{
              if (!isNullOrUnDef(realResponse[key])) {
                if (isObject(realResponse.data)) {
                  realResponse.data[key] = realResponse[key];
                }else{
                  const data = {
                    value:realResponse.data,
                    [key]:realResponse[key]
                  }
                  realResponse.data = data
                }
              }
            })
          }
          return realResponse.data;
        }
      default:
        return Promise.reject(response);
    }
  },
};

const timeoutHandler: ResponseInterceptor = {
  onRejected(error, options) {
    if(isAxiosError(error)){
      const { code, message } = error;
      if (code === "ECONNABORTED" && message.indexOf("timeout") != -1) {
        error.message = "当前网络不稳定，请稍后重试";
      } 
    }
    return Promise.reject(error);
  },
};


let _timer = null;
//11003 账号停用
const tokenInvailResponseCode = ["1001", "1002","11003","401"];
const tokenInvailResponseStatus = [401];
const tokenInvaildHandler: ResponseInterceptor = {
  onRejected(error, options) {
    // if(isAxiosError(error) && !error.response){
    //   const { createMessageError } = useMessages();
    //     if (_timer) {
    //       clearTimeout(_timer);
    //       _timer = null;
    //     }
    //     _timer = setTimeout(() => {
    //       _timer = null;
    //       import("@/utils/accountUtils").then(({ afterLogout }) => {
    //         afterLogout();
    //       });
    //     }, 1000);
    //     const msgText = "登录已失效，即将跳转到登录界面";
    //     createMessageError(msgText);
    //     return Promise.reject(msgText);
    // }
    // else{
      if(isAxiosError(error) && error.code == 'ECONNABORTED'){
        return Promise.reject(error);
      }
      const { data:serveResponse = {},status = {} } = (isAxiosError(error) && error.response) ? error.response : error;
      if (
          (status === 200 && serveResponse?.code && tokenInvailResponseCode.includes(serveResponse?.code)) || 
          tokenInvailResponseStatus.includes(status) ||
          (status === 200 && serveResponse?.code && serveResponse.code == '2002' && serveResponse.message.indexOf('令牌过期或者失效') != -1 )
      ) {
        // const { createMessageError } = useMessages();
        // if (_timer) {
        //   clearTimeout(_timer);
        //   _timer = null;
        // }
        // _timer = setTimeout(() => {
        //   _timer = null;
        //   import("@/utils/accountUtils").then(({ afterLogout }) => {
        //     afterLogout();
        //   });
        // }, 1000);
        // const msgText = serveResponse?.code == '11003'?serveResponse.message:"登录已失效，即将跳转到登录界面";
        // createMessageError(msgText);
        import("@/utils/accountUtils").then(({ afterLogout }) => {
          afterLogout();
        });
        const msgText = serveResponse?.code == '11003'?serveResponse.message:"登录已失效，即将跳转到登录界面";
        return Promise.reject(msgText);
      } else {
        return Promise.reject(error);
      }
    }
  // },
}; 


const responseErrorStatusHandler: ResponseInterceptor = {
  onRejected(error, options) {
    if (isAxiosError(error)) {
      return Promise.reject(error.message);
    }
    else{
      const { status: httpStatusCode, data } = error;
      if (httpStatusCode == 502 || httpStatusCode == 500 || data?.code == '500' || data?.code == '503') {
        return Promise.reject(`系统异常，请刷新重试!`);
      } else {
        return Promise.reject(data.data || data.message);
      }
    }
  },
};


export default {
  request: [requestHeaderConfigHandler,cryptoHandler],
  response: [
    streamDownloadHandler,
    responseCodeFilterHandler,
    timeoutHandler,
    tokenInvaildHandler,
    responseErrorStatusHandler,
  ], // 响应拦截
};
