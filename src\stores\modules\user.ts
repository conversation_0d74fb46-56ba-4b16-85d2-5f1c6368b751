import { defineStore } from 'pinia'
import { stores } from '@/stores'
import { StoreNamesEnum, UserType } from '@/enum'
import { UserInfoState, UserInfoInterface } from './type'
import { createCacheStorage } from '@/utils/cache/storage'
import { CacheConfig } from '@/config/cacheConfig'

type ObjToKeyValArray<T> = {
  [K in keyof T]: [K, T[K]]
}[keyof T]

export const useUserStore = defineStore(StoreNamesEnum.UserInfo, {
  state: (): UserInfoState => {
    return {
      _token:
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJSSzoiOiIxOTYyNjkyMTAxODQ2NTk3NjMzIiwiZXhwIjoxNzU3MDM2NDM2LCJpYXQiOjE3NTY3NzcyMzZ9.P-QoFsNnALawgjGCkegpVjCpZ_i8tgjajqnOs2k7Zb8',
      /**默认公众号EntityId */
      defaultChannelId: '',
      /** 用户信息 */
      _userInfo: {
        img: '',
        nickname: '',
        mobile: '',
        idNo: '',
        name: '',
        gender: '',
        id: '',
        isAuth: false,
        type: UserType.Member,
      },
    }
  },
  getters: {
    token: (state) => {
      if (!state._token) {
        try {
          const authStorage = createCacheStorage(CacheConfig.Token)
          const _tokenCache = authStorage.get()
          state._token = _tokenCache
        } catch (e) {
          console.error(e)
        }
      }
      return state._token
    },
    /** 是否登录 */
    isLogin(state) {
      return Boolean(state._token)
    },
    /** 用户信息 */
    userInfo: (state): UserInfoInterface => {
      if (!state._userInfo.id) {
        try {
          const userInfoStorage = createCacheStorage(CacheConfig.UserInfo)
          const _userInfoCache = userInfoStorage.get()
          if (_userInfoCache) {
            state._userInfo = _userInfoCache
          }
        } catch (e) {
          console.error(e)
        }
      }
      return state._userInfo
    },
  },
  actions: {
    /**
     * @description 缓存token
     */
    setToken(token: string) {
      this._token = token
      const authStorage = createCacheStorage(CacheConfig.Token)
      authStorage.set(token)
    },
    /**
     * @description 缓存用户信息
     */
    setUserInfo(info: any) {
      const data = {
        ...info,
        isAuth: !!(info.name && info.idNo),
      }
      this._userInfo = { ...data }
      const userInfoStorage = createCacheStorage(CacheConfig.UserInfo)
      userInfoStorage.set(data)
    },
    /**
     * @description Set UserInfoState
     */
    setGlobalUserInfoState(...args: ObjToKeyValArray<UserInfoState>) {
      this.$patch({ [args[0]]: args[1] })
    },
  },
})

export function useUserInfoStoreWithoutSetup() {
  return useUserStore(stores)
}
