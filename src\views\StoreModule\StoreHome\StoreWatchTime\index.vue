<template>
  <div class="store_watch_time_wrapper">
    <!-- 总计 -->
    <div class="store_watch_time_count">
      <span>{{`共${watchTimeList.length}条数据`}}</span>
    </div>
    <JLoadingWrapper :show="isPageLoadingRef" class="store_watch_time_list">
      <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="store_watch_time_list_refresh">
        <template v-if="watchTimeList.length">
          <VanList
            v-model:loading="isLoadingRef"
            :finished="isFinishedRef"
            finished-text="没有更多数据了"
          >
            <StoreWatchTimeCard v-for="item in watchTimeList" :key="item.id" :watchTimeInfo="item" @click="handleWatchTimeClick(item)" />
          </VanList>
        </template>
        <template v-else>
          <EmptyData style="min-height: 400px;" />
        </template>
      </VanPullRefresh>
    </JLoadingWrapper>
    <!-- 观看时长明细 -->
    <StoreWatchTimeDetails v-model:show="showWatchTimeDetailsRef" :watchTimeList="watchTimeDetailsList" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { showToast } from "vant";
import useWatchTime from "./hooks/useWatchTime";
import { isArray } from "@/utils/isUtils";
import { getLiveRoomRecord } from "@/services/storeApi";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreWatchTimeCard from "./components/StoreWatchTimeCard.vue";
import StoreWatchTimeDetails from "./components/StoreWatchTimeDetails.vue";

defineOptions({ name: "StoreWatchTime" });

/** props */
const props = defineProps<{
  userId: string;
}>();

const { 
  isPageLoadingRef, 
  refreshingRef, 
  isLoadingRef, 
  isFinishedRef, 
  watchTimeList, 
  onRefresh, 
  getWatchTimeList 
} = useWatchTime({
  userId: props.userId,
});

/** 观看明细 */
const watchTimeDetailsList = ref([]);
const showWatchTimeDetailsRef = ref(false);
/** 点击 */
async function handleWatchTimeClick(item) {
  try {
    let _params = {
      userId: props.userId,
      watchDate: item.watchDate,
    };
    const resp = await getLiveRoomRecord(_params);
    if (resp && isArray(resp) && resp.length > 0) {
      watchTimeDetailsList.value = resp;
      showWatchTimeDetailsRef.value = true;
    } else if( resp && isArray(resp) && resp.length === 0) {
      showToast("暂无数据");
    }
  } catch (error) {
    console.log("error" + error);
  }
}

/** 组件挂载 */
onMounted(async () => {
  await getWatchTimeList();
});
</script>

<style lang="less" scoped>
.store_watch_time_wrapper {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    .store_watch_time_count {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 12px;
        box-sizing: border-box;
        span {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 15px;
            color: #333333;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
    .store_watch_time_list {
        flex: 1;
        .store_watch_time_list_refresh {
            height: 100%;
            box-sizing: border-box;
            overflow-y: auto;
        }
    }
}
</style>
