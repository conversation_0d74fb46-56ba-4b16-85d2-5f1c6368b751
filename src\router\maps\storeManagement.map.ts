import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";
export const StoreManagement = {
  [RoutesName.StoreWelfareVoucherStatistics]: {
    path: "welfareVoucherStatistics",
    component: () => import("@/views/StoreManagement/WelfareVoucherStatistics/index.vue"),
    meta: {
      title: "福利券统计",
      isMenu: false,
      isShow: false,
    },
  },
  [RoutesName.StoreWelfareVoucherStatisticsDetail]: {
    path: "welfareVoucherStatisticsDetail",
    component: () =>
      import("@/views/StoreManagement/WelfareVoucherStatistics/WelfareVoucherStatisticsDetail/index.vue"),
    meta: {
      title: "福利券统计详情",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({
      couponName: route.query.couponName || "",
      couponId: route.query.couponId || "",
      staffId: route.query.staffId || "",
      csId: route.query.csId || "",
    }),
  },
  [RoutesName.StoreVerificationStatistics]: {
    path: "verificationStatistics",
    component: () => import("@/views/StoreManagement/VerificationStatistics/index.vue"),
    meta: {
      title: "核销统计",
      isMenu: false,
    },
  },

  [RoutesName.StoreSalesStatistics]: {
    path: "salesStatistics",
    component: () => import("@/views/StoreManagement/SalesStatistics/index.vue"),
    meta: {
      title: "销售统计",
      isMenu: false,
    },
    props: (route: RouteLocation) => ({
      storeId: route.query.storeId || "",
    }),
  },
  [RoutesName.StoreShopAssistantStatistics]: {
    path: "shopAssistantStatistics",
    component: () => import("@/views/StoreManagement/ShopAssistantStatistics/index.vue"),
    meta: {
      title: "店员统计",
      isMenu: false,
    },
    props: (route: RouteLocation) => ({
      storeId: route.query.storeId || "",
    }),
  },
  [RoutesName.StoreRanking]: {
    path: "storeRanking",
    component: () => import("@/views/StoreManagement/StoreRanking/index.vue"),
    meta: {
      title: "排行榜",
      isMenu: false,
    },
  },
  [RoutesName.MemberManagement]: {
    path: "memberManagement",
    component: () => import("@/views/StoreManagement/MemberManagement/index.vue"),
    meta: {
      isMenu: false,
      title: "会员管理",
    },
    props: (route: RouteLocation) => ({
      storeId: route.query.storeId || "",
      customerId: route.query.customerId || "",
    }),
  },
  [RoutesName.ViewDuration]: {
    path: "viewDuration",
    component: () => import("@/views/StoreManagement/ViewDuration/index.vue"),
    meta: {
      isMenu: false,
      title: "观看时长",
    },
  },
  [RoutesName.StoreLogistics]: {
    path: "storeLogistics",
    component: () => import("@/views/StoreManagement/StoreLogistics/index.vue"),
    meta: {
      isMenu: false,
      title: "门店物流",
    },
  },
};
