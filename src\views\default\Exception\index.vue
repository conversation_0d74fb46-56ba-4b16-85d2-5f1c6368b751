<template>
  <div v-if='code !== "9006"' class="main-bg exceptionWrapper">
    <div class="infoWrapper">
      <img :src="exceptionInfo.imgSrc" alt="" />
      <div class="notice">{{ exceptionInfo.notice }}</div>
      <div class="notice-sub" v-if="notice || exceptionInfo.subContent">{{ notice || exceptionInfo.subContent }}</div>
      <div class="error-sub" v-if="exceptionInfo.errContent">
        <p>{{ exceptionInfo.errContent }}</p>
      </div>
    </div>
  </div>
  <van-overlay v-if='code == "9006"' :show="true">
    <div style="height: 100%;width: 100%;text-align: center;padding:10px;box-sizing: border-box;">
      <img :src="AuthNoticeSrc" alt="" srcset="" style="height: 100%;max-width: 100%;">
    </div>
    
  </van-overlay>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { toRefs, watch, reactive, ref } from "vue";
import Img404Src from "@/assets/image/exception/404.png";
import Img403Src from "@/assets/image/exception/403.png";
import NoAuthSrc from "@/assets/image/exception/noAuth.png"
import Img9001Src from "@/assets/image/exception/9001.png";
// import Img9003Src from "@/assets/image/exception/9003.png";
import Img9004Src from "@/assets/image/exception/9004.png";
import Img9005Src from "@/assets/image/exception/9005.png";
import AuthNoticeSrc from "@/assets/image/exception/AuthNotice.png"
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { transformMinioSrc } from "@/utils/fileUtils";
import { onMounted } from "vue";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";

const Img9003Src = ''
const Default_Exception_Info = {
  "404": {
    imgSrc: Img9003Src,
    notice: "资源访问权限受限",
    subContent:'建议稍后重新尝试访问'
  },
  "403": {
    imgSrc: NoAuthSrc,
    notice: "经销商账户访问权限已冻结",
    subContent:'请联系客户支持团队处理'
  },
  "9000":{
    imgSrc: Img9003Src,
    notice: "身份验证标识有误",
    subContent:'请联系系统管理员处理'
  },
  "9001":{
    imgSrc: Img9001Src,
    notice: "需完成公众号订阅认证",
  },
  "9002":{
    imgSrc: Img9003Src,
    notice: "用户凭证获取异常",
    subContent:'建议重试或联系技术支持'
  },
  "9003":{
    imgSrc: Img9003Src,
    notice: "访问凭证已失效",
    subContent:'请申请管理员重新签发访问链接'
  },
  "9004":{
    imgSrc: Img9004Src,
    notice: "内容访问时效已终止",
    subContent:'请关注后续活动或申请新访问凭证'
  },
  "9005":{
    imgSrc: Img9005Src,
    notice: "邀请凭证已过期",
    subContent:'请联系管理员更新访问授权'
  },
  "9006":{
    imgSrc: Img9003Src,
    notice: "需激活完整服务授权",
    subContent:'操作异常请留存截图并联系支持团队'
  },
  "9008":{
    imgSrc: Img9003Src,
    notice: "需在企业微信环境访问",
  },
  "9007": {
    imgSrc: Img9003Src,
    notice: "服务功能暂时受限",
    subContent:''
  },
  "9009": {
    imgSrc: Img9003Src,
    notice: "需使用群组管理账户登录",
    subContent:''
  },
};
const errorStorage = createCacheStorage(CacheConfig.Error)
const route = useRoute();
const router = useRouter();
const { notice = "" } = toRefs(route.params);
const { code } = toRefs(route.meta);

const userStore = useUserStoreWithoutSetup()

const exceptionInfo = reactive({
  imgSrc: "",
  notice: "",
  subContent:'',
  errContent:''
});
watch(
  code,
  async(newCode: string) => {
    if(newCode == "9001"){
      const stateCache = createCacheStorage(CacheConfig.State);
      const wxappImg = stateCache.get("wxappImg");
      exceptionInfo.imgSrc = wxappImg?transformMinioSrc(wxappImg as string):Default_Exception_Info[newCode].imgSrc;
      exceptionInfo.notice = wxappImg?'请长按识别上面二维码图片':Default_Exception_Info[newCode].notice;
      exceptionInfo.subContent = wxappImg?'点击前往公众号进行关注':'';
    }
    else if(newCode == '9007'){
      try{
      }
      catch(e){

      }
    }
    else{
      exceptionInfo.imgSrc = Default_Exception_Info[newCode].imgSrc;
      exceptionInfo.notice = Default_Exception_Info[newCode].notice;
      exceptionInfo.subContent = Default_Exception_Info[newCode].subContent || '';
    }
    const errorContentCache = errorStorage.get()
    if(errorContentCache){
      if(!errorContentCache.uploadSuccess){
        exceptionInfo.errContent = `[${errorContentCache.type}-${errorContentCache.id}] ${errorContentCache.message}`
      }
      else{
        exceptionInfo.errContent = `[${errorContentCache.id}]`
      }
      errorStorage.remove()
    }
  },
  {
    immediate: true,
  }
)
</script>

<style scoped lang="less">
@import "@/styles/default.less";
.exceptionWrapper {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  padding: 10px;
  box-sizing: border-box;
  justify-content: center;
  width: 100%;
  img {
    width:80%;
    margin-bottom: 16px;
  }
}
.notice {
  color: #333333;
  line-height: 30px;
  font-size: 20px;
  font-weight: 600;
}
.notice-sub {
  color: #666;
  line-height: 34px;
  font-size: 16px;
}
.error-sub {
  color: #999;
  line-height: 34px;
  font-size: 13px;
  overflow: auto;
  max-height: 150px;
}
.infoWrapper {
  width: 100%;
  text-align: center;
  box-sizing: border-box;
}
</style>
