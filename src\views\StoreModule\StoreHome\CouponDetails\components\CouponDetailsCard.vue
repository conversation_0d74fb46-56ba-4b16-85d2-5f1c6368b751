<template>
  <div class="coupon_wrapper">
    <div class="content">
      <div class="coupon-header">
        <div class="coupon-type">
          <span>{{ couponTypeMap[couponInfoRef?.batchType ?? StoreCouponTypeEnum.COUPON]}}</span>
        </div>
        <div v-if="props.couponStatus == CouponStatusEnum.USED" class="coupon-time">{{ `核销时间：${couponInfoRef?.usedTime ?? '-'}` }}</div>
        <div v-else class="coupon-time">{{ `有效时间：${couponInfoRef?.expiredTime ?? '-'}` }}</div>
      </div>
      <!-- 内容 -->
      <div class="content-container">
        <img :src="CouponSrc" alt="" />
        <div class="coupon-info">
          <div class="coupon-name">{{ couponInfoRef?.categoryName }}</div>
          <div class="coupon-desc">{{`领取时间：${couponInfoRef?.receiveTime ?? '-'}`}}</div>
          <div class="coupon-desc">{{`领取说明：${exchangeNoteMap[couponInfoRef?.sourceType ?? StoreCouponReceiveTypeEnum.LIVE]}`}}</div>
        </div>
      </div>
    </div>
    <div class="coupon-status" :style="{ backgroundColor: couponStatusBgColor }">
      <span :style="{ color: couponStatusTextColor }">{{ couponStatusText }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
import { CouponStatusEnum, StoreCouponReceiveTypeEnum, StoreCouponTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import CouponSrc from "@/assets/storeImage/storeHome/coupon.png";

defineOptions({ name: 'CouponDetailsCard' });

/** props */
const props = withDefaults(defineProps<{
  couponStatus?: CouponStatusEnum; // 券状态
  couponInfo: {
    categoryName?: string; // 券分类名称
    description?: string; // 券描述
    receiveTime?: string; // 领取时间
    sourceType?: StoreCouponReceiveTypeEnum; // 领取方式
    usedTime?: string; // 核销时间
    expiredTime?: string; // 有效时间
    batchType?: StoreCouponTypeEnum; // 福利券类型
  };
}>(), {});

const { couponInfo: couponInfoRef, couponStatus: couponStatusRef } = toRefs(props);

/** 优惠券状态映射 */
const COUPON_STATUS_MAP = {
  [CouponStatusEnum.NOT_USED]: '待使用',
  [CouponStatusEnum.USED]: '已使用',
  [CouponStatusEnum.EXPIRED]: '已失效',
};

/** 状态对应的背景色 */
const STATUS_BG_COLORS = {
  [CouponStatusEnum.NOT_USED]: '#EF1115',
  [CouponStatusEnum.USED]: '#DEDCDA',
  [CouponStatusEnum.EXPIRED]: '#DEDCDA',
};

/** 状态对应的字体颜色 */
const STATUS_TEXT_COLORS = {
  [CouponStatusEnum.NOT_USED]: '#FFFFFF',
  [CouponStatusEnum.USED]: '#999999',
  [CouponStatusEnum.EXPIRED]: '#999999',
};

/** 福利券领取说明 */
const exchangeNoteMap = {
  [StoreCouponReceiveTypeEnum.LIVE]: '直播间领取',
  [StoreCouponReceiveTypeEnum.STORE]: '门店发放',
}

/** 福利券类型 */
const couponTypeMap = {
  [StoreCouponTypeEnum.COUPON]: '福利券',
  [StoreCouponTypeEnum.TIME_COUPON]: '时长券',
}

const couponStatusText = computed(() => COUPON_STATUS_MAP[couponStatusRef.value]);
const couponStatusBgColor = computed(() => STATUS_BG_COLORS[couponStatusRef.value]);
const couponStatusTextColor = computed(() => STATUS_TEXT_COLORS[couponStatusRef.value]);
</script>

<style lang="less" scoped>
.coupon_wrapper {
    width: 100%;
    height: 126px;
    border-radius: 8px;
    display: flex;
    position: relative;
    margin-bottom: 8px;
}
.coupon-status {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 126px;
    padding-left: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    span {
        writing-mode: vertical-rl;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        letter-spacing: 8px;
    }
}
.content {
    position: absolute;
    top: 0;
    left: 0;
    width: calc(100% - 40px);
    height: 100%;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    z-index: 99;

    /* 两侧半圆形缺口效果 */
    -webkit-mask:
        radial-gradient(circle at 0 38px, #0000 8px, red 0),
        radial-gradient(circle at right 38px, #0000 8px, red 0);
    -webkit-mask-size: 51%;
    -webkit-mask-position: 0, 100%;
    -webkit-mask-repeat: no-repeat;

    /* 横穿的虚线 */
    &::after {
        content: "";
        position: absolute;
        top: 38px;
        left: 8px; /* 避开左侧半圆 */
        right: 8px; /* 避开右侧半圆 */
        height: 1px;
        background: repeating-linear-gradient(to right, #ccc 0, #ccc 4px, transparent 4px, transparent 8px);
    }
}
.coupon-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 24px;
    .coupon-type {
        background: #DFEFFF;
        border-radius: 4px;
        padding: 1px 12px;
        box-sizing: border-box;
        span {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 11px;
            color: #4DA4FF;
            line-height: 16px;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
    }
    .coupon-time {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        text-align: center;
        font-style: normal;
        text-transform: none;
    }
}
.content-container {
    height: 76px;
    margin-top: 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    img {
        width: 56px;
        height: 56px;
    }
    .coupon-info {
        flex: 1;
        height: 100%;
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        .coupon-name {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 18px;
            color: #333333;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .coupon-desc {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
}
</style>
