<template>
  <view
    v-if="props.show"
    class="fixed bottom-260rpx right-100rpx z-50 w-68rpx h-68rpx rounded-50% flex items-center justify-center backTop-wrapper"
    @click="toBackTop"
  >
    <van-icon name="back-top" size="40rpx" />
  </view>
</template>

<script lang="ts" setup>
defineOptions({ name: 'JBackTop' })

/** props */
const props = defineProps<{
  show: boolean
}>()

function toBackTop() {
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 500, // 滚动动画的时长
  })
}
</script>

<style lang="scss" scoped>
.backTop-wrapper {
  background-color: #fff;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
</style>
