import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user'
import { getBasicPlatformPrefix } from '@/utils/urlUtils'
import { isObject, isArray, isNullOrUnDef } from '@/utils/isUtils'
import { RequestConfig } from './type'
import { logoutHandler } from '../helper'

/**
 * @description: 请求工具类
 */
export class Request {
  private static instance: Request
  private token: string | null = null
  private options: any

  // 定义 tranfromKeyMap 映射
  private static tranfromKeyMap = {
    'request-no': 'request-No',
  }

  // 创建一个Request实例（单例模式）
  private constructor(options) {
    this.options = { ...options }
    this.loadInterceptors()
  }

  /**
   * @description 单例获取方法
   */
  public static getInstance(options): Request {
    if (!Request.instance) {
      Request.instance = new Request(options)
    }
    return Request.instance
  }

  /**
   * @description store中获取token
   */
  public getToken() {
    if (!this.token) {
      const userInfoStore = useUserInfoStoreWithoutSetup()
      this.token = userInfoStore.token
    }
    return this.token
  }

  /**
   * @description 加载请求拦截器
   */
  private loadInterceptors() {
    uni.addInterceptor('request', {
      // 拦截前触发
      invoke: (args) => {
        try {
          console.log('拦截前触发')
          // token
          const _token = this.getToken()
          // url前缀
          const basicPrefix = getBasicPlatformPrefix()

          // 判断是否需要token
          if (!args.requestConfig?.withToken) {
            args.header[import.meta.env.VITE_TOKEN_NAME] = _token
          }

          // 判断是否传入扩展头信息，合并header
          if (isObject(args.requestConfig?.extendHeaders)) {
            args.header = { ...args.header, ...args.requestConfig?.extendHeaders }
          }

          // 拼接baseUrl
          if (!args.url.includes(basicPrefix)) {
            args.url = `${this.options.baseUrl}${args.url}`
          }
        } catch (error) {
          console.error('Request Error:', error)
        }
      },
    })
  }

  /**
   * @description 处理响应code
   */
  private handleResponseCode(realResponse, resolve, reject) {
    switch (realResponse.code) {
      case '200':
        resolve(realResponse.data)
        break
      case '1001':
        logoutHandler()
        reject(realResponse.message || realResponse.data)
        break
      case '2002':
        if (realResponse.message.includes('令牌过期')) {
          logoutHandler()
        }
        reject(realResponse.message || realResponse.data)
        break
      case '502':
        console.log('网关错误')
        break
      default:
        reject(realResponse.message || realResponse.data)
    }
  }

  /**
   * @description: 请求失败回调
   */
  private handleRequestError(error, reject) {
    reject(error)
  }

  /**
   * @description: 请求方法
   */
  private request<T = any>(
    type = 'GET', // 请求类型
    url = '', // 请求地址
    data = {}, // 请求数据
    options = {}, // 请求选项
    requestConfig: RequestConfig = {}, // 请求配置
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      uni.request({
        url,
        data,
        header: {},
        requestConfig,
        method: type,
        ...Object.assign(this.options, options),
        /**
         * @description 请求成功
         * @param res 请求响应
         */
        success: (res) => {
          const { statusCode, header, data: realResponse } = res
          // 判断状态码
          if (statusCode !== 200) {
            reject(res)
          } else {
            // 处理响应头，方便后续使用
            if (isObject(realResponse.data) && isArray(requestConfig.extendResHeaders)) {
              requestConfig.extendResHeaders.forEach((key) => {
                // 获取响应头中对应键的值，优先使用 tranfromKeyMap 中的映射值
                const headerValue = header[Request.tranfromKeyMap[key]] || header[key]

                // 如果 headerValue 存在，则将其赋值给响应数据的相应键
                if (headerValue) {
                  realResponse.data[key] = headerValue
                }
              })
            }

            // 处理响应code
            this.handleResponseCode(realResponse, resolve, reject)
          }
        },
        /**
         * @description 请求失败
         * @param err 请求失败
         */
        fail: (err) => {
          this.handleRequestError(err, reject)
        },
      })
    })
  }

  /**
   * @description get
   */
  get<T = any>({ url = '', params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>('GET', url, params, option, requestConfig)
  }

  /**
   * @description post
   */
  post<T = any>({ url = '', params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>('POST', url, params, option, requestConfig)
  }

  /**
   * @description put
   */
  put<T = any>({ url = '', params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>('PUT', url, params, option, requestConfig)
  }

  /**
   * @description delete
   */
  delete<T = any>({ url = '', params = {}, option = {}, requestConfig = {} }) {
    return this.request<T>('DELETE', url, params, option, requestConfig)
  }
}
