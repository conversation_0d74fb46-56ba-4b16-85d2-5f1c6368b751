<template>
  <JLoadingWrapper :show="isLoading">
    <div class="setting">
      <div class="userInfo">
        <div class="userInfo-title">个人信息</div>
        <van-cell-group :border="false">
          <van-cell title="我的头像" center :clickable="false">
            <van-image round width="30px" height="30px" :src="storeUserInfo.img || defaultAvatar"></van-image>
          </van-cell>
          <van-cell title="我的昵称">
            <input readonly v-model="storeUserInfo.nickname" maxlength="15" />
          </van-cell>
          <van-cell title="手机号码" :value="storeUserInfo.mobile || '--'"></van-cell>
          <div
            class="cell-warp"
            v-for="(item, index) in agreementMap"
            @click="toAgreement(item.key, item.title)"
            :key="index"
          >
            <van-cell :title="item.title" is-link></van-cell>
            <div class="cell-line"></div>
          </div>
        </van-cell-group>
      </div>
    </div>
    <!-- 退出登录 -->
    <div class="logout_wrapper">
      <VanButton type="danger" block round @click="showDoubleConfirm" style="width: 100%;height: 36px;">
        退出登录
      </VanButton>
    </div>
    <!-- 二次确认 -->
    <JDoubleConfirm v-model:show="showDoubleConfirmRef" tip="确认退出账号？" @confirm="handleConfirm" />
  </JLoadingWrapper>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useBoolean } from "../../hooks";
import { useMessages } from "@/hooks/useMessage";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { getAgreementByKey } from "@/services/storeApi";
import { afterLogout } from "@/utils/accountUtils";
/** 静态资源 */
import defaultAvatar from "@/assets/image/system/account/defaultAvatar.jpg";
/** 相关组件 */
import JDoubleConfirm from "@/views/StoreModule/components/JDoubleConfirm.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

const { bool: showDoubleConfirmRef, setTrue: showDoubleConfirm } = useBoolean();
const { storeToken, storeUserInfo } = useUserStoreWithoutSetup();
const message = useMessages();

const enum SystemPDFKeyEnum {
  Agreement = "sto_user_agreement",
  Policy = "sto_privacy_policy",
  Consent = "sto_informed_consent"
}

const isLoading = ref<boolean>(false);
const agreementMap = computed(() => [
  {
    key: SystemPDFKeyEnum.Agreement,
    title: '用户协议'
  },
  {
    key: SystemPDFKeyEnum.Policy,
    title: '隐私政策'
  }
]);

const toAgreement = async (key: SystemPDFKeyEnum, title: string) => {
  try {
    isLoading.value = true;
    const params = {
      key
    }
    const res = await getAgreementByKey(params)
    const srcEndWith = res.value
    if (!srcEndWith) {
      return message.createMessageInfo('暂无内容');
    }
    // window.open(JSON.parse(srcEndWith).url, '_blank')
    location.href = JSON.parse(srcEndWith).url
  } catch (error) {
    message.createMessageError(error)
  } finally {
    isLoading.value = false;
  }
}

/** 退出登录确认 */
function handleConfirm() {
  afterLogout();
}
</script>

<style scoped lang="less">
.setting {
    padding: 10px;
    box-sizing: border-box;

    .userInfo-title,
    .messageSet-title {
        font-size: 16px;
        font-weight: bold;
        padding: 10px 10px 0 13px;
    }

    input {
        outline: none;
        border: none;
        text-align: right;
    }

    .userInfo {
        background: #fff;
        border-radius: 8px;
        overflow: hidden;

        ::v-deep .van-cell__value {
            color: black;
        }

        ::v-deep .van-cell__title {
            color: black !important;
        }
    }

    .messageSet {
        margin-top: 10px;
        background: #fff;
        border-radius: 8px;
    }

    .footerBtn {
        background: #fff;
        width: calc(100%);
        box-sizing: border-box;
        padding: 0 10px 10px 10px;
        position: fixed;
        bottom: 0px;
        left: 0;
    }
}
.logout_wrapper {
  padding: 12px;
  box-sizing: border-box;
  :deep(.van-button__text) {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
