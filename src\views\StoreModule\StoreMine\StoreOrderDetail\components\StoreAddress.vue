<template>
  <div class="store-address-wrapper">
    <img :src="storeAvatar" alt="门店头像" />
    <!-- 门店信息 -->
    <div class="store-info">
      <div class="store-name">
        <span>{{ storeEntityRef?.storeName }}</span>
        <a class="member-phone" @click.stop :href="storeEntityRef?.contactPhone ? `tel:${storeEntityRef?.contactPhone}` : 'javascript:void(0)'">
          <img :src="phoneSrc" alt="" />
        </a>
      </div>
      <!-- 营业时间 -->
      <div class="store-time">
        <img :src="timeSrc" alt="" />
        <span>{{ storeEntityRef?.businessHours ? `营业时间 ${storeEntityRef?.businessHours}` : '暂无营业时间' }}</span>
      </div>
      <!-- 地址 -->
      <div class="store-address">
        <img :src="addressSrc" alt="" />
        <span>{{ formattedAddress }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
/** 静态资源 */
import storeSrc from "@/assets/storeImage/storeMine/store.png";
import addressSrc from "@/assets/storeImage/storeHome/orderDetails/location.png";
import timeSrc from "@/assets/storeImage/storeHome/orderDetails/time.png";
import phoneSrc from "@/assets/storeImage/storeHome/orderDetails/phone.png";

defineOptions({ name: 'StoreAddress' });

/** props */
const props = defineProps<{
  storeEntity: {
    storeName: null,
    storeAvatar: null,
    province: '',
    city: '',
    area: '',
    addressDetail: '',
    contactPhone: null,
    businessHours: null,
  };
}>();

const { storeEntity: storeEntityRef } = toRefs(props);

/** 门店地址 */
const formattedAddress = computed(() => {
  const { province = '', city = '', area = '', addressDetail = '' } = storeEntityRef.value || {};
  const address = `${province}${city}${area}${addressDetail}`.trim();
  return address || '暂无地址';
});

/** 门店头像 */
const storeAvatar = computed(() => {
  const { storeAvatar } = storeEntityRef.value || {};
  return storeAvatar || storeSrc;
});
</script>

<style lang="less" scoped>
.store-address-wrapper {
    width: 100%;
    border-radius: 8px;
    padding: 8px 12px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #FFFFFF;
    img {
        width: 56px;
        height: 56px;
        border-radius: 4px;
    }
    .store-info {
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        gap: 4px;
        .store-name {
            display: flex;
            align-items: center;
            gap: 4px;
            span {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                text-align: left;
                font-style: normal;
                text-transform: none;
                line-height: 20px;
            }
            img {
                width: 18px;
                height: 18px;
            }
        }
        .store-time,
        .store-address {
            display: flex;
            align-items: center;
            gap: 4px;
            img {
                width: 16px;
                height: 16px;
            }
            span {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                color: #333333;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>
