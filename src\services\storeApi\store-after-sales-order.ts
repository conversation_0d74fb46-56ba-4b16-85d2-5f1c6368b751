import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 售后订单 */
export const enum storeAfterSalesOrderApiEnum {
  // 申请退款
  applyRefund = "/h5/afterSaleRecord/refund",
  // 删除售后记录
  deleteAfterSaleRecord = "/h5/afterSaleRecord/delete",
  // 执行售后动作
  executeAfterSaleAction = "/h5/afterSaleRecord/doAction",
  // 店员/店长执行售后动作
  executeAfterSaleActionByAdmin = "/h5/afterSaleRecord/clerk/doAction",
  // 获取店铺待审核售后单数
  getPendingReviewAfterSaleCount = "/h5/afterSaleRecord/getPendingCount",
}

/**
 * @description 获取店铺待审核售后单数
 */
export function getPendingReviewAfterSaleCountApi(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeAfterSalesOrderApiEnum.getPendingReviewAfterSaleCount),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description  店员/店长执行售后动作（同意申请、拒绝申请、收货并退款）
 */
export function executeAfterSaleActionByAdminApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeAfterSalesOrderApiEnum.executeAfterSaleActionByAdmin),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 客户执行售后动作（填写物流单号、撤销申请、同意申请、拒绝申请）
 */
export function executeAfterSaleActionApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeAfterSalesOrderApiEnum.executeAfterSaleAction),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 申请退款
 */
export function applyRefundApi(_params) {
  return defHttp.put({
    url: getStoreApiUrl(storeAfterSalesOrderApiEnum.applyRefund),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 删除售后记录
 */
export function deleteAfterSaleRecordApi(_params: { recordNo: string }) {
  return defHttp.delete({
    url: getStoreApiUrl(storeAfterSalesOrderApiEnum.deleteAfterSaleRecord),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}
