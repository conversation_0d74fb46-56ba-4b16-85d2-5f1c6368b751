import { isProdEnv, isTestEnv } from '@/utils/envUtils'

/**
 * @description 获取basic-api前缀
 * * @returns url前缀
 */
export function getBasicPlatformPrefix() {
  const isProd = isProdEnv()
  return isProd ? import.meta.env.VITE_PROD_BASIC_BASE_URL : import.meta.env.VITE_DEV_BASIC_BASE_URL
}

/**
 * @description 获取basic-api完整地址
 * * @returns url
 */
export function basicPlatformUrl(url: string) {
  return `${getBasicPlatformPrefix()}${url}`
}

/**
 * @description 获取 URL 前缀
 * @param {string} [url] - 可选的 URL 参数
 * @returns {string} URL 前缀
 */
export function getURLPrefix(url?: string): string {
  // 如果提供了 URL，则返回空字符串
  if (url) {
    return ''
  }

  // 判断当前环境是否为生产环境
  const isProd = isProdEnv()

  // 根据环境选择合适的前缀
  const prefix = isProd
    ? import.meta.env.VITE_PROD_STORE_BASE_URL
    : import.meta.env.VITE_DEV_STORE_BASE_URL

  return prefix // 返回最终的 URL 前缀
}
