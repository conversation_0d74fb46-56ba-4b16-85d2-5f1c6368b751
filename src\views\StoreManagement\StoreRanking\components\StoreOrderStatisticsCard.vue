<template>
  <div class="order-card">
    <div class="order-card__product">
      <img v-if="item.rank==1" :src="ranking_1" alt="" width="32" height="32"/>
      <img v-else-if="item.rank==2" :src="ranking_2" alt="" width="32" height="32"/>
      <img v-else-if="item.rank==3" :src="ranking_3" alt="" width="32" height="32"/>
      <span v-else class="product-name">{{ item.rank }}.</span>
    </div>
    <div class="order-card__quantity">
      <img :src="item.img" alt="" round width="48" height="48" style="border-radius:50%;margin-right: 8px"/>
      <p>{{ item.name }}</p>
    </div>
    <div class="order-card__price">
      {{ item.type===1?(item.totalAmount / 100).toFixed(2):item.orderNum }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs } from 'vue';
import ranking_1 from "@/assets/storeImage/storeHome/ranking_1.png";
import ranking_2 from "@/assets/storeImage/storeHome/ranking_2.png";
import ranking_3 from "@/assets/storeImage/storeHome/ranking_3.png";
defineOptions({ name: 'StoreOrderStatisticsCard' });
/** props */
const props = defineProps<{
  item:{
    img?: string;
    name?: string;
    totalAmount?: string;
    orderNum?: string;
    index?:number,
    type?:number
    rank?:number
  }
}>();

const {item:item} = toRefs(props);

</script>

<style lang="less" scoped>
.order-card {
    display: flex;
    align-items: center;
    height: 64px;
    width: 100%;

    & > div {
        box-sizing: border-box;
        padding: 0 8px;

        span {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 20px;
            color: #333333;
            text-align: left;
            display: block;
        }
    }

    &__product {
        flex:1;
        padding-left: 8px;
        .product-name {
            font-family: YouSheBiaoTiHei;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            height: 100%;
            word-break: break-all;
        }
    }

    &__quantity {
      display:flex;
      flex:2;
      align-items: center;
    }
    &__price{
      flex:1;
      text-align: right;
    }
}
</style>
