<template>
  <VanPopup
    :show="showRef"
    @update:show="handleUpdateShow"
    round
    position="bottom"
    closeable
    safe-area-inset-bottom
    close-icon="close"
    teleport="body"
    style="height: auto;"
  >
    <div class="wrapper">
      <!-- header -->
      <div class="header-container">
        <span class="title">加减积分</span>
      </div>
      <div class="point-contianer">
        <!-- 类型 -->
        <div class="coupon-redeem-content">
          <div class="coupon-redeem-content-item">类型</div>
          <VanRadioGroup v-model="model.changeType" direction="horizontal">
            <VanRadio :name="13" checked-color="#EF1115">
              <span class="radio-title">增加</span>
            </VanRadio>
            <VanRadio :name="14" checked-color="#EF1115">
              <span class="radio-title">减少</span>
            </VanRadio>
          </VanRadioGroup>
        </div>
        <!-- 数量 -->
        <div class="coupon-redeem-content">
          <div class="coupon-redeem-content-item">数量</div>
          <VanStepper
            v-model="model.changeValue"
            theme="round"
            button-size="22"
            :style="{ '--van-stepper-button-round-theme-color': '#EF1115' }"
            integer
            input-width="50px"
          />
        </div>
        <!-- 备注 -->
        <div class="coupon-redeem-content">
          <div class="coupon-redeem-content-item">备注</div>
          <VanField
            v-model="model.reason"
            rows="1"
            autosize
            type="textarea"
            placeholder="请输入加减原因，客户可见"
            :maxlength="200"
            style="flex: 1;background: #F8F8F8;padding: 6px 8px;border-radius: 8px;"
          />
        </div>
      </div>

      <VanButton type="danger" block round @click="handleConfirm">确定</VanButton>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { useUserRole } from "@/views/StoreModule/hooks";
import { useMessages } from "@/hooks/useMessage";
import { addOrSubtractIntegral } from "@/services/storeApi";

defineOptions({ name: 'AddSubtractPoints' });

/** props */
const props = defineProps<{
  show: boolean;
  userId: string;
  unionId: string;
}>();

/** emit */
const emit = defineEmits<{
  (e: 'update:show', val: boolean): void;
  (e: 'success'): void;
}>();

const showRef  = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

const { createMessageError, createMessageSuccess } = useMessages();
const { storeId: storeIdRef } = useUserRole();

const initParams = {
  /** 类型 */
  changeType: 13,
  /** 积分数量 */
  changeValue: 0,
  /** 备注 */
  reason: '',
};
const model = ref({ ...initParams });

function handleUpdateShow(val: boolean) {
  emit('update:show', val);
}

/** 获取参数 */
function getParams() {
  return {
    ...model.value,
    storeId: storeIdRef.value,
    customerId: props.userId,
    unionId: props.unionId
  };
}

async function handleConfirm() {
  try {
    const _params = getParams();
    await addOrSubtractIntegral(_params);
    createMessageSuccess("加减积分成功");
    emit('success');
    handleUpdateShow(false);
  } catch (error) {
    createMessageError("加减积分失败：" + error);
  }
}
</script>

<style lang="less" scoped>
.wrapper {
  padding: 8px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  .header-container {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
  .point-contianer {
    display: flex;
    flex-direction: column;
    gap: 20px;
    .coupon-redeem-content {
      display: flex;
      align-items: center;
      gap: 24px;
      .coupon-redeem-content-item {
        width: 32px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
.radio-title {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  line-height: 20px;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
