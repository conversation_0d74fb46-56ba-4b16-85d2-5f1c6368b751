<template>
  <div class="view-duration-page">
    <JLoadingWrapper :show="isPageLoadingRef">
      <div class="search-bar">
        <div class="search-bar-left">
          <JTag
            v-for="range in dataRangeList"
            class="tag-item"
            :key="range.value"
            @click="handleDataRangeClick(range.value)"
            :type="searchParamsRef.dateType === range.value ? 'primary' : 'inactive'"
          >
            {{ range.label }}
          </JTag>
        </div>
        <div class="screen" @click="handleFilter">
          <span>筛选</span>
          <SvgIcon name="dropDown" style="font-size: 18px;" :class="{'rotate-180': showFilterPopup }" />
        </div>
      </div>
      <div class="total-count">共{{ viewingDurationList.length }}条数据</div>
      <VanPullRefresh class="content-wrapper" v-model="refreshingRef" @refresh="onRefresh">
        <VanList
          v-if="viewingDurationList.length"
          v-model:loading="loadingRef"
          @load="onLoad"
          :finished="isFinishedRef"
          finished-text="没有更多了"
        >
          <ListCard
            v-for="item in viewingDurationList"
            :data="item"
            @click="handelWatchLog(item)"
          />
        </VanList>
        <EmptyData v-else />
      </VanPullRefresh>
    </JLoadingWrapper>
  </div>

  <!-- 筛选 -->
  <TagSearchPopup v-model:show="showFilterPopup" @update:value="updateSearch" />
  <!-- 明细 -->
  <WatchHistoryPopup
    v-model:show="showHistoryPopup"
    v-if="showHistoryPopup"
    :params="historyParams"
    :memberName="memberName"
    :memberId="memberId"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { useUserRole, usePaginatedFetch } from "@/views/StoreModule/hooks";
import { useMessages } from "@/hooks/useMessage";
import { getViewDurationPage } from "@/services/storeApi";
/** 相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import ListCard from "./components/ListCard.vue";
import TagSearchPopup from "./components/TabSearchPopup.vue";
import WatchHistoryPopup from "./components/WatchHistoryPopup.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

const enum DataRangeValEnum {
  today = 1,
  yesterday = 2,
  this_week = 3,
  this_month = 4,
  all = 5,
}

const { storeId } = useUserRole();
/** 分页数据加载Hook */
const {
  isPageLoadingRef,
  setPageLoadingTrue,
  setPageLoadingFalse,
  refreshingRef,
  loadingRef,
  isFinishedRef,
  searchParamsRef,
  pageListRef: viewingDurationList,
  initPageDataRequest: initViewingDurationList,
  onLoad,
  onRefresh,
} = usePaginatedFetch({
  fetchApi: getViewDurationPage,
  searchParams: {
    dateType: 1,
    csShortId: "",
    staffShortId: "",
  },
  beforeRequest: (params) => {
    return new Promise((resolve) => {
      resolve({
        ...params,
        data: {
          ...params.data,
          storeId: storeId.value
        }
      });
    });
  }
});


const memberName = ref('')
const memberId = ref('')
const dataRangeList = [
  {
    label: "今日",
    value: DataRangeValEnum.today,
  },
  {
    label: "昨日",
    value: DataRangeValEnum.yesterday,
  },
  {
    label: "本周",
    value: DataRangeValEnum.this_week,
  },
  {
    label: "本月",
    value: DataRangeValEnum.this_month,
  },
];

const showFilterPopup = ref(false);
const showHistoryPopup = ref(false);
const historyParams = ref({})
const handleFilter = () => {
  showFilterPopup.value = true;
};

const handleDataRangeClick = (value: DataRangeValEnum) => {
  searchParamsRef.value.dateType = value;
  handleSearch();
};

const updateSearch = data => {
  searchParamsRef.value.staffShortId = data.staffShortId;
  searchParamsRef.value.csShortId = data.csShortId;
  handleSearch();
};

const handleSearch = async () => {
  setPageLoadingTrue();
  /** 初始化 */
  await initViewingDurationList();
  setPageLoadingFalse();
};

const handelWatchLog =(data)=>{
  showHistoryPopup.value = true
  memberId.value = data.memberShortId
  memberName.value = data.memberName
  historyParams.value ={
    csId:data.memberId,
    dateType: searchParamsRef.value.dateType
  }
}

/** 组件挂载 */
onMounted(async () => {
  setPageLoadingTrue();
  /** 初始化 */
  await initViewingDurationList();
  setPageLoadingFalse();
});
</script>

<style lang="less" scoped>
.view-duration-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  .search-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background-color: #fff;
    padding: 10px;
    box-sizing: border-box;
    .search-bar-left {
      flex: 1;
      .j-tag {
        width: 15%;
        margin-right: 15px;
        font-size: 12px;
      }
    }
    .screen {
      display: flex;
      align-items: center;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      text-align: right;
      font-style: normal;
      text-transform: none;
      .rotate-180 {
        transform: rotate(-180deg);
      }
    }
  }
  .total-count {
    padding: 12px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 15px;
    color: #666666;
    line-height: 18px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  :deep(.j-loading-wrapper-content) {
    overflow: hidden;
  }
  .content-wrapper {
    width: calc(100% - 12px * 2);
    height: calc(100% - 36px - 34px - 24px);
    overflow-y: scroll;
    margin-top: 12px;
    padding: 0 12px;
  }
}
</style>
