import { ref, reactive } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { getLiveRoomData } from "@/services/storeApi";

export default function useWatchTime(_params: {
  userId: string;
}) {
  const isPageLoadingRef = ref(false);
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 观看时长数据 */
  const watchTimeList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);

  /** 刷新 */
  function onRefresh() {
    refreshingRef.value = true;
    getWatchTimeList();
    refreshingRef.value = false;
  }

  /** 获取用户观看数据 */
  async function getWatchTimeList() {
    try {
      const res = await getLiveRoomData({
        userId: _params.userId,
      });
      if (res) {
        watchTimeList.value = res;
        isFinishedRef.value = true;
      }
    } catch (error) {
      createMessageError("获取观看时长失败：" + error);
    }
  }

  return {
    isPageLoadingRef,
    watchTimeList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onRefresh,
    getWatchTimeList,
  };
}
