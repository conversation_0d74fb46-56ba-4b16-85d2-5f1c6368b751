import { ref } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { getMyAfterSaleOrderDetail } from "@/services/storeApi";

export default function useGetAfterSales(_params: { recordNo: string }) {
  const isPageLoadingRef = ref(false);
  const { createMessageSuccess, createMessageError } = useMessages();
  /** 售后单号 */
  const recordNoRef = ref<string>(_params.recordNo);
  /** 售后详情 */
  const afterSalesOrderDetailRef = ref<any>();

  /** 获取售后详情 */
  async function getAfterSalesDetail() {
    try {
      isPageLoadingRef.value = true;
      // TODO: 调用接口获取售后详情
      const _params = {
        recordNo: recordNoRef.value,
      };
      const resp = await getMyAfterSaleOrderDetail(_params);
      if (resp) {
        afterSalesOrderDetailRef.value = resp;
      }
      isPageLoadingRef.value = false;
    } catch (error) {
      createMessageError("获取售后详情失败：" + error);
    }
  }

  return {
    isPageLoadingRef,
    afterSalesOrderDetailRef,
    getAfterSalesDetail,
  };
}
