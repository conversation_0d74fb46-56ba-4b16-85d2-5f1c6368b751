import { ref, computed } from "vue";
import { showToast } from "vant";
import { LogisticsStatusEnum } from "@/views/StoreModule/enums";
import { isArray } from "@/utils/isUtils";
import { queryOrderLogistics } from "@/services/storeApi";

interface ShipTrace {
  orderCode?: string;
  trackingNo?: string;
  shipCompanyCode?: string;
  state?: LogisticsStatusEnum;
  acceptStation?: string;
  acceptTime?: string;
  location?: string;
  station?: string;
  stationTel?: string;
  stationAdd?: string;
  deliveryMan?: string;
  deliveryManTel?: string;
}

export default function useGetShipTraces() {
  /**
   * @description 物流状态映射表
   */
  const logisticsStatusMap = {
    [LogisticsStatusEnum.NO_TRACKING]: "暂无轨迹信息",
    [LogisticsStatusEnum.COLLECTED]: "已揽收",
    [LogisticsStatusEnum.IN_TRANSIT]: "运输中",
    [LogisticsStatusEnum.DELIVERED]: "已签收",
    [LogisticsStatusEnum.PROBLEM]: "问题件",
    [LogisticsStatusEnum.FORWARDED]: "转寄中",
    [LogisticsStatusEnum.CUSTOMS]: "清关中",
  };
  /** 物流信息 */
  const shipTracesList = ref<ShipTrace[] | null>(null);

  /** 最新一条物流信息 */
  const latestShipTrace = computed(() => {
    if (isArray(shipTracesList.value)) {
      return shipTracesList.value[0];
    }
    return null;
  });

  /** 获取物流信息 */
  const getShipTraces = async (_params: { orderCode: string; trackingNo: string; shipCompanyCode: string }) => {
    try {
      const resp = await queryOrderLogistics(_params);
      if (isArray(resp) && resp?.length > 1) {
        shipTracesList.value = resp;
      } else {
        shipTracesList.value = null;
      }
    } catch (error) {
      showToast(`获取物流信息失败`);
    }
  };

  return {
    shipTracesList,
    getShipTraces,
    latestShipTrace,
    logisticsStatusMap
  };
}
