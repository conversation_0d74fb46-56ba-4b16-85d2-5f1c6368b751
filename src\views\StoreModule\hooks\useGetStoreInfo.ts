import { ref } from "vue";
import { StoreStatusEnum } from "@/views/StoreModule/enums";
import { getStoreBasicInfo } from "@/services/storeApi";

export default function useGetStoreInfo() {
  /** 门店基础信息 */
  const storeInfo = ref({
    storeName: null,
    storeAvatar: null,
    storeStatus: StoreStatusEnum.NORMAL,
    province: "",
    city: "",
    area: "",
    addressDetail: "",
    contactPhone: null,
    contactName: null,
  });

  /** 获取门店基础信息 */
  async function getStoreInfoByStoreId(storeId: string) {
    // TODO: 获取门店基础信息
    try {
      const _params = { id: storeId };
      const resp = await getStoreBasicInfo(_params);
      if (resp) {
        Object.assign(storeInfo.value, resp);
      }
    } catch (error) {
      console.log(error);
    }
  }

  return {
    storeInfo,
    getStoreInfoByStoreId,
  }
}
