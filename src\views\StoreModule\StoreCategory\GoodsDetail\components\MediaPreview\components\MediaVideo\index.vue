<template>
    <div class="video-box" @click.stop="handleClickVideo">
        <video :src="src" ref="videoRef" webkit-playsinline='true' crossorigin="anonymous" playsinline='true'
            :controls="false"></video>
        <div class="opt-box" @click="changePlayStatus">
            <div class="play-icon">
                <van-icon name="play-circle-o" v-if="!isPlay" size="60px" color="#fff" />
            </div>
            <div class='opt-bottom' @touchmove.stop="()=>{}" v-if="isShowBottom">
                <div class="time-box">
                    <span>{{ nowTimeFormatRef }}</span>
                    <div class="silder-line">
                        <van-slider @change="sliderChange" :max="sliderMAX" :model-value="mediaReactive.sliderValue"
                            inactive-color="#A8A8A8" active-color="#FFEE83" button-size="16px" bar-height="8px" />
                    </div>
                    <span>{{ totalTimeFormatRef }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, computed } from "vue";
const props = withDefaults(defineProps<{
    src: string,
    isShowBottom?: boolean
}>(), {
    src: '',
    isShowBottom: true
})
const videoRef = ref<HTMLVideoElement>(null)
const sliderMAX = 100
const isPlay = ref<boolean>(false)
const mediaReactive = reactive({
    currentTime: 0,
    duration: 0,
    sliderValue: 0,
})
const totalTimeFormatRef = computed(() => {
    const totalSeconds = mediaReactive.duration
    const minutes = `${Math.floor(totalSeconds / 60).toFixed(0)}`.padStart(2, `0`)
    const seconds = `${(totalSeconds % 60).toFixed(0)}`.padStart(2, `0`)
    return `${minutes}:${seconds}`
})
const nowTimeFormatRef = computed(() => {
    const nowSeconds = mediaReactive.currentTime
    const minutes = `${Math.floor(nowSeconds / 60).toFixed(0)}`.padStart(2, `0`)
    const seconds = `${(nowSeconds % 60).toFixed(0)}`.padStart(2, `0`)
    return `${minutes}:${seconds}`
})
const handleClickVideo = () => { }
//生成唯一id
const timeupdateChange = (e) => {
    const duration = e.target.duration
    const currentTime = e.target.currentTime
    let sliderValue = currentTime / duration * sliderMAX;
    mediaReactive.sliderValue = sliderValue;
    mediaReactive.currentTime = currentTime
}
const sliderChange = (_value: number) => {
    if (mediaReactive.duration) {
        const value = _value / sliderMAX * mediaReactive.duration
        mediaReactive.sliderValue = _value;
        seek(value)
    }
}
const loadedmetadata = (e) => {
    mediaReactive.duration = e.target.duration
    mediaReactive.currentTime = e.target.currentTime || 0
}
const getCurrentTime = () => {
    return mediaReactive.currentTime
}

const changePlayStatus = () => {
    isPlay.value = !isPlay.value
    if (isPlay.value) {
        videoRef.value.play()
    } else {
        videoRef.value.pause()
    }
}
const seek = (time: number) => {
    videoRef.value.currentTime = time
}
const pause = () => {
    isPlay.value = false
    videoRef.value.pause()
}
const play = () => {
    isPlay.value = true
    videoRef.value.play()
}
const ended = (e) => {
    seek(0)
    pause()
}
const init = () => {
    mediaReactive.currentTime = 0
    mediaReactive.sliderValue = 0
    isPlay.value = false
    seek(0)
}
const initEvent = () => {
    videoRef.value.addEventListener('timeupdate', timeupdateChange)
    videoRef.value.addEventListener('loadedmetadata', loadedmetadata)
    videoRef.value.addEventListener('ended', ended)
}
const removeEvent = () => {
    videoRef.value?.removeEventListener('timeupdate', timeupdateChange)
    videoRef.value?.removeEventListener('loadedmetadata', loadedmetadata)
    videoRef.value?.removeEventListener('ended', ended)
}
onMounted(() => {
    initEvent()
})
onUnmounted(() => {
    removeEvent()
})
defineExpose({
    play,
    seek,
    pause,
    init,
    getCurrentTime,
})
</script>

<style scoped lang="less">
.video-box {
    height: 100%;
    width: 100%;
    position: relative;

    video {
        width: 100%;
        height: 100%;
    }

    .opt-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;

        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .opt-bottom {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 10px;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(0, 0, 0, 0.5);

            .time-box {
                box-sizing: border-box;
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #fff;
                flex: 1;

                .silder-line {
                    flex: 1;
                    margin: 0 10px;
                    box-sizing: border-box;
                }
            }
        }
    }
}
</style>