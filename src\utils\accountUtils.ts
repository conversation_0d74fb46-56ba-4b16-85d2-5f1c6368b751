import { loadRoutes, mergeRoutesConfig, getRoutesMapByConfig, getFirstChildrenRoute } from "./routerUtils";
import { routesMap } from "@/router/maps/index";
import { baseRoutesConfig } from "@/router/config/base.config";
import {AdminRoutesConfig} from "@/router/config/sync/admin.config";
import {AgentRoutesConfig} from "@/router/config/sync/agent.config";
import { scrmRoutesConfig } from "@/router/config/sync/scrm.config";
import { clearStorage, createCacheStorage } from "@/utils/cache/storageCache";
import type { Router } from "vue-router";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { RoutesName } from "@/enums/routes";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { useMessages } from "@/hooks/useMessage";
import { RoleTypeEnum } from "@/enums/role";
import { MemberRoutesConfig } from "@/router/config/sync/member.config";
import { isInFrame, isProdEnv } from "./envUtils";
import { getImgUrlPrefix, parseUrlParams } from "./http/urlUtils";
import { CacheConfig } from "./cache/config";
import { SystemVarEnum } from "@/enums/systemVar";
import { QWAuthTypeEnum } from "./wxUtils";
import { MessageEventEnum, useWindowMessage } from "@/hooks/useWindowMessage";
const {sendMessageToWindows} = useWindowMessage()
export function afterLogout() {
  if (isInFrame()) {
    let webAuthParams
    const params = parseUrlParams(location.search)
    const stateCache = createCacheStorage(CacheConfig.State);
    let _cacheAppId = stateCache.get("appId");
    const _stateInfo = stateCache.get();
    if (_stateInfo.corpId && _stateInfo.agentId) {
      webAuthParams = {
        type: QWAuthTypeEnum.QW,
        appid: _stateInfo.corpId,
        state: params.state as string,
        agentId: _stateInfo.agentId
      }

    }
    else {
      webAuthParams = {
        type: QWAuthTypeEnum.WX,
        appid: _cacheAppId,
        state: params.state as string
      }
      
    }
    sendMessageToWindows(MessageEventEnum.Login, webAuthParams)
    clearLoginStatus()
  }
  else{
    clearLoginStatus()
    location.href = location.href.replace(/code=.*&state/,'state');
  }
}

export function clearLoginStatus(){
  const userStore = useUserStoreWithoutSetup();
  const { destoryMessage } = useMessages();
  destoryMessage();
  userStore.$reset();
  clearStorage();
}


type AfterLoginProps = {
  router: Router;
};

export async function afterLogin({ router }: AfterLoginProps) {
  const userStore = useUserStoreWithoutSetup();
  const systemStore = useSystemStoreWithoutSetup()
  let _syncRouteConfig = [];
    const {type} = userStore.userInfo
    const stateCache = createCacheStorage(CacheConfig.State);
    const _stateInfo = stateCache.get();
    // if(systemStore.entryUrl.indexOf('scrm') !=-1 && type == RoleTypeEnum.Admin){
    //   _syncRouteConfig = scrmRoutesConfig
    // }
    if(type == RoleTypeEnum.Admin){
      _syncRouteConfig = AdminRoutesConfig;
      // if(systemStore.entryUrl.indexOf('scrm') !=-1 && _stateInfo.corpId && _stateInfo.agentId){
      //   _syncRouteConfig = [
      //     ..._syncRouteConfig,
      //     ...scrmRoutesConfig
      //   ]
      // }
      if(_stateInfo.corpId && _stateInfo.agentId){
        _syncRouteConfig = [
          ..._syncRouteConfig,
          ...scrmRoutesConfig
        ]
      }
    }
    else if(type == RoleTypeEnum.Dealer){
      _syncRouteConfig = AgentRoutesConfig;
    }
    else if(type == RoleTypeEnum.Member){
      _syncRouteConfig = MemberRoutesConfig;
    }
  const _config = mergeRoutesConfig(_syncRouteConfig, baseRoutesConfig);
  userStore.setRouteConfig(_config);
  const routes = getRoutesMapByConfig(_config, routesMap);
  loadRoutes(routes, router);
  // const systemStore = useSystemStoreWithoutSetup();
  systemStore.setImgPrefix(getImgUrlPrefix());
  // const tenantRootRoute = routes.find(route=>route.name == RoutesName.Root && route.children && route.children.length)
  // router.replace(getFirstChildrenRoute(tenantRootRoute.children));
}

