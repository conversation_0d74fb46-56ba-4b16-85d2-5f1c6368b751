<template>
    <div class="store-live-streaming" @click="handleClick">
        <img :src="storeLiveStreaming" alt="" />
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
/** 静态资源 */
import storeLiveStreaming from "@/assets/storeImage/storeHome/storeLiveStreaming.png";

defineOptions({ name: 'StoreLiveStreaming' });

/** props */
const props = defineProps<{
    liveRoomLink: string;
}>();

function handleClick() {
    window.open(props.liveRoomLink);
}
</script>

<style lang="less" scoped>
.store-live-streaming {
    margin-top: 12px;
    height: 118px;
    cursor: pointer;
    img {
        width: 100%;
        height: 100%;
    }
}
</style>