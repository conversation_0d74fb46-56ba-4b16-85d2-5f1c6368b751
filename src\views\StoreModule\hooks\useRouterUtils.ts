import { RoutesName } from "@/enums/routes";
import { useRouter, useRoute } from "vue-router";
import { routesMap } from "@/router/maps";

/**
 * @description 路由工具
 */
export default function useRouterUtils() {
  const router = useRouter();
  const route = useRoute();

  /**
   * @description 跳转页面
   * @param name 页面名称
   * @param props 页面参数
   */
  function routerPushByRouteName(type: RoutesName, props?: Record<string, string | number>) {
    const _routeInfo = routesMap[type];
    _routeInfo.query = {
      ...route.query,
      ...props,
    };
    router.push(_routeInfo);
  }

  /**
   * @description 返回上一页
   */
  function routerBack() {
    router.back();
  }

  return {
    routerPushByRouteName,
    routerBack,
  };
}
