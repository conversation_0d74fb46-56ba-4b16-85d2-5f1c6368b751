<template>
	<div class="shop-price">
		<div class="price-icon" :style="pricePreStyle" v-if="isPrefix">{{ prefix }}</div>
		<div class="price-icon" :style="pricePreStyle">￥</div>
		<div class="price-warp" :style="priceStyle" v-if="isAutoComputed">
			<template v-if="priceInfo.max > 0">
				{{ `${priceInfo.min.toFixed(remainder)}~${priceInfo.max.toFixed(remainder)}` }}
			</template>
			<template v-else>
				{{ priceInfo.min.toFixed(remainder) }}
			</template>
		</div>
		<template v-else>
            <div class="price-warp" v-if="!isSplitUnit" :style="priceStyle" >
				{{ _price }}
			</div>
			<template v-else>
				<div class="price-warp" :style="priceStyle" >
					{{ priceStr }}
				</div>
				<div class="price-icon" :style="pricePreStyle">{{ remainderStr }}</div>
			</template>
			<!-- 划线价 -->
			<template v-if="marketPriceStr">
				<span class="store_commodity_item_original_price">
					{{ `￥${marketPriceStr}` }}
                </span>
			</template>
		</template>
		<div class="price-suffix" v-if="isSuffix">{{ suffix }}</div>
	</div>
</template>

<script setup lang="ts">
import { isNumber } from "@/utils/isUtils";
import { computed, type StyleValue } from "vue"
interface Info {
	max: number;
	min: number;
}
const props = withDefaults(defineProps<{
	//规格信息
	skuInfo?: any;
	//自定义插槽默认值的样式
	customSlotDefaultStyle?: StyleValue;
	//规格列表 用于自动计算价格
	appletProductSpecDTOList?: any[],
	//自定义价格
	price?: number,
	// 划线价
	marketPrice?: number,
	//是否自动计算价格 多规格显示最小~最大的价格区间
	isAutoComputed?: boolean,
	//是否分转为元
	isCut: boolean,
	//保留小数点
	remainder?: number,
	//价格字体大小
	priceFontSize?: number | string,
	customPriceStyle?: StyleValue,
	pricePreFontSize?: number | string,
	customPrePriceStyle?: StyleValue,
	//价格后缀
	suffix?: string,
	isSuffix?: boolean,
	//自动计算获取的价格key
	priceKey?: 'price' | 'minPrice',
	isPrefix?: boolean,
	prefix?: string,
	//是否分割单位
	isSplitUnit?: boolean,
}>(), {
	skuInfo: () => ({}),
	customStyle: '',
	customSlotDefaultStyle: '',
	customPriceStyle: '',
	appletProductSpecDTOList: () => ([]),
	price: 0,
	marketPrice: 0,
	isAutoComputed: false,
	isCut: true,
	remainder: 2,
	priceFontSize: 20,
	suffix: '起',
	isSuffix: false,
	pricePreFontSize: 12,
	customPrePriceStyle: '',
	usePriceSlot: false,
	priceKey: 'price',
	isPrefix: false,
	prefix: '',
	isSplitUnit: true,
})
const priceInfo = computed<Info>(() => {
	const priceList: number[] = props.appletProductSpecDTOList?.map(item => item.price) || []
	const result = {
		max: 0,
		min: 0
	}
	if (props.appletProductSpecDTOList && props.appletProductSpecDTOList.length) {
		if (props.appletProductSpecDTOList.length > 1) {
			result.max = Math.max(...priceList)
		}
		result.min = Math.min(...priceList)
		if (props.isCut) {
			result.max = result.max / 100
			result.min = result.min / 100
		}
	}
	return result
})
const _price = computed<number>(() => {
	let price = props.skuInfo[props.priceKey] || props.price
	let result = props.isCut ? price / 100 : price
	return result.toFixed(props.remainder)
})
const priceStr = computed(() => {
	const str = _price.value.toString()
	const index = str.indexOf('.')
	if (index > -1) {
		return str.substring(0, index)
	}
	return str
})
const remainderStr = computed(() => {
	const str = _price.value.toString()
	const index = str.indexOf('.')
	if (index > -1) {
		return '.' + str.substring(index + 1)
	}
	return ''
})
const priceStyle = computed<StyleValue>(() => {
	return [
		{
			fontSize: isNumber(props.priceFontSize) ? `${props.priceFontSize}px` : props.priceFontSize,
		},
		props.customPriceStyle,
	]
})
const pricePreStyle = computed<StyleValue>(() => {
	return [
		{
			fontSize: isNumber(props.pricePreFontSize) ? `${props.pricePreFontSize}px` : props.pricePreFontSize,
		},
		props.customPrePriceStyle,
	]
})

/** 将分转换为元并格式化显示 */
const formatPrice = (priceInCents: number) => {
  return (priceInCents / 100).toFixed(2);
};

/**划线价 */
const marketPriceStr = computed(() => {
	if (!props.marketPrice) {
		return null;
	}
    return formatPrice(props.marketPrice);
});
</script>

<style lang="less" scoped>
@import '@/styles/storeVar.less';

.shop-price {
	display: flex;
	align-items: baseline;
	color: @error-color;
	font-size: 12px;
	flex-wrap: wrap;
	font-family: "DINPro";
	font-weight: bold;

	.price-icon,
	.price-warp {
		display: inline-block;
	}

	.price-warp {
		word-break: break-all;
		font-size: 20px;
	}

	.price-suffix {
		color: #000;
	}
	.store_commodity_item_original_price {
        font-family: DIN Pro, DIN Pro;
        font-weight: 400;
        font-size: 15px;
        color: #999999;
        line-height: 32px;
        text-align: left;
        font-style: normal;
        text-decoration-line: line-through;
        text-transform: none;
		margin-left: 4px;
    }
}

</style>