import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import { storeWithdrawRecordApi } from "@/services/storeApi";

export default function useStoreWithdrawRecord() {
  const isPageLoadingRef = ref(false);
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 提现记录数据 */
  const storeWithdrawRecordList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 初始化参数 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    return {
      data: {},
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取提现记录列表数据 */
  async function getStoreWithdrawRecordList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await storeWithdrawRecordApi(_params);

      // 更新订单列表
      if (current === 1) {
        storeWithdrawRecordList.value = records;
      } else if (records.length) {
        storeWithdrawRecordList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 数据初始化 */
  async function initStoreWithdrawRecordList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreWithdrawRecordList();
    isPageLoadingRef.value = false;
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getStoreWithdrawRecordList();
    }
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    initStoreWithdrawRecordList();
  }

  return {
    isPageLoadingRef,
    storeWithdrawRecordList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onRefresh,
    onLoad,
    initStoreWithdrawRecordList,
  };
}
