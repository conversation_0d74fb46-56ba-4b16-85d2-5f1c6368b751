export default function useNumberToChinese() {
    const numberToChinese = (num :number )=> {
        const units = ['', '十', '百', '千', '万', '亿'];
        const chars = '零一二三四五六七八九';
      
        if (num === 0) return chars[0];
      
        let str = '';
        let unitPos = 0;
        let zero = true;
      
        while (num > 0) {
          const digit = num % 10;
          if (digit === 0) {
            if (!zero) {
              zero = true;
              str = chars[digit] + str;
            }
          } else {
            zero = false;
            str = chars[digit] + units[unitPos] + str;
          }
          unitPos++;
          num = Math.floor(num / 10);
        }
      
        // 处理十的特殊情况
        if (str.startsWith('一十')) {
          str = str.slice(1);
        }
      
        return str;
      }
      return {
        numberToChinese
      }
  }
  