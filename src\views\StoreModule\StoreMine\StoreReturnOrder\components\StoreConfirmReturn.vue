<template>
  <VanPopup
    v-model:show="showRef"
    @close="emit('update:show', false)"
    :style="{ borderRadius: '16px', background: '#fff', padding: '12px', width: '80vw' }"
    :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)' }"
  >
    <div class="wrapper">
        <p class="title">提示</p>
        <p class="subTitle">确定后锁定订单</p>
        <!-- 确定 -->
        <VanButton type="danger" @click="handleConfirm" block style="width: 100%;height: 40px;margin-bottom: 16px;">确定退货</VanButton>
        <!-- 确定并填写物流信息 -->
        <VanButton type="default" @click="handelConfirmAndFill" block style="width: 100%;height: 40px;margin-bottom: 16px;">确定并填写物流信息</VanButton>
        <!-- 取消 -->
        <span class="cancel" @click="handleCancel">取消</span>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed } from "vue";
defineOptions({ name: 'StoreConfirmReturn' });

/** props */
const props = withDefaults(defineProps<{
    show: boolean;
}>(), {
});

/** emit */
const emit = defineEmits<{
    (e: 'update:show', show: boolean): void;
    /** 确定 */
    (e: 'confirm'): void;
    /** 确定并填写物流信息 */
    (e: 'confirmAndFillLogistics'): void;
    /** 取消 */
    (e: 'cancel'): void;
}>();

const showRef = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

function handleConfirm() {
  emit('confirm');
  emit('update:show', false);
}

function handelConfirmAndFill() {
  emit('confirmAndFillLogistics');
  emit('update:show', false);
}

function handleCancel() {
  emit('cancel');
  emit('update:show', false);
}
</script>

<style lang="less" scoped>
.wrapper {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        line-height: 32px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: 18px;
        margin-bottom: 16px;
    }
    .subTitle {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #333333;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-bottom: 60px;
    }
    .cancel {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>
