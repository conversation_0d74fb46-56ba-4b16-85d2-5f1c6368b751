import { computed} from "vue";
import { StoreUserTypeEnum, OrgIdentityTypeEnum } from "@/views/StoreModule/enums";
import { useUserStore } from "@/stores/modules/user";

export default function useUserRole() {
  const userStore = useUserStore();
  /** 是否为店长 */
  const isStoreOwner = computed(() => userStore.storeUserInfo?.type === StoreUserTypeEnum.OWNER);

  /** 是否为店员 */
  const isStoreStaff = computed(() => userStore.storeUserInfo?.type === StoreUserTypeEnum.STAFF);

  /** 是否为会员 */
  const isStoreMember = computed(() => userStore.storeUserInfo?.type === StoreUserTypeEnum.CUSTOMER);

  /** 是否经销商 */
  const isDealer = computed(() => userStore.storeUserInfo?.structIdentityType == OrgIdentityTypeEnum.DISTRIBUTOR);

  /** 归属店员/店长ID */
  const staffId = computed(() => userStore.storeUserInfo?.staffId);
  
  /** 归属店铺ID */
  const storeId = computed(() => userStore.storeUserInfo?.storeId); 

  return {
    isStoreOwner,
    isStoreStaff,
    isStoreMember,  
    staffId,
    isDealer,
    storeId
  }
}
