export const enum StorePageEnum {
    /**商城列表 */
    GOODSLIST = 1,
    /**商品详情 */
    GOODSDETAIL,
    /**确认订单 */
    CONFIRMORDER,
    /**地址列表 */
    ADDRESSLIST,
    /**编辑地址 */
    EDITADDRESS,
}

export const enum GoodsTypeEnum{
    /**otc药品 */
    OTC_DRUG = 1,
    /**疗法 */
    THERAPY,
    /**普通商品 */
    COMMON_GOODS,
}

export const enum GoodsExistIntegralEnum{
    /**不存在积分商品 */
    NotExist,
    /**存在积分商品 */
    Exist,
}

export const enum MediaTypeEnum {
    /**图片 */
    Image = 0,
    /**视频 */
    Video = 1,
}

export const enum StoreGoodsEnum {
    /**商品 */
    Goods,
    /**积分商品 */
    IntegralGoods,
    /**福利卷 */
    WelfareTicket,
}

export const enum StatisticsEnum{
    /**店长 */
    StoreManager = 1,
    /**店员 */
    ShopAssistant,
    /**会员 */
    Member,
}

export const enum PayTypeEnum {
    /**在线支付 */
    OnlinePay = 1,
    /**物流代收 */
    LogisticsPay,
    /**定价支付 */
    EarnestPay,
    /**积分支付 */
    PointPay,
    /**现金和积分支付 */
    PointAndOnlinePay
}