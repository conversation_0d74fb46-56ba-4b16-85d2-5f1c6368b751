<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <div class="store_file_download_wrapper">
      <div class="wrapper">
        <p class="title">文件下载</p>
        <p class="subTitle">{{`文件名：${fileNameRef}`}}</p>
        <!-- 确定 -->
        <VanButton type="danger" @click="handleDownloadFile" style="width: 180px;height: 40px;margin-bottom: 16px;">
          <template #icon>
            <SvgIcon name="download" style="font-size: 22px;" />
          </template>
          点击下载
        </VanButton>
        <!-- 取消 -->
        <span class="copylink" @click="handleCopyDownloadLink">复制下载链接</span>
      </div>
      <!-- 下载引导组件 -->
      <StoreDownloadGuide v-model:show="isShowDownloadGuide" />
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, toRefs, onMounted } from "vue";
import { showToast } from "vant";
import { useBoolean } from "../../hooks";
import { isWxBrowser } from "@/views/StoreModule/utils";
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
import { downloadRecord } from "@/services/storeApi";
/** 相关组件 */
import StoreDownloadGuide from "./components/StoreDownloadGuide.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

defineOptions({ name: "StoreFileDownload" });

/** props */
const props = defineProps<{
  fileName: string;
  fileUrl: string;
}>();

const { fileName: fileNameRef, fileUrl: fileUrlRef } = toRefs(props);
const isPageLoadingRef = ref(false);
const { createMessageError } = useMessages();
const { bool: isShowDownloadGuide, setTrue: setIsShowDownloadGuide, setFalse: setIsShowDownloadGuideFalse } = useBoolean(false);

/** 下载文件 */
function handleDownloadFile() {
  try {
    if (isWxBrowser()) {
      setIsShowDownloadGuide();
      return;
    }
    if (fileUrlRef.value) {
      window.open(fileUrlRef.value, "_blank");
    }
  } catch (error) {
    createMessageError("下载失败：" + error);
  }
}

/** 复制下载链接 */
function handleCopyDownloadLink() {
  try {
    if (fileUrlRef.value) {
      copyText(fileUrlRef.value);
      showToast('复制下载链接成功');
    }
  }
  catch (e) {
    showToast('复制下载链接失败');
  }
}
</script>

<style lang="less" scoped>
.store_file_download_wrapper {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  .wrapper {
    width: 80%;
    height: 236px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    padding: 12px;
    border-radius: 12px;
    transform: translateY(-40%);
    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 32px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-top: 18px;
      margin-bottom: 16px;
    }
    .subTitle {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      line-height: 24px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-bottom: 24px;
    }
    .copylink {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FF6864;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
    }
  }
}
</style>
