<template>
  <VanPopup
    :show="props.show"
    @close="handleClose"
    :close-on-click-overlay="props.closeOnClickOverlay"
    :style="popupStyle"
    :overlay-style="overlayStyle"
    :lock-scroll="props.lockScroll"
    :z-index="props.zIndex"
    get-container="body"
    role="dialog"
    aria-modal="true"
    :aria-label="qrCodeTypeInfo.title || '二维码弹窗'"
  >
    <div class="wrapper">
      <div
        class="qrCode-container"
        :style="{ background: `url(${qrCodeTypeInfo.bgImgSrc}) no-repeat`, backgroundSize: '100% 100%' }"
      >
        <!-- 标题 -->
        <p class="qrCode-title">长按识别图中二维码</p>

        <!-- 二维码区域 -->
        <div class="qrCode-content">
          <img
            :src="props.qrCodeType == QrCodeTypeEnum.DISTRIBUTOR_REGISTER ? dealerApplyQrCodeSrc : storeManagerSrc"
            alt="二维码背景"
            class="img"
            aria-hidden="true"
          />
          <div class="content">
            <span class="tip">{{`${qrCodeTypeInfo.title || '二维码'}`}}</span>
            <VanImage
              width="108"
              height="108"
              fit="contain"
              :src="props.qrCodeUrl"
              :alt="qrCodeTypeInfo.title || '二维码'"
              :show-loading="props.showLoading"
              :show-error="props.showError"
            />
          </div>
        </div>

        <!-- 底部文字 -->
        <div class="title-wrapper" v-if="qrCodeTypeInfo.tip">
          <span class="title" :style="{ color: qrCodeTypeInfo?.tipColor }">{{ qrCodeTypeInfo.tip }}</span>
        </div>
      </div>

      <!-- 关闭按钮 -->
      <button class="close-btn" @click="handleClose" aria-label="关闭弹窗">
        <img :src="props.closeIcon || closeBtn" alt="关闭" role="presentation" />
      </button>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { Image as VanImage, Popup as VanPopup } from 'vant';
import type { CSSProperties } from 'vue';
import { QrCodeTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import closeBtn from "@/assets/storeImage/storeHome/closeBtn.png";
import dealerApplyQrCodeSrc from "@/assets/storeImage/storeMine/dealerApplyQrCode.png";
import dealerApplyQrCodeBgSrc from "@/assets/storeImage/storeMine/dealerApplyQrCode-bg.png";
import storeManagerSrc from "@/assets/storeImage/storeMine/storeManagerQrCode.png";
import storeManagerBgSrc from "@/assets/storeImage/storeMine/storeManagerQrCode-bg.png";

interface Props {
  /** 二维码类型 */
  qrCodeType?: QrCodeTypeEnum;
  /** 是否显示弹窗 */
  show: boolean;
  /** 二维码图片URL */
  qrCodeUrl?: string;
  /** 点击遮罩层是否关闭 */
  closeOnClickOverlay?: boolean;
  /** 是否锁定背景滚动 */
  lockScroll?: boolean;
  /** 弹窗层级 */
  zIndex?: number;
  /** 自定义关闭按钮图标 */
  closeIcon?: string;
  /** 是否显示加载中提示 */
  showLoading?: boolean;
  /** 是否显示加载失败提示 */
  showError?: boolean;
}

defineOptions({ name: 'StoreApplyQrCode' });

const props = withDefaults(defineProps<Props>(), {
  qrCodeType: QrCodeTypeEnum.DISTRIBUTOR_REGISTER,
  closeOnClickOverlay: false,
  lockScroll: true,
  zIndex: 2000,
  showLoading: true,
  showError: true,
  qrCodeUrl: '',
  closeIcon: ''
});

const emit = defineEmits<{
  (e: 'update:show', show: boolean): void;
  (e: 'close'): void;
}>();

const popupStyle = computed<CSSProperties>(() => ({
  borderRadius: '16px',
  background: 'transparent',
  overflow: 'visible'
}));

const overlayStyle = computed<CSSProperties>(() => ({
  background: 'rgba(0, 0, 0, 0.4)'
}));

const handleClose = () => {
  emit('update:show', false);
  emit('close');
};

/** 类型 */
const qrCodeTypeMap = {
  [QrCodeTypeEnum.DISTRIBUTOR_REGISTER]: {
    title: '· 经销商注册码 ·',
    tip: '经销商识别二维码后即可注册自己的经销商部门并邀请店长注册',
    bgImgSrc: dealerApplyQrCodeBgSrc,
    tipColor: '#4F6E96'
  },
  [QrCodeTypeEnum.STORE_MANAGER_REGISTER]: {
    title: '· 店长注册码 ·',
    tip: '店长识别二维码后 即可注册自己的店铺并邀请用户',
    bgImgSrc: storeManagerBgSrc,
    tipColor: '#2E8685'
  },
};

/** 根据qrCodeType获取qrCodeTypeMap */
const qrCodeTypeInfo = computed(() => qrCodeTypeMap[props.qrCodeType]);
</script>

<style lang="less" scoped>
.wrapper {
  box-sizing: border-box;
  position: relative;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .qrCode-container {
    width: 246px;
    min-height: 320px;
    border-radius: 16px;
    padding: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .qrCode-title {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 24px;
      color: #FFFFFF;
      line-height: 30px;
      text-shadow: 0px 2px 1px rgba(23, 112, 226, 0.79);
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin: 0 0 12px;
    }

    .qrCode-content {
      width: 100%;
      height: 200px;
      box-sizing: border-box;
      position: relative;
      margin: 12px 0;

      .img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        object-fit: cover;
      }

      .content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12px;
        box-sizing: border-box;

        .tip {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: bold;
          font-size: 13px;
          color: #FFFFFF;
          line-height: 20px;
          letter-spacing: 1px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-bottom: 20px;
          margin-top: -12px;
        }

        :deep(.van-image) {
          border: 1px solid #f5f5f5;
          padding: 6px;
          background: #fff;
          border-radius: 4px;
        }
      }
    }

    .title-wrapper {
      text-align: center;

      .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #4F6E96;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .close-btn {
    position: absolute;
    bottom: -38px;
    left: 50%;
    transform: translate(-50%, 0);
    width: 28px;
    height: 28px;
    cursor: pointer;
    transition: transform 0.2s ease;
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    outline: none;

    &:active {
      transform: translate(-50%, 0) scale(0.9);
    }

    img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
