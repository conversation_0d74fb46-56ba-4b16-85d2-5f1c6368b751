<template>
  <!-- #ifdef MP-WEIXIN -->
  <view>
    <view style="display: flex; align-items: center; gap: 4rpx" @click="show = true">
      <text class="search-type-name">{{ searchTypeText }}</text>
      <image :src="searchUpIcon" class="search-type-icon" :class="{ 'rotate-180': show }" />
    </view>

    <van-action-sheet
      :show="show"
      cancel-text="取消"
      close-on-click-action
      :actions="actions"
      @select="onSelect"
      @cancel="show = false"
      @click-overlay="show = false"
    />
  </view>
  <!-- #endif -->

  <!-- #ifdef H5 -->
  <JPopover v-model:show="show" placement="bottom-start">
    <template #reference>
      <view style="display: flex; align-items: center; gap: 2px">
        <text class="search-type-name">{{ searchTypeText }}</text>
        <image :src="searchUpIcon" class="search-type-icon" :class="{ 'rotate-180': show }" />
      </view>
    </template>
    <view class="search-type-list">
      <view
        v-for="item in searchTypeOptions"
        :key="item.value"
        class="search-type-item"
        @click="handleClickItem(item)"
        :class="{ 'active-type': props.value === item.value }"
      >
        {{ item.text }}
      </view>
    </view>
  </JPopover>
  <!-- #endif -->
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { StoreOrderTypeEnum } from '@/enum'
import searchUpIcon from '@/static/images/storeHome/searchUpIcon.png'

defineOptions({ name: 'StoreSelectOrderType' })

const props = defineProps<{ value: StoreOrderTypeEnum }>()
const emit = defineEmits<{ (e: 'update:value', value: StoreOrderTypeEnum): void }>()

const show = ref(false)

const searchTypeOptions = [
  { text: '普通订单', value: StoreOrderTypeEnum.NORMAL },
  { text: '福利券订单', value: StoreOrderTypeEnum.COUPON },
  { text: '积分订单', value: StoreOrderTypeEnum.INTEGRAL },
]

const actions = computed(() => searchTypeOptions.map((i) => ({ name: i.text, value: i.value })))

const searchTypeText = computed(() => {
  const option = searchTypeOptions.find((i) => i.value === props.value)
  return option ? option.text : ''
})

// 兼容小程序 van-action-sheet 的 select 事件
function onSelect(e: any) {
  // e.detail = { name, value, index }
  emit('update:value', e?.detail?.value)
  show.value = false
}

/** methods */
const handleClickItem = (item: { text: string; value: StoreOrderTypeEnum }) => {
  emit('update:value', item.value)
  show.value = false
}
</script>

<style lang="scss" scoped>
.search-type-name {
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  text-align: right;
  font-style: normal;
  text-transform: none;
}
.search-type-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  transform: rotateX(180deg);
  transition: transform 0.3s ease;
}
.rotate-180 {
  transform: rotateX(0deg) !important;
}

/*  #ifdef H5 */
.search-type-list {
  width: 160rpx;
  background: #ffffff;
  .search-type-item {
    height: 72rpx;
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    line-height: 40rpx;
    text-align: right;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 2rpx solid #eeeeee;
    box-sizing: border-box;
  }
}
.active-type {
  color: #ef1115 !important;
}
/*  #endif  */
</style>
