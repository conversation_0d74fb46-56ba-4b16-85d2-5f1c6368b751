import { VantComponent } from '../common/component';
VantComponent({
    props: {
        options: <PERSON><PERSON>y,
        showBorder: <PERSON>olean,
    },
    methods: {
        onSelect(event) {
            const { index } = event.currentTarget.dataset;
            const option = this.data.options[index];
            this.$emit('select', Object.assign(Object.assign({}, option), { index }));
        },
    },
});
