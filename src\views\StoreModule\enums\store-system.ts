/**
 * @description keepAliveRouteName
 */
export enum KeepAliveRouteNameEnum {
  /** 首页 */
  HOME = "StoreHome",
  /** 我的订单 */
  MY_ORDER = "MyOrder",
  /** 店铺订单 */
  STORE_ORDER = "StoreOrder",
  /** 福利券 */
  COUPON = "Coupon",
  /** 待核销订单 */
  PENDING_ORDER = "PendingOrder",
  /** 确认订单 */
  CONFIRM_ORDER = "StoreConfirmOrder",
  /** 退款审核 */
  REFUND_AUDIT = "RefundAudit",
  /** 个人中心 */
  STORE_MINE = "StoreMine",
}

/**
 * @description 福利券状态
 */
export enum CouponStatusEnum {
  /**
   * @description 待使用
   */
  NOT_USED = 0,
  /**
   * @description 已使用
   */
  USED = 1,
  /**
   * @description 已失效
   */
  EXPIRED = 2,
}

/**
 * @description 福利券操作
 */
export enum OperationTypeEnum {
  /** 兑换说明 */
  EXCHANGE = 1,
  /** 核销 */
  VERIFICATION = 2,
  /** 券明细 */
  VOUCHER_DETAILS = 3,
}

/**
 * @description 福利券类型
 */
export enum CouponTypeEnum {}

/**
 * @description 积分
 */
export enum IntegralEnum {
  /** 收入 */
  INCOME = 1,
  /** 支出 */
  EXPEND = -1,
}

/**
 * @description QR码类型
 */
export enum QRCodeTypeEnum {
  /** 提货码 */
  PICK_UP_CODE = 1,
  /** 时长码 */
  TIME_CODE = 2,
  /** 福利券码 */
  COUPON_CODE = 3,
  /** 积分码 */
  INTEGRAL_CODE = 4,
}

/**
 * @description 我的订单
 */
export enum OrderStatusEnum {
  /** 全部 */
  ALL = 666,

  /** 待付款 */
  WAIT_PAY = 1,
  /** 待发货/提货 */
  WAIT_DELIVER = 2,
  /** 待收货 */
  WAIT_RECEIVE = 3,

  /** 退款/售后 */
  REFUND = 999,
}

/**
 * @description 订单统计时间筛选
 */
export const enum DataRangeValue {
  /** 今日 */
  TODAY = "today",
  /** 近七天 */
  SEVEN_DAYS = "seven_days",
  /** 近30天 */
  THIRTY_DAYS = "thirty_days",
  /** 近90天 */
  NINETY_DAYS = "ninety_days",
}

/**
 * @description 订单统计类型
 */
export const enum OrderStatisticsTypeEnum {
  /** 全部 */
  ALL = 0,

  /** 购物类订单 */
  SHOPPING = 1,
  /** 福利券订单 */
  COUPON = 2,
  /** 积分兑换订单 */
  INTEGRAL = 3,
}

/**
 * @description 门店管理-订单统计-排序类型
 */
export const enum OrderSortTypeEnum {
  /** 消费金额 */
  AMOUNT = 1,
  /** 下单数量 */
  COUNT = 2,
}

/**
 * @description 轮播图类型
 */
export const enum BannerTypeEnum {
  /** 门店首页 */
  STORE_HOME = 2,
}

/**
 * @description 门店商品类别
 */
export const enum GoodsCategoryEnum {
  /** 普通商品 */
  NORMAL = 3,
}

/**
 * @description 店铺管理时间筛选
 */
export const enum StoreDataRangeEnum {
  /** 今日 */
  TODAY = "today",
  /** 昨天 */
  YESTERDAY = "yesterday",
  /** 本月 */
  THIS_MONTH = "this_month",
  /** 全部 */
  ALL = "all",
}

/**
 * @description 店铺管理订单类型
 */
export const enum StoreOrderTypeEnum {
  /** 普通订单 */
  NORMAL = 1,
  /** 代下单订单 */
  ORDER_BY_OTHERS = 2,
  /** 积分订单 */
  INTEGRAL = 3,
  /** 福利券订单 */
  COUPON = 4,
}

/**
 * @description 门店用户类型
 */
export const enum StoreUserTypeEnum {
  /** 店长 */
  OWNER = 1,
  /** 店员 */
  STAFF = 2,
  /** 百货用户&商城用户 */
  CUSTOMER = 3,
}

/**
 * @description 订单状态
 */
export const enum StoreOrderStatusEnum {
  /** 待支付 */
  WAIT_PAY = 1,
  /** 待发货 */
  WAIT_SEND = 2,
  /** 待收货 */
  WAIT_RECEIVE = 3,
  /** 已完成 */
  FINISHED = 4,
  /** 已取消 */
  CANCELLED = 5,
}

/**
 * @description 订单支付方式
 */
export const enum StoreOrderPayTypeEnum {
  /** 未支付 */
  UNPAID = 0,
  /** 在线支付 */
  ONLINE = 1,
  /** 物流代收 */
  LOGISTICS = 2,
  /** 支付定金 */
  DEPOSIT = 3,
  /** 纯积分支付 */
  INTEGRAL = 4,
  /** 积分+现金 */
  INTEGRAL_AND_CASH = 5,
  /** 福利券 */
  COUPON = 6,
}

/**
 * @description 订单是否锁单
 */
export const enum StoreOrderIsLockEnum {
  /** 未锁单 */
  UNLOCK = 0,
  /** 已锁单 */
  LOCK = 1,
}

/**
 * @description 订单支付状态
 */
export const enum StoreOrderPayStatusEnum {
  /** 未支付 */
  UNPAID = 0,
  /** 支付定金 */
  DEPOSIT = 1,
  /** 支付全款 */
  FULL = 2,
  /** 线下支付 */
  OFFLINE = 3,
}

/**
 * @description 跳转待核销订单路由类型
 */
export const enum StorePendingOrderRouteTypeEnum {
  /** 我的 - 待核销订单 */
  MY_WAIT_VERIFY = 1,
  /** 扫码 - 待核销订单 */
  SCAN_WAIT_VERIFY = 2,
}

/**
 * @description 跳转福利券路由类型
 */
export const enum StoreCouponRouteTypeEnum {
  /** 我的福利券 */
  MY_COUPON = 1,
  /** 扫码福利券核销 */
  SCAN_COUPON = 2,
}

/**
 * @description 跳转积分路由类型
 */
export const enum StoreIntegralRouteTypeEnum {
  /** 我的积分 */
  MY_INTEGRAL = 1,
  /** 扫码积分加减 */
  SCAN_INTEGRAL = 2,
}

/**
 * @description 跳转收货地址路由类型
 */
export const enum StoreAddressRouteTypeEnum {
  /** 我的收货地址 */
  MY_ADDRESS = 1,
  /** 下单的收货地址 */
  ORDER_ADDRESS = 2,
}

/**
 * @description 福利券类型
 */
export const enum StoreCouponTypeEnum {
  /** 福利券 */
  COUPON = 1,
  /** 时长券 */
  TIME_COUPON = 2,
}

/**
 * @description 福利券领取方式
 */
export const enum StoreCouponReceiveTypeEnum {
  /** 门店发放 */
  STORE = 1,
  /** 直播间 */
  LIVE = 2,
}

/**
 * @description 订单详情订单类型
 */
export const enum StoreOrderDetailTypeEnum {
  /** 普通订单 */
  NORMAL = 1,
  /** 待下单订单 */
  WAIT = 2,
  /** 积分订单 */
  INTEGRAL = 3,
  /** 福利券订单 */
  COUPON = 4,
}

/**
 * @description 订单详情核销状态
 */
export const enum StoreOrderDetailVerifyStatusEnum {
  /** 未核销 */
  UNVERIFY = 0,
  /** 已核销 */
  VERIFIED = 1,
}

/**
 * @description 跳转订单详情类型
 */
export const enum StoreOrderDetailRouteTypeEnum {
  /** 我的订单跳转 */
  MY_ORDER = 1,
  /** 扫核销码跳转 */
  SCAN = 2,
}

/**
 * @description 跳转售后详情路由类型
 */
export const enum StoreAfterSaleDetailRouteTypeEnum {
  /** 我的售后跳转 */
  MY_AFTER_SALE = 1,
  /** 退款审核跳转 */
  REFUND_AUDIT = 2,
}

/**
 * @description 跳转填写物流信息路由类型
 */
export const enum StoreLogisticsRouteTypeEnum {
  /** 填写物流 */
  FILL_LOGISTICS = 1,
  /** 确认收货并填写物流 */
  CONFIRM_AND_FILL_LOGISTICS = 2,
}

/**
 * @description 个人中心扫码核销类型
 */
export const enum StoreScanTypeEnum {
  /** 扫提货码 */
  PICKUP = 1,
  /** 扫福利券码 */
  COUPON = 2,
  /** 扫积分码 */
  INTEGRAL = 3,
  /** 扫订单核销码 */
  ORDER = 4,
  /** 时长码 */
  TIME = 5,
}

/**
 * @description 门店是否停用
 */
export const enum StoreStatusEnum {
  /** 正常 */
  NORMAL = 1,
  /** 停用 */
  DISABLED = 0,
}

/**
 * @description 积分明细来源类别
 */
export const enum StoreIntegralDetailTypeEnum {
  /** 过期未使用 */
  EXPIRED = 0,
  /** 积分购物支出 */
  SHOPPING_EXPENSE = 1,
  /** 购物返还 */
  SHOPPING_REBATE = 2,
  /** 签到 */
  CHECK_IN = 3,
  /** 每日来访 */
  DAILY_VISIT = 4,
  /** 查看商品 */
  VIEW_PRODUCT = 5,
  /** 社群任务 */
  COMMUNITY_TASK = 6,
  /** 社群手动增加 */
  COMMUNITY_MANUAL_ADD = 7,
  /** 社群手动减少 */
  COMMUNITY_MANUAL_SUB = 8,
  /** 社群积分替代红包 */
  COMMUNITY_POINT_REPLACE_REDPACKET = 9,
  /** 商城手动增加 */
  MALL_MANUAL_ADD = 11,
  /** 商城手动减少 */
  MALL_MANUAL_SUB = 12,
  /** 门店手动增加 */
  STORE_MANUAL_ADD = 13,
  /** 门店手动减少 */
  STORE_MANUAL_SUB = 14,
}

/**
 * @description 积分渠道
 */
export const enum StoreIntegralChannelEnum {
  /** 商城 */
  MALL = 0,
  /** 社群 */
  COMMUNITY = 1,
  /** 门店 */
  STORE = 2,
}

/**
 * @description 售后类型
 */
export const enum StoreAfterSaleTypeEnum {
  /** 仅退款 */
  REFUND = 1,
  /** 退货退款 */
  REFUND_RETURN = 2,
  /** 取消订单 */
  CANCEL_ORDER = 3,
}

/**
 * @description 售后状态
 */
export const enum AfterSaleStatusEnum {
  /** 全部 前端自定义 */
  ALL = 9999,
  /** 其他状态 前端自定义 */
  OTHER = 9998,

  /** 非售后状态 */
  NON_AFTER_SALE = 0,
  /** 待商家受理 */
  PENDING_MERCHANT = 1,
  /** 客户撤回申请 */
  CUSTOMER_WITHDRAWN = 2,
  /** 商家拒绝退款 */
  MERCHANT_REFUSED = 3,
  /** 待客户退货 */
  PENDING_RETURN = 4,
  /** 待商家收货 */
  PENDING_MERCHANT_RECEIPT = 5,
  /** 退货退款关闭 */
  RETURN_CLOSED = 6,
  /** 退款中 */
  REFUNDING = 7,
  /** 待打款 */
  PENDING_PAYMENT = 8,
  /** 退款完成 */
  REFUND_COMPLETED = 9,
  /** 商家同意取消订单 */
  CANCEL_APPROVED = 10,
  /** 商家拒绝取消订单 */
  CANCEL_REJECTED = 11,
}

/**
 * @description 售后原因
 */
export const enum AfterSaleReasonEnum {
  /** 其他 */
  OTHER = 0,
  /** 拍错/多拍 */
  WRONG_OR_EXTRA_ORDER = 1,
  /** 不想要了 */
  NO_LONGER_WANTED = 2,
  /** 无快递信息 */
  NO_EXPRESS_INFO = 3,
  /** 包裹为空 */
  EMPTY_PACKAGE = 4,
  /** 已拒签包裹 */
  REJECTED_PACKAGE = 5,
  /** 快递长时间未送达 */
  DELIVERY_DELAYED = 6,
  /** 与商品描述不符 */
  DESCRIPTION_MISMATCH = 7,
  /** 质量问题 */
  QUALITY_ISSUE = 8,
  /** 卖家发错货 */
  WRONG_ITEM_SENT = 9,
  /** 三无产品 */
  NO_CERTIFICATION = 10,
  /** 假冒产品 */
  FAKE_PRODUCT = 11,
}

/**
 * @description 二维码类型
 */
export const enum QrCodeTypeEnum {
  /** 经销商注册码 */
  DISTRIBUTOR_REGISTER = 1,
  /** 店长注册码 */
  STORE_MANAGER_REGISTER = 2,
}

/**
 * @description 组织架构身份类型
 */
export const enum OrgIdentityTypeEnum {
  /** 区域经理 */
  REGIONAL_MANAGER = 1,
  /** 经销商 */
  DISTRIBUTOR = 2,
  /** 门店 */
  STORE = 3,
}

/**
 * @description 组织申请类型
 */
export const enum OrgApplyTypeEnum {
  /** 区域经理申请 */
  REGIONAL_MANAGER_APPLY = 1,
  /** 经销商申请 */
  DISTRIBUTOR_APPLY = 2,
  /** 门店申请 */
  STORE_APPLY = 3,
}

/**
 * @description 地址模式
 */
export const enum AddressModeEnum {
  /** 新增 */
  ADD = 1,
  /** 编辑 */
  EDIT = 2,
}

/**
 * 物流轨迹状态枚举
 * @description 描述物流运输过程中的各个状态
 */
export const enum LogisticsStatusEnum {
  /** 0 - 暂无轨迹信息 */
  NO_TRACKING = 0,
  /** 1 - 已揽收 */
  COLLECTED = 1,
  /** 2 - 在途中 */
  IN_TRANSIT = 2,
  /** 3 - 已签收 */
  DELIVERED = 3,
  /** 4 - 问题件 */
  PROBLEM = 4,
  /** 5 - 转寄 */
  FORWARDED = 5,
  /** 6 - 清关 */
  CUSTOMS = 6,
}

/**
 * @description 商品提货方式
 */
export const enum ProductPickupModeEnum {
  /** 快递到家 */
  HOME_DELIVERY = 1,
  /** 到店自提 */
  STORE_PICKUP = 2,
}

/**
 * @description 订单核销类型
 */
export const enum OrderVerificationTypeEnum {
  /**下单后待核销 */
  PENDING_VERIFICATION = 1,
  /**下单自动核销 */
  AUTO_VERIFICATION = 2,
  /**下单门店到货后核销 */
  STORE_VERIFICATION = 3,
}

/**
 * @description 组织申请状态
 */
export const enum OrgApplyStatusEnum {
  /** 待审核 */
  PENDING = 0,
  /** 审核通过 */
  APPROVED = 1,
  /** 审核不通过 */
  REJECTED = 2,
}

/**
 * @description 退货订单状态
 */
export const enum ReturnOrderStatusEnum {
  /** 待退货 */
  PENDING_RETURN = 0,
  /** 已锁定 */
  LOCKED = 1,
  /** 已完成 */
  COMPLETED = 2,
}

/**
 * @description 客户角色操作
 */
export const enum CustomerRoleOperationEnum {
  /**客户撤回申请 */
  WITHDRAW_APPLICATION = 20,
  /**客户申请退款 */
  APPLY_FOR_REFUND = 21,
  /**客户申请取消订单 */
  APPLY_FOR_CANCELLATION = 22,
  /**客户填写物流单号 */
  SUBMIT_SHIPPING_INFO = 23,
  /**客户退货超时 */
  RETURN_TIMEOUT = 24,
  /**后台管理员填写物流单号  */
  SUBMIT_SHIPPING_INFO_FROM_PC = 25,
}

/**
 * @description 店长/店员角色操作
 */
export const enum StoreRoleOperationEnum {
  /**同意退款（退款金额小于等于线上支付金额） */
  AGREE_TO_REFUND_ONLINE = 1,
  /**同意退款（退款金额大于线上支付金额） */
  AGREE_TO_REFUND_OFFLINE = 2,
  /**同意退货退款 */
  AGREE_TO_REFUND_AND_RETURN = 3,
  /**同意取消订单 */
  AGREE_TO_CANCEL = 5,
  /**拒绝申请 */
  REFUSE_APPLICATION = 7,
  /**收货并退款（退款金额小于等于线上支付金额 */
  RECEIPT_AND_REFUND_ONLINE = 8,
  /**收货并退款（退款金额大于线上支付金额） */
  RECEIPT_AND_REFUND_OFFLINE = 9,
  /**已线下退款 */
  REFUNDED_OFFLINE = 11,
}

/**
 * @description 门店业务Code
 */
export const enum StoreBusinessCodeEnum {
  /** 门店退货审核业务 */
  STORE_RETURN_APPROVAL = "store_return_approval",
}

/**
 * @description 店铺管理
 */
export const enum StoreManageEnum {
  /** 我的店铺 */
  MY_STORE = "my_store",
  /** 待审核 */
  PENDING_REVIEW = "pending_review",
}

/**
 * @description 店铺功能
 */
export const enum StoreFunctionEnum {
  /** 退款审核 */
  REFUND_AUDIT = "refundAudit",
}

/**
 * @description 门店审核
 */
export const enum StoreAuditEnum {
  /** 待审核 */
  PENDING_REVIEW = 0,
  /** 审核通过 */
  REVIEW_PASSED = 1,
  /** 审核不通过 */
  REVIEW_NOT_PASSED = 2,
}

/**
 * @description 门店数据导出
 */
export const enum StoreDataExportEnum {
  /** 导出会员数据 */
  A01_MEMBER_DATA = "A01",
  /** 导出会员消费数据 */
  A02_MEMBER_CONSUMPTION = "A02",
  /** 导出福利券领取时间统计数据 */
  B01_COUPON_CLAIM_STATS = "B01",
  /** 导出福利券使用时间统计数据 */
  B02_COUPON_USE_STATS = "B02",
  /** 导出会员每日观看时长数据 */
  C01_DAILY_VIEWING_TIME = "C01",
  /** 导出订单统计 */
  D00_ORDERS = "D00",
  /** 导出已支付不含退款订单数据 */
  D01_PAID_ORDERS_WITHOUT_REFUNDS = "D01",
  /** 导出已支付包含退款订单数据 */
  D02_PAID_ORDERS_WITH_REFUNDS = "D02",
  /** 导出已退款订单数据 */
  D03_REFUNDED_ORDERS = "D03",
}

/**
 * @description 导出状态
 */
export const enum ExportStatusEnum {
  /** 未导出 */
  PENDING_EXPORT = 0,
  /** 导出中 */
  EXPORTING = 1,
  /** 已导出 */
  EXPORT_SUCCESS = 2,
  /** 导出失败 */
  EXPORT_FAILED = 3,
}

/**
 * @description 提现状态
 */
export const enum WithdrawStatusEnum {
  /** 待审核 */
  PENDING_REVIEW = 1,
  /** 待打款 */
  PENDING_PAYMENT = 2,
  /** 打款中 */
  PAYMENT_IN_PROGRESS = 3,
  /** 打款失败 */
  PAYMENT_FAILED = 4,
  /** 已打款 */
  PAID = 5,
  /** 已驳回 */
  REJECTED = 6,
}

/**
 * @description 账户类型
 */
export const enum AccountTypeEnum {
  /** 对私账户 */
  PRIVATE = 1,
  /** 对公账户 */
  PUBLIC = 2,
}

/**
 * @description 收支流水记录类型
 */
export const enum FlowTypeEnum {
  /**分佣结算 */
  COMMISSION_SETTLEMENT = 1,
  /**提现 */
  WITHDRAW = 2,
  /**提现驳回 */
  WITHDRAW_REJECTED = 3,
}

/**
 * @description 收支流水收支类型
 */
export const enum FlowDirectionEnum {
  /** 收入 */
  INCOME = 1,
  /** 支出 */
  EXPENSE = -1,
}
