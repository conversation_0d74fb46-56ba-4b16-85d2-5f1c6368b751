import { ref, reactive, computed } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { getMemberCustomerPage, getMemberOverview } from "@/services/storeApi";

export default function useMember(_params: {
    staffShortId: string | string[];
    storeId: string;
    customerId: string;
}) {
  const isPageLoadingRef = ref(false);
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 初始化查询参数 */
  const initParams = {
    keyword: null,
    staffShortId: _params?.staffShortId ?? null,
    startTime: null,
    endTime: null,
    storeId: _params?.storeId ?? null,
    customerId: _params?.customerId ?? null,
  };
  const modal = ref({ ...initParams });

  /** 会员汇总 */
  const memberOverview = ref({
    monthNew: 0,
    todayNew: 0,
    totalCount: 0,
    yesterdayNew: 0,
  });
  /** 会员数据 */
  const memberList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(true);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);

  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 获取搜索参数 */
  function getSearchParams() {
    const _searchParams = {
      data: {
        staffShortId: modal.value.staffShortId,
        keyword: modal.value.keyword,
        startTime: modal.value.startTime,
        endTime: modal.value.endTime,
        storeId: modal.value.storeId,
        customerId: modal.value.customerId,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };

    return _searchParams;
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getMemberList();
    }
  }

  /** 初始化 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    getMemberList();
  }

  /** 获取会员数据汇总 */
  async function getMemberCustomer() {
    try {
      const _params = {
        storeId: modal.value.storeId,
        customerId: modal.value.customerId,
      };
      const res = await getMemberOverview(_params);
      if (res) {
        memberOverview.value = res;
      }
    } catch (error) {
      createMessageError("获取会员数据异常：" + error);
    }
  }

  /** 获取会员分页数据 */
  async function getMemberList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getMemberCustomerPage(_params);

      // 更新订单列表
      if (current === 1) {
        memberList.value = records;
      } else if (records.length) {
        memberList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 数据初始化 */
  async function initMemberList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getMemberList();
    isPageLoadingRef.value = false;
  }

  return {
    modal,
    isPageLoadingRef,
    memberList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    memberOverview,
    onLoad,
    onRefresh,
    getMemberList,
    initMemberList,
    getMemberCustomer
  };
}
