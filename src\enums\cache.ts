export const enum Cache_Key {
  Token = "jw-root-token",
  UserInfo = "jw-root-userinfo",
  RouteConfig = "jw-root-route-config",
  System = "jw-root-system",
  AdvertiserInfo="jw-root-advertiser-info",
  State="jw-root-state",
  MemberInfo="jw-root-member-info",
  VideoProcess = "jw-root-video-process",
  LoginRetryTime = 'jw-root-login-retry-time',
  Error = 'jw-root-error',
  Camp = 'jw-root-camp',
  SystemVar="jw-root-system-var",
  UnResiterRetryTime='jw-root-unResiter-retry-time',
  VideoFinished='jw-root-video-finished',
  TempInput='jw-root-temp-input',
  StreamProcessSeconds='jw-root-stream-process-seconds',
  IsAuthVaild = 'jw-root-is-auth-valid',
  StateCache = 'jw-root-state-cache',
  IsWatched='jw-root-is-watched',
  IsSkipAddCoursePlanNotice = 'jw-scrm-is-skip-courseplan-notice',
  ApiPrefix = 'jw-root-api-prefix',
  FatherOrigin= 'jw-root-father-origin',
  /** 门店 */
  StoreToken='jw-root-store-token',
  StoreUserInfo='jw-root-store-userinfo',
  StorePathName='jw-root-store-pathName',
  StoreOrderCode='jw-root-order-code',
  StoreHasShowFillAddressForm='jw-root-store-has-show-fill-address-form',
}
export const enum StorageType {
  LOCAL = "local",
  SESSION = "session",
}
