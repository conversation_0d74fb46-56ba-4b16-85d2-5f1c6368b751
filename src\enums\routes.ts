export const enum RoutesName {
  Root = "jw-root",
  Login = "jw-root-login",
  Check = "jw-root-check",
  Exception403 = "jw-root-exception-403",
  Exception404 = "jw-root-exception-404",
  Result = "jw-root-result",
  UnResiter = "jw-root-unresiter",
  PromotionAuth = "jw-root-PromotionAuth",
  Exception9000 = "jw-root-exception-9000",
  Exception9001 = "jw-root-exception-9001",
  Exception9002 = "jw-root-exception-9002",
  Exception9003 = "jw-root-exception-9003",
  Exception9004 = "jw-root-exception-9004",
  Exception9005 = "jw-root-exception-9005",
  Exception9006 = "jw-root-exception-9006",
  Exception9008 = "jw-root-exception-9008",
  Exception9007 = "jw-root-exception-9007",
  Exception9009 = "jw-root-exception-9009",
  GetCode = "jw-root-get-code",
  Watch = "jw-root-watch",
}

/** 商城模块 */
export const enum RoutesName {
  StoreIndex = "jw-root-store-index",
  /** 缺省页 */
  StoreDefault = "jw-root-store-default",
  /** 商城首页 */
  StoreHome = "jw-root-store-home",
  /** 福利券 */
  StoreCoupon = "jw-root-store-coupon",
  /** 福利券详情 */
  StoreCouponDetail = "jw-root-store-coupon-detail",
  /** 观看时长 */
  StoreWatchTime = "jw-root-store-watch-time",

  /** 积分 */
  StoreIntegral = "jw-root-store-integral",
  /** 待核销订单 */
  StorePendingVerificationOrders = "jw-root-store-pending-verification-orders",

  /** 商城我的 */
  StoreMine = "jw-root-store-mine",
  /** 我的订单 */
  StoreMyOrders = "jw-root-store-my-orders",
  /** 店铺订单 */
  StoreShopOrders = "jw-root-store-shop-orders",
  /** 订单统计 */
  StoreOrderStatistics = "jw-root-store-order-statistics",
  /** 商品分类 */
  StoreCategory = "jw-root-store-category",
  /** 商品搜索 */
  StoreSearch = "jw-root-store-search",
  /** 商品详情 */
  StoreDetail = "jw-root-store-detail",
  /** 订单详情 */
  StoreOrderDetail = "jw-root-store-order-detail",
  /** 申请退款 */
  StoreApplyRefund = "jw-root-store-apply-refund",
  /** 售后详情 */
  StoreAfterSaleDetail = "jw-root-store-after-sale-detail",
  /** 排行榜 */
  StoreRanking = "jw-root-store-ranking",

  /** 积分商城 */
  StoreIntegralMall = "jw-root-store-integral-mall",
  /** 福利券统计 */
  StoreWelfareVoucherStatistics = "jw-root-welfare-voucher-statistics",
  /** 福利券统计详情 */
  StoreWelfareVoucherStatisticsDetail = "jw-root-welfare-voucher-statistics-detail",
  /**核销统计 */
  StoreVerificationStatistics = 'jw-root-verification-statistics',
  /**销售统计 */
  StoreSalesStatistics = 'jw-root-sale-statistics',
  /**店员统计 */
  StoreShopAssistantStatistics = 'jw-root-shop-assistant-statistics',
  /**积分商城-我能兑 */
  StoreIntegralMallCanExchange = 'jw-root-store-integral-mall-can-exchange',
  /**福利卷商城 */
  StoreWelfareVoucherMall = 'jw-root-store-welfare-voucher-mall',
  /**确认订单 */
  StoreConfirmOrder = 'jw-root-store-confirm-order',
  /**收银台 */
  StoreCashier = 'jw-root-store-cashier',
  /**账号设置 */
  StoreAccountSetting = 'jw-root-store-account-setting',
  /**会员管理  */
  MemberManagement = 'jw-root-member-management',
  /**观看时长  */
  ViewDuration = 'jw-root-view-duration',
  /**登录 */
  StoreLogin = "jw-root-store-login",
  /**绑定门店 */
  StoreInviteMember = "jw-root-store-invite-member",

  /** 我的经销商 */
  MyDealer = "jw-root-store-my-dealer",
  /** 店铺管理 */
  StoreManagement = "jw-root-store-management",
  /** 注册 */
  StoreSignup = "jw-root-store-signup",
  /** 地址新增 */
  StoreAddressAdd = "jw-root-store-address-add",
  /** 收货地址 */
  StoreAddress = "jw-root-store-address",
  /** 现金券 */
  StoreCashCoupon = "jw-root-store-cash-coupon",

  /** 退货订单 */
  StoreReturnOrder = "jw-root-store-return-order",
  /** 退款审核 */
  StoreRefundAudit = "jw-root-store-refund-audit",
  /** 填写物流信息 */
  StoreFillLogistics = "jw-root-store-fill-logistics",
  /** 填写物流单号 */
  StoreFillLogisticsNo = "jw-root-store-fill-logistics-no",
  /** 数据导出 */
  StoreDataExport = "jw-root-store-data-export",
  /** 导出记录 */
  StoreExportRecord = "jw-root-store-export-record",
  /** 文件下载 */
  StoreFileDownload = "jw-root-store-file-download",
  /** 钱包 */
  StoreWallet = "jw-root-store-wallet",
  /** 提现记录 */
  StoreWithdrawRecord = "jw-root-store-withdraw-record",
  /** 提现 */
  StoreWithdraw = "jw-root-store-withdraw",
  /** 银行卡信息 */
  StoreBankCard = "jw-root-store-bank-card",

  /** 门店物流 */
  StoreLogistics = "jw-root-store-logistics",
}

/**商城直播 */
export const enum RoutesName {
  StoreLive = "jw-root-store-live",
}
