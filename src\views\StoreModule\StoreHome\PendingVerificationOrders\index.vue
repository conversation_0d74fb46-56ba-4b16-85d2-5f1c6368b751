<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <!-- 门店店长、店员操作 -->
    <StoreSelectSearch
      v-if="props.type == StorePendingOrderRouteTypeEnum.MY_WAIT_VERIFY"
      v-model:value="searchValueRef"
      v-model:searchTypeValue="searchTypeRef"
      :searchTypeOptions="searchTypeOptions"
      @search="initPendingOrdersList"
    />
    <div
      :class="{ 
        'pending-orders-content-h1': props.type == StorePendingOrderRouteTypeEnum.SCAN_WAIT_VERIFY, 
        'pending-orders-content-h2': props.type == StorePendingOrderRouteTypeEnum.MY_WAIT_VERIFY 
      }"
    >
      <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" @scroll="onScroll" class="pending-orders-content">
        <template v-if="pendingVerificationOrders.length">
          <VanList v-model:loading="isLoadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
            <PendingOrdersCard
              v-for="item in pendingVerificationOrders"
              :key="item"
              :orderInfo="item"
              @writeOffOrder="handleWriteOffOrder"
              @click="handleToOrderDetail(item)"
            />
          </VanList>
        </template>
        <template v-else>
          <EmptyData />
        </template>
      </VanPullRefresh>
    </div>
    <!-- 核销订单 -->
    <WriteOffOrder v-model:show="isShowWriteOffOrderRef" @confirm="handleConfirmWriteOffOrder" />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, onActivated, nextTick } from "vue";
import useGetPendingOrders from "./hooks/useGetPendingOrders";
import { useMessages } from "@/hooks/useMessage";
import { StorePendingOrderRouteTypeEnum, KeepAliveRouteNameEnum, StoreOrderDetailRouteTypeEnum } from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { RoutesName } from "@/enums/routes";
import { verifyOrder } from "@/services/storeApi";
/**  相关组件 */
import StoreSelectSearch from "@/views/StoreModule/components/StoreSelectSearch.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import PendingOrdersCard from "./components/PendingOrdersCard.vue";
import WriteOffOrder from "@/views/StoreModule/components/WriteOffOrder.vue";

defineOptions({ name: KeepAliveRouteNameEnum.PENDING_ORDER });

/** props */
const props = defineProps<{
  userId: string;
  type: StorePendingOrderRouteTypeEnum;
}>();

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom } = useKeepAliveRoute();
const {
  isPageLoadingRef,
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  pendingVerificationOrders,
  searchValueRef,
  searchTypeRef,
  searchTypeOptions,
  onRefresh,
  onLoad,
  getPendingOrders,
  initPendingOrdersList
} = useGetPendingOrders({
  userId: props.userId
});
const { routerPushByRouteName } = useRouterUtils();
const { createMessageError, createMessageSuccess } = useMessages();
const isShowWriteOffOrderRef = ref(false);
/** 当前核销订单 */
const currentVerifyOrderInfoRef = ref({
  code: null,
  verifyCode: null
});

/** 跳转订单详情 */
function handleToOrderDetail(orderInfo) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.PENDING_ORDER);
  routerPushByRouteName(RoutesName.StoreOrderDetail, {
    routeType: StoreOrderDetailRouteTypeEnum.SCAN,
    orderCode: orderInfo?.code ?? "",
  });
}

/** 滚动触发 */
function onScroll(e) {
  scrollEventHandler(e, KeepAliveRouteNameEnum.PENDING_ORDER);
}

function handleWriteOffOrder(orderInfo) {
  isShowWriteOffOrderRef.value = true;
  currentVerifyOrderInfoRef.value = {
    code: orderInfo?.code ?? "",
    verifyCode: orderInfo?.orderVerificationDTO?.code ?? "",
  };
}

/** 核销订单 */
async function handleConfirmWriteOffOrder() {
  try {
    const { code, verifyCode } = currentVerifyOrderInfoRef.value;
    const _params = {
      code, 
      verifyCode
    };
    const resp = await verifyOrder(_params);
    if (resp) {
      createMessageSuccess("核销成功");
      currentVerifyOrderInfoRef.value = {
        code: null,
        verifyCode: null
      };
      onRefresh();
    }
  } catch (error) {
    createMessageError("核销失败：" + error);
    currentVerifyOrderInfoRef.value = {
      code: null,
      verifyCode: null
    };
  }
}

/** 组件挂载 */
onMounted(() => {
  getPendingOrders();
});

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName("pending-orders-content")[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.PENDING_ORDER);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.PENDING_ORDER);
  });
});
</script>

<style lang="less" scoped>
.pending-orders-content {
  width: 100%;
  height: 100%;
  padding: 12px 10px;
  box-sizing: border-box;
  overflow-y: auto;
  padding-bottom: calc(10px + env(safe-area-inset-bottom));
}
.pending-orders-content-h1 {
  height: 100vh;
}
.pending-orders-content-h2 {
  height: calc(100vh - 48px);
}
</style>
