<template>
  <DefaultLayouts>
    <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh">
      <view class="store_order_detail_wrapper">
        <view class="store_order_detail_bg" :style="backgroundStyle"></view>
        <!-- #ifdef MP-WEIXIN -->
        <JNavBar title="订单详情" />
        <!-- #endif -->
        <scroll-view scroll-y enhanced :show-scrollbar="false" class="store_order_detail_content">
          <!-- 订单状态 -->
          <StoreOrderStatus :orderInfo="orderDetailRef" />
          <!-- 收货地址信息 -->
          <StoreDeliveryInfo
            v-if="isHomeDelivery"
            :addressInfo="orderDetailRef?.customerAddressDTO"
          />
          <!-- 商品信息 -->
          <StoreCommodityInfo :orderInfo="orderDetailRef" @clickVerificationCodeStatus="" />
          <!-- 核销码 -->
          <StoreVerificationCode
            v-if="
              orderDetailRef?.status === StoreOrderStatusEnum.WAIT_SEND &&
              routeTypeRef != StoreOrderDetailRouteTypeEnum.SCAN &&
              isSelfPickUp
            "
            :orderInfo="orderDetailRef"
          />
          <!-- 门店地址 -->
          <StoreAddress v-if="isSelfPickUp" :storeEntity="orderDetailRef?.storeEntityDTO" />
          <!-- 支付信息 -->
          <StorePaymentInfo :orderInfo="orderDetailRef" />
          <!-- 订单信息 -->
          <StoreOrderInfo :orderInfo="orderDetailRef" />
        </scroll-view>
        <view class="footer">
          <!-- 待提货 -->
          <template
            v-if="
              [StoreOrderStatusEnum.WAIT_SEND].includes(orderDetailRef?.status) &&
              routeTypeRef == StoreOrderDetailRouteTypeEnum.SCAN &&
              isSelfPickUp &&
              !isAutoVerification
            "
          >
            <van-button
              type="primary"
              size="small"
              block
              round
              style="width: 120px; margin: 8px 12px"
              @click=""
            >
              确认核销
            </van-button>
          </template>
          <!-- 待付款 -->
          <template
            v-if="
              [StoreOrderStatusEnum.WAIT_PAY].includes(orderDetailRef?.status) &&
              routeTypeRef == StoreOrderDetailRouteTypeEnum.MY_ORDER
            "
          >
            <view class="btn-group">
              <!-- 取消订单 -->
              <view class="cancellation-order" @click.stop="">取消订单</view>
              <!-- 付款 -->
              <view class="payment-order" @click.stop="">付款</view>
            </view>
          </template>
          <!-- 申请退款 (我的订单：待发货、待收货、已完成、不锁单、在线支付) -->
          <template v-if="isShowApplyRefund">
            <view class="btn-group">
              <!-- 申请退款 -->
              <view class="refund-order" @click.stop="">申请退款</view>
            </view>
          </template>
        </view>
      </view>
    </JLoadingWrapper>
  </DefaultLayouts>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useGetOrderDetail } from './hooks'
import {
  StoreOrderStatusEnum,
  StoreOrderDetailRouteTypeEnum,
  StoreOrderTypeEnum,
  ProductPickupModeEnum,
  StoreOrderPayTypeEnum,
  StoreOrderIsLockEnum,
  OrderVerificationTypeEnum,
  CustomerRoleOperationEnum,
} from '@/enum'
import { isArray } from '@/utils/isUtils'
/** 静态资源 */
import OrderDetailBg from '@/static/images/storeHome/orderDetails/order_detail_bg.png'
import OrderDetailCancelledBg from '@/static/images/storeHome/orderDetails/order_detail_cancelled_bg.png'
/** 相关组件 */
import DefaultLayouts from '@/layouts/default.vue'
import JLoadingWrapper from '@/components/JLoadingWrapper/index.vue'
import JNavBar from '@/components/JNavBar/index.vue'
import StoreOrderStatus from './components/StoreOrderStatus.vue'
import StoreDeliveryInfo from './components/StoreDeliveryInfo.vue'
import StoreCommodityInfo from './components/StoreCommodityInfo.vue'
import StoreVerificationCode from './components/StoreVerificationCode.vue'
import StoreAddress from './components/StoreAddress.vue'
import StorePaymentInfo from './components/StorePaymentInfo.vue'
import StoreOrderInfo from './components/StoreOrderInfo.vue'

defineOptions({ name: 'OrderDetail' })

const { isPageLoadingRef, getOrderDetail, orderDetailRef } = useGetOrderDetail()
/** 当前路由Type */
const routeTypeRef = ref<StoreOrderDetailRouteTypeEnum>(StoreOrderDetailRouteTypeEnum.MY_ORDER)

/**
 * @description 计算属性
 */

/** 是否显示申请退款 */
const isShowApplyRefund = computed(() => {
  return (
    [
      StoreOrderStatusEnum.WAIT_SEND,
      StoreOrderStatusEnum.WAIT_RECEIVE,
      StoreOrderStatusEnum.FINISHED,
    ].includes(orderDetailRef.value?.status) &&
    routeTypeRef.value == StoreOrderDetailRouteTypeEnum.MY_ORDER &&
    [StoreOrderPayTypeEnum.ONLINE].includes(orderDetailRef.value?.payType) &&
    isArray(orderDetailRef.value?.action) &&
    orderDetailRef.value?.action.includes(CustomerRoleOperationEnum.APPLY_FOR_REFUND)
  )
})

/** 商品是否快递到家 */
const isHomeDelivery = computed(() => {
  return orderDetailRef.value?.pickupType == ProductPickupModeEnum.HOME_DELIVERY
})

/** 商品是否自提 */
const isSelfPickUp = computed(() => {
  return orderDetailRef.value?.pickupType == ProductPickupModeEnum.STORE_PICKUP
})

/** 是否显示底部按钮 */
const isShowBottomBtnRef = computed(() => {
  if (
    [StoreOrderStatusEnum.WAIT_SEND, StoreOrderStatusEnum.WAIT_PAY].includes(
      orderDetailRef.value?.status,
    )
  ) {
    return true
  }
  return false
})

/** 是否下单自动核销 */
const isAutoVerification = computed(() => {
  return orderDetailRef.value?.verificationType == OrderVerificationTypeEnum.AUTO_VERIFICATION
})

/** 背景图 */
const backgroundStyle = computed(() => {
  const backgroundImage =
    orderDetailRef.value?.status === StoreOrderStatusEnum.CANCELLED
      ? OrderDetailCancelledBg
      : OrderDetailBg

  return {
    background: `url(${backgroundImage}) no-repeat`,
    backgroundSize: '100% 100%',
  }
})

onLoad(async (query) => {
  console.log(query)
  try {
    if (query.orderCode) {
      await getOrderDetail(query.orderCode)
    }
    if (query.routeType) {
      routeTypeRef.value = Number(query.routeType)
    }
  } catch (error) {
    console.log(error)
  }
})
</script>

<style lang="scss" scoped>
.store_order_detail_wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;

  .store_order_detail_bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 582rpx;
    z-index: 1;
  }
  .store_order_detail_content {
    width: 100%;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
    padding-bottom: calc(12rpx + 90rpx + env(safe-area-inset-bottom));
    box-sizing: border-box;
    overflow: hidden;
  }
  .footer {
    width: 100%;
    height: 90rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 3;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16rpx;
    box-sizing: border-box;
    margin-bottom: env(safe-area-inset-bottom);
    .btn-group {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin: 16rpx 24rpx;
      .cancellation-order,
      .payment-order,
      .refund-order {
        min-height: 64rpx;
        height: 100%;
        border-radius: 999rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .cancellation-order {
        width: 160rpx;
        background: #ecf5ff;
        color: #4da4ff;
      }
      .payment-order {
        width: 160rpx;
        background: #fff4f4;
        color: #ef1115;
      }
      .refund-order {
        width: 240rpx;
        background: #ef1115;
        color: #ffffff;
      }
    }

    :deep(.van-button__text) {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 48rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
