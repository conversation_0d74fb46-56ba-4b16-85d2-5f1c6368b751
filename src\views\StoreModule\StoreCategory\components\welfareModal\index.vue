<template>
	<!-- 自定义图标 -->
	<van-popup round v-model:show="_show" closeable teleport="body" close-icon="close" position="bottom"
		@close="onClose" :safe-area-inset-bottom='safeBottom' lock-scroll>
		<JLoadingWrapper :show="isLoading">
			<div class="goods-sku-box">
				<div class="goods-content">
					<div class="goods-img">
						<MaskBanner :is-use-type-mask="true"
							:type="tempRef.isPublish == 0 ? 'shelves' : curGoodsInfo.availStocks == 0 ? 'stock' : curGoodsInfo.isDeleted == 1 ? 'specDeleted' : null"
							v-if="curGoodsInfo.disabled">
						</MaskBanner>
						<img :src="tempRef.path || tempRef.firstImg" @click="handlePreviewImage(0)" mode="aspectFill" />
					</div>
					<div class="content-detail">
						<GoodsTitle :state="tempRef" />
						<WelfareContent :sku-info="curGoodsInfo"></WelfareContent>
					</div>
				</div>
				<div class="sku-list">
					<TagBtn class="tag-item" @click='handleSkuClick(item)' v-for="item in skuList" :key="item.specId"
						:active='item.id === curSku' type="warning" :style="customStyleTag">
						{{ item.name || item.specName }}
					</TagBtn>
				</div>
				<div class="add-cart">
					<div class="add-title">数量</div>
					<div class="add-opt">
						<NumberBox :value="cartNum" :max-stock="curGoodsInfo.availStocks"
							@update:value='handleStepChange' :maxSize='curGoodsInfo.upper' />
					</div>
				</div>
				<div class="footer-box">
					<van-button type="default" class="btn" round @click="handleClick"
						:disabled="!curSku || curGoodsInfo.disabled">立即兑换</van-button>
				</div>
			</div>
		</JLoadingWrapper>
	</van-popup>
</template>

<script setup lang="ts">
import { ref, reactive, toRef, computed, watch, nextTick } from "vue"
import { showImagePreview } from 'vant';
import GoodsTitle from "../GoodsTitle.vue"
import NumberBox from "../NumberBox.vue";
import WelfareContent from "../WelfareContent.vue"
import MaskBanner from "../MaskBanner/index.vue";
import TagBtn from "../TagBtn/index.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import { queryWelfareDetail } from "@/services/storeApi/store-category"
import { isObject } from "@/utils/isUtils";
import { useMessages } from "@/hooks/useMessage";
import useWelfare from "./hooks/useWelfare";
import { useRoute, useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";
import type { StyleValue } from "vue";

interface Props {
	/**商品id，可通过id重新获取商品数据 */
	productId?: string;
	/**是否显示弹窗 */
	show: boolean;
	/**当前商品数据 */
	state: any;
	/**是否开启底部安全距离 */
	safeBottom: boolean;
	/**规格id 默认选中最低积分规格*/
	skuId: string | null,
	productIdField?: string;
}
const props = withDefaults(defineProps<Props>(), {
	productId: null,
	show: false,
	safeBottom: true,
	state: () => ({}),
	skuId: null,
	productIdField: 'id'
})
const emits = defineEmits<{
	(e: 'update:show', val: boolean): void;
	(e: 'update:skuId', val: string): void;
	(e: 'refresh', info: any): void;
}>()
const route = useRoute()
const router = useRouter()
const tempRef = ref({ ...props.state })
const message = useMessages()
const { curGoodsInfo, curSku, skuList, handleConfirmOrder, isLoading, cartNum } = useWelfare({
	goodsInfo: tempRef,
	productIdField: props.productIdField
})
const _show = computed({
	get: () => props.show,
	set: (val) => emits('update:show', val)
})
const customStyleTag = computed<StyleValue>(() => {
	return {
		backgroundColor: '#F8F8F8',
		borderRadius: '4px',
		borderColor: '#F8F8F8',
		color: '#000',
	}
})
const handleStepChange = (val: number) => {
	cartNum.value = val
}
const handleClick = () => {
	handleConfirmOrder((info) => {
		_show.value = false
		router.push({
			name: RoutesName.StoreConfirmOrder,
			query: {
				...route.query,
				...info
			}
		})
	})
}
const onClose = () => {
	_show.value = false
}
const handlePreviewImage = (index: number) => {
	showImagePreview({
		images: [tempRef.value.path || tempRef.value.firstImg],
		startPosition: index
	})
}
const handleSkuClick = (val: any) => {
	curSku.value = val.id
	cartNum.value = 1
	emits('update:skuId', curSku.value)
}
const getDetail = async () => {
	try {
		isLoading.value = true
		const res = await queryWelfareDetail(props.productId)
		res[props.productIdField] = res?.id
		tempRef.value = res
	} catch (e) {
		message.createMessageError(`获取失败：${e}`)
	} finally {
		isLoading.value = true
	}
}

watch(() => props.state, (newVal) => {
	if (isObject(newVal)) {
		tempRef.value = newVal
	}
}, {
	deep: true,
})

watch(() => props.productId, (newVal) => {
	if (newVal) {
		getDetail()
	}
})
watch(() => props.skuId, (newVal) => {
	if (newVal) {
		curSku.value = newVal
	}
})
</script>

<style scoped lang="less">
@import '@/styles/storeVar.less';

.shelve {
	filter: opacity(50%);
}

.goods-sku-box {
	font-size: 14px;
	padding: 30px 12px 12px;
	box-sizing: border-box;
	position: relative;
	height: 100%;


	.goods-content {
		margin-top: 15px;
		display: flex;
		align-items: center;

		.goods-img {
			position: relative;
			margin-right: 10px;
			box-sizing: border-box;
			width: 80px;
			height: 80px;

			img {
				width: 100%;
				height: 100%;
				min-width: 80px;
				border-radius: 10px;
			}
		}

		.content-detail {
			height: 80px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}
	}

	.sku-list {
		margin-top: 24px;
		display: flex;
		flex-wrap: wrap;
		max-height: 150px;
		overflow: auto;

		.tag-item {
			margin: 0 10px 10px 0;
		}
	}



	.add-cart {
		margin-top: 15px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.add-title {}

		.add-opt {}
	}

	.footer-box {
		margin-top: 35px;
		width: 100%;
		box-sizing: border-box;

		.btn {
			width: 100%;
			background-color: @error-color;
			color: #fff;
		}
	}
}
</style>