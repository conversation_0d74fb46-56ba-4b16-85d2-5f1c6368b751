export function secondsToDHMS(seconds: number): string {
    // 计算天数
    const days = Math.floor(seconds / (24 * 3600));
    seconds %= 24 * 3600;

    // 计算小时
    const hours = Math.floor(seconds / 3600);
    seconds %= 3600;

    // 计算分钟
    const minutes = Math.floor(seconds / 60);
    seconds %= 60;

    // 格式化为两位数，前面补零
    return `${days.toString().padStart(2, '0')}天${hours.toString().padStart(2, '0')}小时${minutes.toString().padStart(2, '0')}分${seconds.toString().padStart(2, '0')}秒`;
}