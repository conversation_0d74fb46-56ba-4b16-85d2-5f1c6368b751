import { ref } from "vue";
import { getStructureApplyInfoApi } from "@/services/storeApi";

export default function useGetApplicationRecord() {
  const isPageLoadingRef = ref(false);
  /** 扫组织申请码后回显申请记录 */
  async function getApplicationRecord(_params: {
    structureId?: string | number;
    applyType?: string;
  }) {
    try {
      isPageLoadingRef.value = true;
      const applyInfo = await getStructureApplyInfoApi(_params);
      if (applyInfo) {
        return applyInfo;
      }
    } catch (error) {
      throw error;
    } finally {
      isPageLoadingRef.value = false;
    }
  }

  return {
    isPageLoadingRef,
    getApplicationRecord,
  }
}
