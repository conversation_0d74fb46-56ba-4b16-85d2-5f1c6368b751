<template>
	<view class="content-header" v-bind="$attrs">
		<template v-if="isAutoComputedTitle">
			<span v-if="state.type == GoodsTypeEnum.OTC_DRUG">[{{ state.frontName }}]{{ state.name }}{{ specName }}</span>
			<span v-else>{{ state.frontName }}</span>
		</template>
		<template v-else>
			<span>{{ title }}</span>
		</template>
	</view>
</template>

<script setup lang="ts">
import { computed, type StyleValue } from "vue";
import { GoodsTypeEnum } from "@/enums/storeGoods";
import { filterSkuMin } from "@/utils/storeUtils";
const props = withDefaults(defineProps<{
	state: any,
	title: string,
	isAutoComputedTitle: boolean,
	//自定义规格名称字段
	customlSkuField:string,
}>(), {
	state: () => ({}),
	title: '',
	isAutoComputedTitle:true,
	customlSkuField:'specName'
})
const specName = computed(() => {
	let result = ''
	if (props.state.appletProductSpecDTOList) {
		//获取最小价格的规格
		result = filterSkuMin(props.state.appletProductSpecDTOList)?.name || ''
	} else {
		result = props.state[props.customlSkuField] || ''
	}
	return result
})
</script>

<style lang="less" scoped>
.content-header {
	width: 100%;
	word-break: break-all;
	font-weight: bold;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-size: 16px;
	line-height: 20px;
}
</style>