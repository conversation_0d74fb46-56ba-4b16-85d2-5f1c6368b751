<template>
    <div class="welfare-box" v-bind="$attrs">
        <span v-if="title">{{ title }}</span>
        <span v-else :style="priceStyle">{{ props.num || props.skuInfo?.exchangeCount || 0 }}张{{ props.skuInfo?.couponCateName || props.skuInfo?.couponName || '' }}</span>
        <span class="suffix" v-if="isShowSuffux" :style="{fontSize:isNumber(props.suffixSize) ? `${props.suffixSize}px` : props.suffixSize}">{{ props.suffix }}</span>
    </div>
</template>

<script setup lang="ts">
import { ref,computed,type StyleValue } from "vue";
import { isNumber } from "@/utils/isUtils";
const props = withDefaults(defineProps<{
    title?: string,
    num?: number,
    suffix?: string,
    titleSize?: number,
    suffixSize?: number,
    isShowSuffux?: boolean,
    skuInfo?: any,
    customStyle?: StyleValue
}>(), {
    title: '',
    num: 0,
    titleSize: 16,
    suffixSize: 12,
    suffix: '可兑',
    isShowSuffux: true,
    skuInfo:()=>({}),
    customStyle:()=>({})
})
const priceStyle = computed(()=>{
    return [
        props.customStyle,
        {fontSize:isNumber(props.titleSize) ? `${props.titleSize}px` : props.titleSize}
    ]
})
</script>

<style lang="less" scoped>
@import url("@/styles/storeVar.less");

.welfare-box {
    color: @error-color;
    margin-right: 10px;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: baseline;

    .suffix {
        white-space: nowrap;
        color: #666666;
    }
}
</style>