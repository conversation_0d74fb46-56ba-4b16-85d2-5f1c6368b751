import { ref, reactive } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { GoodsCategoryEnum } from "@/views/StoreModule/enums";
import { getStoreNewProductList } from "@/services/storeApi";
/** 静态资源 */
import CouponSrc from "@/assets/storeImage/storeHome/coupon.png";

export default function useGetStoreCommodity(_params: { storeId: string }) {
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 商品列表数据 */
  const commodityList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 获取搜索参数 */
  function getSearchParams() {
    return {
      data: {
        storeId: _params.storeId,
        type: GoodsCategoryEnum.NORMAL,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取商品列表数据 */
  async function getStoreCommodity() {
    const { current, size } = pageVO;

    try {
      const _params = getSearchParams();

      const { records, total } = await getStoreNewProductList(_params);
      if (!records?.length) return;

      if (current === 1) {
        commodityList.value = records;
      } else if (records.length) {
        commodityList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });

      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError(`获取商品失败：${error}`);
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
    }
  }

  /** 加载分页数据 */
  function onLoad() {
    console.log(1111);
    
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getStoreCommodity();
    }
  }

  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    getStoreCommodity();
  }

  /** 商品列表数据初始化 */
  async function initStoreCommodity() {
    ininParams();
    await getStoreCommodity();
  }

  return {
    commodityList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onLoad,
    onRefresh,
    initStoreCommodity,
  };
}
