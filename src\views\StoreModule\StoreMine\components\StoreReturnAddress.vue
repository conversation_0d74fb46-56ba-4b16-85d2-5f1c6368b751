<template>
  <VanDialog
    v-model:show="showRef"
    show-cancel-button
    confirm-button-text="确定"
    confirm-button-color="#EF1115"
    @confirm="confirm"
  >
    <div class="store_merchant_return_info">
      <div class="store_merchant_return_info_title">选择退货地址</div>
      <template v-if="supplierIdRef">
        <!-- 供应商名称 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">供应商名称</span>
          <span class="store_merchant_return_info_item_value">{{ supplierAddress?.companyName }}</span>
        </div>
        <!-- 联系人姓名 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">联系人姓名</span>
          <span class="store_merchant_return_info_item_value">{{ supplierAddress?.contactName }}</span>
        </div>
        <!-- 供应商电话 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">供应商电话</span>
          <span class="store_merchant_return_info_item_value">{{ supplierAddress?.contactPhone }}</span>
        </div>
        <!-- 供应商地址 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">供应商地址</span>
          <span class="store_merchant_return_info_item_value">{{ formattedSupplierAddress }}</span>
        </div>
      </template>
      <template v-else>
        <!-- 门店名称 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">门店名称</span>
          <span class="store_merchant_return_info_item_value">{{ storeInfoRef?.storeName }}</span>
        </div>
        <!-- 店长 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">门店联系人</span>
          <span class="store_merchant_return_info_item_value">{{ storeInfoRef?.contactName }}</span>
        </div>
        <!-- 联系电话 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">联系号码</span>
          <span class="store_merchant_return_info_item_value">{{ storeInfoRef?.contactPhone }}</span>
        </div>
        <!-- 收货地址 -->
        <div class="store_merchant_return_info_item">
          <span class="store_merchant_return_info_item_label">收货地址</span>
          <span class="store_merchant_return_info_item_value">{{ formattedAddress }}</span>
        </div>
      </template>
    </div>
  </VanDialog>
</template>

<script lang="ts" setup>
import { toRefs, computed, watch } from "vue";
import { useGetStoreInfo, useGetSupplierAddress } from "@/views/StoreModule/hooks";

defineOptions({ name: 'StoreReturnAddress' });

/** props */
const props = withDefaults(defineProps<{
  show: boolean;
  belongStoreId: string;
  supplierId: string;
}>(), {
  supplierId: null,
});

/** emits */
const emits = defineEmits<{
    (e: 'update:show', show: boolean): void
    (e: 'confirm'): void
}>();

const { belongStoreId: belongStoreIdRef, supplierId: supplierIdRef } = toRefs(props);

console.log("belongStoreIdRef.value", belongStoreIdRef.value);


/** 订单归属门店 */
const { storeInfo: storeInfoRef, getStoreInfoByStoreId } = useGetStoreInfo();

/** 供应商地址 */
const { supplierAddress, getSupplierAddressList } = useGetSupplierAddress();

/** 门店退货收货地址 */
const formattedAddress = computed(() => {
  const { province = '', city = '', area = '', addressDetail = '' } = storeInfoRef.value || {};
  const address = `${province}${city}${area}${addressDetail}`.trim();
  return address || '暂无地址';
});

/** 供应商退货收货地址 */
const formattedSupplierAddress = computed(() => {
  const { province = '', city = '', district = '', addressDetail = '' } = supplierAddress.value || {};
  const address = `${province}${city}${district}${addressDetail}`.trim();
  return address || '暂无地址';
});

const showRef = computed({
  get() {
    return props.show;
  },
  set(show: boolean) {
    emits('update:show', show);
  }
});

function confirm() {
  emits('confirm');
}

/** 监听 */
watch(() => props.show, async (newVal) => {
  if (newVal) {
    console.log("supplierIdRef.value", supplierIdRef.value, "belongStoreIdRef.value", belongStoreIdRef.value);
    
    if (supplierIdRef.value) {
      await getSupplierAddressList(supplierIdRef.value);
    } else {
      await getStoreInfoByStoreId(belongStoreIdRef.value);
    }
  }
});
</script>

<style lang="less" scoped>
.store_merchant_return_info {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    .store_merchant_return_info_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 16px;
    }
    .store_merchant_return_info_item {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;
        .store_merchant_return_info_item_label {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_merchant_return_info_item_value {
            max-width: 260px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }
}
</style>
