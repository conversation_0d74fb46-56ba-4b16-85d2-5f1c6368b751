<template>
  <VanPopup
    :show="showRef"
    @update:show="handleUpdateShow"
    round
    position="bottom"
    :closeable="false"
    safe-area-inset-bottom
    close-icon="close"
    teleport="body"
    :style="popupStyle"
  >
    <div class="wrapper">
      <div class="form-wrapper">
        <!-- 暂不填写 -->
        <div class="title-wrapper">
          <span @click="handleNotFill">暂不填写</span>
          <img :src="addressIconSrc" alt="" />
          <p>——快速抢购商品</p>
        </div>
        <!-- 收件人 -->
        <div class="form-item">
          <span class="tip">*</span>
          <span style="margin-right: 8px;width: 56px;">收件人</span>
          <VanField
            v-model="formValue.name"
            placeholder="填写收件人姓名"
            :maxlength="200"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
        </div>
        <!-- 手机号 -->
        <div class="form-item">
          <span class="tip">*</span>
          <span style="margin-right: 8px;width: 56px;">手机号码</span>
          <VanField
            v-model="formValue.mobile"
            placeholder="填写手机号码"
            :maxlength="200"
            style="flex: 1;background: #F8F8F8;padding: 8px 8px 8px 64px;border-radius: 4px;"
          />
          <span class="phone">
            +86
            <VanIcon name="arrow-down" style="margin-left: 2px;" />
          </span>
        </div>
        <!-- 所在地区 -->
        <div class="form-item">
          <span class="tip">*</span>
          <span style="margin-right: 8px;width: 56px;">所在地区</span>
          <VanField
            v-model="addressStr"
            readonly
            rows="1"
            autosize
            type="textarea"
            placeholder="选择区域"
            :maxlength="200"
            @click="showAddress = true"
            style="flex: 1;background: #F8F8F8;padding: 8px 16px 8px 8px;border-radius: 4px;"
          />
          <span class="address">
            <VanIcon name="arrow" />
          </span>
        </div>
        <!-- 详细地址 -->
        <div class="form-item" style="align-items: normal;">
          <span class="tip" style="margin-top: 4px;">*</span>
          <span style="margin-right: 8px;width: 56px;margin-top: 4px;">详细地址</span>
          <VanField
            v-model="formValue.address"
            :rows="3"
            autosize
            type="textarea"
            placeholder="填写详细地址"
            :maxlength="200"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
        </div>
        <!-- 设置默认地址 -->
        <div class="form-item" style="margin-bottom: 24px;justify-content: space-between;">
          <span style="margin-right: 8px;width: 120px;">设置默认地址</span>
          <VanSwitch
            v-model="formValue.isDefault"
            :active-value="1"
            :inactive-value="0"
            active-color="#EF1115"
            size="20px"
          />
        </div>
      </div>
      <!-- footer -->
      <div class="footer">
        <VanRow justify="space-between" gutter="8">
          <VanCol span="24">
            <VanButton type="danger" @click="handleSaveAddress" round block style="width: 100%;height: 36px;">保存</VanButton>
          </VanCol>
        </VanRow>
      </div>
    </div>
  </VanPopup>
  <!-- 地址选择 -->
  <JStoreAddress v-model:show="showAddress" @confirm="handleAddressConfirm" style="z-index: 9999;" />
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import { createCacheStorage } from '@/utils/cache/storageCache';
import { CacheConfig } from '@/utils/cache/config';
import { addAddress } from "@/services/storeApi";
/** 静态资源 */
import addressIconSrc from "@/assets/storeImage/storeHome/addressTip.png";

/** 相关组件 */
import JStoreAddress from "@/views/StoreModule/components/JStoreAddress.vue";

defineOptions({ name: 'StoreAddressForm' });

/** props */
const props = defineProps<{
  show: boolean;
}>();

/** emit */
const emit = defineEmits<{
  (e: 'update:show', val: boolean): void;
  // 关闭
  (e: 'close'): void;
}>();

/** 表单参数 */
const initparams = {
  name: '', // 收件人姓名
  mobile: '', // 联系电话
  address: '', // 详细地址
  isDefault: false,
  province: '',
  provinceId: null,
  cityName: '',
  cityId: null,
  area: '',
  areaId: null,
  town: '',
  townId: null,
};
const formValue = ref({ ...initparams });

/** 收货区域 */
const addressStr = computed(() => {
  const { province, cityName, area, town } = formValue.value;
  return [province, cityName, area, town].filter(Boolean).join('/');
});

const { createMessageError, createMessageSuccess } = useMessages();
const showAddress = ref(false);
function handleAddressConfirm(addressData: {
  area: string;
  areaId: string;
  city: string;
  cityId: string;
  province: string;
  provinceId: string;
  town: string;
  townId: string;
}) {
  const { province, provinceId, city, cityId, area, areaId, town, townId } = addressData;
  Object.assign(formValue.value, {
    province,
    provinceId,
    cityName: city,
    cityId,
    area,
    areaId,
    town,
    townId
  });
}

const showRef = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

function handleUpdateShow(val: boolean) {
  emit('update:show', val);
}

const popupStyle = computed(() => ({
  height: 'auto',
}));

/** 暂不填写 */
function handleNotFill() {
  const hasShowFillAddressFormStorage = createCacheStorage(CacheConfig.StoreHasShowFillAddressForm);
  hasShowFillAddressFormStorage.set({ hasDoNotFillAddressForm: true });

  emit('close');
}

/** 获取参数 */
function _getParams() {
  const { name, mobile, address, isDefault, provinceId, province, cityId, cityName, areaId, area, townId, town } = formValue.value;
  return {
    name: name.trim(),
    mobile: mobile.trim(),
    address: address.trim(),
    isDefault: isDefault ? 1 : 0,
    province: province,
    provinceId: provinceId,
    cityName,
    cityId,
    areaId: areaId,
    area: area,
    town,
    townId,
  };
}

/** 保存 */
async function handleSaveAddress() {
  try {
    // 校验收件人姓名
    if (!formValue.value.name || formValue.value.name.trim() === '') {
      showToast('请输入收件人姓名');
      return;
    }

    // 校验手机号格式
    const mobileRegex = /^1[3-9]\d{9}$/;
    if (!formValue.value.mobile || !mobileRegex.test(formValue.value.mobile)) {
      showToast('请输入正确的手机号码');
      return;
    }

    // 校验所在地区
    if (!formValue.value.province || formValue.value.province.trim() === '') {
      showToast('请选择所在地区');
      return;
    }

    // 校验详细地址
    if (!formValue.value.address || formValue.value.address.trim() === '') {
      showToast('请输入详细地址');
      return;
    }

    const _params = _getParams();
    await addAddress(_params);
    showToast('保存地址成功');
    emit('close');
  } catch (error) {
    console.error('保存地址失败:', error);
    createMessageError('保存地址失败：' + error);
  }
}
</script>

<style lang="less" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-image: url("@/assets/storeImage/storeHome/addressBg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: calc(100% - env(safe-area-inset-bottom));
  box-sizing: border-box;
  padding: 16px 12px 0px 12px;

  .form-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;

    .title-wrapper {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      text-align: right;
      font-style: normal;
      text-transform: none;
      display: flex;
      flex-direction: column;
      gap: 8px;

      img {
        width: 197px;
        height: 80px;
        margin-left: auto;
        margin-right: 20px;
      }

      p {
        margin-right: 20px;
      }
    }

    .form-item {
      display: flex;
      align-items: center;
      position: relative;

      .tip {
        color: #FF3E3E;
        margin-top: 4px;
      }

      span {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .phone {
        position: absolute;
        left: 86px;
      }

      .address {
        position: absolute;
        right: 12px;
      }
    }
  }

  .footer {
    box-sizing: border-box;
    :deep(.van-button__text) {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }
  }
}

/* 其他样式保持不变 */
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

:deep(.van-field__control) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
