<template>
  <VanPopup
    :show="showRef"
    @update:show="handleUpdateShow"
    round
    position="bottom"
    closeable
    safe-area-inset-bottom
    close-icon="close"
    teleport="body"
    style="height: auto;"
  >
    <div class="wrapper">
      <div class="coupon-redeem-header">
        <img :src="CouponSrc" alt="" />
        <span class="coupon-redeem-header-title">{{ couponInfoRef?.categoryName ?? `福利券`  }}</span>
      </div>
      <div class="coupon-redeem-content">
        <div class="coupon-redeem-content-item">核销数量</div>
        <VanStepper
          v-model="redeemNumRef"
          :max="maxNumRef"
          theme="round"
          button-size="22"
          disable-input
          :style="{ '--van-stepper-button-round-theme-color': '#EF1115' }"
        />
      </div>
      <VanButton type="danger" block round @click="handleRedeem">确定</VanButton>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { CouponStatusEnum } from "@/views/StoreModule/enums";
import { verifyCoupon } from "@/services/storeApi";
/** 静态资源 */
import CouponSrc from "@/assets/storeImage/storeHome/coupon.png";

defineOptions({ name: 'CouponRedeem' });

/** props */
const props = withDefaults(defineProps<{
  show: boolean;
  userId: string;
  couponInfo: {
    categoryId?: string;
    categoryName?: string;
    description?: string;
    useStatus?: CouponStatusEnum;
    unUseNum?: number;
    useNum?: number;
    invalidNum?: number;
    exchangeNote?: string;
  };
}>(), { });

/** emit */
const emit = defineEmits<{
  (e: 'update:show', val: boolean): void;
  (e: 'success'): void;
}>();

const { couponInfo: couponInfoRef } = toRefs(props);
const { createMessageSuccess, createMessageError } = useMessages();

const showRef  = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

const maxNumRef = computed(() => couponInfoRef.value?.unUseNum || 0);

const redeemNumRef = ref(0);

function handleUpdateShow(val: boolean) {
  emit('update:show', val);
}

function getSearchParams() {
  return {
    userId: props.userId,
    couponId: couponInfoRef.value?.categoryId,
    redeemNum: redeemNumRef.value,
  };
}

async function handleRedeem() {
  const _params = getSearchParams();

  try {
    await verifyCoupon(_params);
    emit('update:show', false);
    emit('success');
    createMessageSuccess('核销福利券成功');
  } catch (error) {
    createMessageError(`核销福利券失败：${error}`);
  }
}
</script>

<style lang="less" scoped>
.wrapper {
  padding: 40px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  .coupon-redeem-header {
    display: flex;
    align-items: center;
    gap: 8px;
    img {
      width: 36px;
      height: 36px;
      border-radius: 8px;
    }
    .coupon-redeem-header-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      line-height: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .coupon-redeem-content {
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .coupon-redeem-content-item {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
