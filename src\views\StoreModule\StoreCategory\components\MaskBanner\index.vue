<template>
    <div class="mask" :style="_customStyle">
        <template v-if="isUseTypeMask">
            <div class="pres-mask" v-if="type === 'pres'">
                <div>{{ typeMap['pres'] }}</div>
                <div>请在药师指导</div>
                <div>下使用</div>
            </div>
            <div class="stock-mask" v-else-if="type === 'stock'">
                {{ typeMap['stock'] }}
            </div>
            <div class="normal-mask" v-else-if="isNormalType">
                {{ typeMap[type] }}
            </div>
        </template>
        <slot v-else></slot>
    </div>
</template>

<script setup lang="ts">
import { ref,type StyleValue } from "vue";
import { computed } from "vue";
type MaskType = 'stock' | 'pres' | 'shelves' | 'stockPlain' | 'specDeleted'
const props = withDefaults(defineProps<{ isFixed: boolean, isUseTypeMask: boolean, type: MaskType,customStyle:StyleValue }>(), {
    /**是否使用定位 */
    isFixed: true,
    /**是否使用类型遮罩 */
    isUseTypeMask: false,
    type: null,
    customStyle:()=>({})
})
const typeMap: Record<MaskType, string> = {
    'pres': '处方药',
    'stock': '无库存',
    'shelves': '已下架',
    'stockPlain': '无库存',
    'specDeleted': '规格已删除'
}
const normalTypeKeys = ref<string[]>(['stockPlain', 'shelves', 'specDeleted'])
const isNormalType = computed(() => {
    return normalTypeKeys.value.includes(props.type)
})
const _customStyle = computed(() => {
    let styles:StyleValue = {};
    if (props.isFixed) {
        styles = {
            position: 'absolute',
            left: 0,
            top: 0,
            zIndex: 1,
        }
    }
    return [styles,props.customStyle]
})
</script>

<style scoped lang="less">
.mask {
    border-radius: 4px;
    width: 100%;
    height: 100%;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, .4);
    font-size: 11px;

    .pres-mask {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        div {
            text-align: center;
        }
    }

    .stock-mask {
        background-color: #FF6864;
        border-radius: 50%;
        color: #fff;
        padding: 20px 10px;
    }

    .normal-mask {}
}
</style>