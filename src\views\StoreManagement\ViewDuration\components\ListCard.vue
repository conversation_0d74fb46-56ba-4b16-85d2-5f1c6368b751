<template>
  <div class="view-duration-card">
    <div class="view-duration-card-wap">
      <div class="view-duration-card-wap-left">
        <VanImage 
          width="60px" 
          height="60px" 
          radius="50%" 
          fit="cover" 
          :src="data.img"
          :alt="`${data.memberName}的头像`"
        />
        <div class="info">
          <div class="name">{{ data.memberName }}</div>
          <div class="id">ID：{{ data.memberShortId }}</div>
        </div>
      </div>
      <div class="view-duration-card-wap-right">{{ formattedDuration }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface MemberData {
  memberShortId: string;
  watchSeconds: number;
  memberName: string;
  img: string;
}

interface ListCardProps {
  data: MemberData;
}

const props = withDefaults(defineProps<ListCardProps>(), {
  data: () => ({
    memberShortId: "",
    watchSeconds: 0,
    memberName: "",
    img: "",
  }),
});

const formattedDuration = computed(() => {
  return `${Math.floor(props.data.watchSeconds / 60)}分钟`;
});
</script>

<style scoped lang="less">
.view-duration-card {
  width: 100%;
  font-size: 15px;
  margin-bottom: 10px;
  .view-duration-card-wap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100% - 12px * 2);
    background-color: white;
    margin: auto;
    border-radius: 15px;
    padding: 12px;
    &-left {
      display: flex;
      align-items: center;
      .info {
        margin-left: 10px;
        .name {
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>