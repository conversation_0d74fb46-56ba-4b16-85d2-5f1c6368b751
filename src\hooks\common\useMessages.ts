export default function useMessages() {
  return {
    createMessageSuccess: (props: string) => {
      uni.showToast({
        title: props,
        icon: 'none',
        duration: 1000,
      })
    },
    createMessagePrimary: (props: string) => {
      uni.showToast({
        title: props,
        icon: 'none',
        duration: 1000,
      })
    },
    createMessageDanger: (props: string) => {
      uni.showToast({
        title: props,
        icon: 'none',
        duration: 1000,
      })
    },
    createMessageInfo: (props: string) => {
      uni.showToast({
        title: props,
        icon: 'none',
        duration: 1000,
      })
    },
    createMessageError: (props: string) => {
      uni.showToast({
        title: props,
        icon: 'none',
        duration: 1000,
      })
    },
    createMessageWarning: (props: string) => {
      uni.showToast({
        title: props,
        icon: 'none',
        duration: 1000,
      })
    },
    createMessageExportSuccess: (msg: string) => {
      uni.showToast({
        title: msg,
        icon: 'none',
        duration: 1000,
      })
    },
    destoryMessage: () => {},
  }
}
