require('module-alias/register')
const { spawn } = require('child_process')
const {
  cleanupByDirectory,
  coverFileContentByPath,
  // filterLargeImages,
} = require('./utils/fileUtils.js')

/**
 * 执行命令并返回一个 Promise
 * @param {string} command - 要执行的命令
 * @returns {Promise<number>} - 返回命令执行的退出码
 */
function execPromise(command) {
  return new Promise((resolve, reject) => {
    const cmdList = command.split(' ')
    console.log('-----------------exec command-----------------')
    console.log(`${command}`)
    console.log('----------------------------------------------')

    const child = spawn(cmdList[0], cmdList.slice(1), { shell: true })

    // 监听标准输出流
    child.stdout.on('data', (data) => {
      console.log(`${data}`)
    })

    // 监听标准错误流
    child.stderr.on('data', (data) => {
      console.error(`${data}`)
    })

    // 监听错误事件
    child.on('error', (error) => {
      reject(`${command} exec error: ${error}`)
    })

    // 监听子进程关闭事件
    child.on('close', (code) => {
      resolve(code)
    })
  })
}

const Prebuild_Directory = './scripts/preBuild'
const Mp_Project_Directory = './src'
/** 过滤图片大小，单位：KB */
const IMAGESIZE = 20
/** 压缩图片大小，单位：KB */
const COMPRESSSIZEKB = 100
/**
 * @description 打包构建
 * @param {*} isDev 是否是开发模式
 */
exports.build = async (isDev) => {
  try {
    // 清理预构建目录
    await cleanupByDirectory(Prebuild_Directory)

    // 使用 TypeScript 编译预构建项目
    await execPromise('npx tsc --project pre_build_tsconfig.json')

    // 引入预构建的页面配置
    const { prodConfig } = require('./preBuild/src/config/pagesConfig.js')

    // 覆盖 pages.json 文件内容
    await coverFileContentByPath(`${Mp_Project_Directory}/pages.json`, JSON.stringify(prodConfig))

    // 过滤大图片并压缩
    // await filterLargeImages(
    //   `${Mp_Project_Directory}/static/images`,
    //   `./large_images/images`,
    //   `${Prebuild_Directory}/largeImageSrcList.js`,
    //   IMAGESIZE,
    //   COMPRESSSIZEKB,
    // )

    // 根据是否为开发环境执行不同的构建命令
    await execPromise(isDev ? 'npx uni -p mp-weixin' : 'npx uni build -p mp-weixin')
  } catch (e) {
    // 捕获并打印构建错误
    console.log(`build error: ${e}`)
  }
}
