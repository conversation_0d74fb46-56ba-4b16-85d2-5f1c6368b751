import { ref, watch, computed, type StyleValue } from "vue";
import { queryGoodsDetail, queryIntergralDetail, queryWelfareDetail } from "@/services/storeApi/store-category";
import { saleComputedSum, genSaleCount } from "@/utils/storeUtils";
import { useMessages } from "@/hooks/useMessage";
import { isNullOrUnDef } from "@/utils/isUtils";
import { getStoreBasicInfo } from "@/services/storeApi";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { StoreGoodsEnum } from "@/enums/storeGoods";
export default function (type: StoreGoodsEnum) {
  const { storeUserInfo } = useUserStoreWithoutSetup();
  const message = useMessages();
  const detailId = ref<string>(null);
  //商品数据 
  const tempGoodsInfo = ref<any>({});
  //用于单规格
  const skuInfo = ref<any>({});
  const showSku = ref<boolean>(false);
  const showWelfareModal = ref<boolean>(false);
  const goodLoading = ref<boolean>(false);
  //初始化sku
  const isInitSku = ref<boolean>(true);
  //积分商品
  const showIntegralModal = ref<boolean>(false);
  //当前商品类型
  const curType = computed(() => {
    return tempGoodsInfo.value.type || null;
  });
  //门店信息
  const storeInfo = ref<any>({});
  //已售数量
  const initSaled = computed<string>(() => {
    const _text = type == StoreGoodsEnum.Goods ? "已售" : "已兑换";
    let list = [];
    if (type == StoreGoodsEnum.IntegralGoods) {
      list = tempGoodsInfo.value?.appletPointSpecDTOS || [];
    }
    if (type == StoreGoodsEnum.Goods) {
      list = tempGoodsInfo.value?.appletProductSpecDTOList || [];
    }
    if (type == StoreGoodsEnum.WelfareTicket) {
      list = tempGoodsInfo.value?.couponProductSpecList || [];
    }
    const sumCount = genSaleCount(list);
    return _text + saleComputedSum(sumCount);
  });
  //首图
  const firstImg = computed<string>(() => {
    return tempGoodsInfo.value.path || tempGoodsInfo.value.firstImg;
  });
  //轮播图
  const productImgDTOList = computed<any[]>(() => {
    let list = [];
    if (type == StoreGoodsEnum.IntegralGoods) {
      list = tempGoodsInfo.value?.productImgDTOList || [];
    }
    if (type == StoreGoodsEnum.Goods) {
      list = tempGoodsInfo.value?.appletProductImgDTOList || [];
    }
    if (type == StoreGoodsEnum.WelfareTicket) {
      list = tempGoodsInfo.value?.couponProductImgDTOList || [];
    }
    return list;
  });
  const productRemark = computed<string>(() => {
    return (tempGoodsInfo.value.therapyDrugDetailDTO || {}).remark || "";
  });
  const productInsertsDTO = computed(() => {
    const info = tempGoodsInfo.value?.productInsertsDTO || {};
    if (tempGoodsInfo.value) {
      info.name = tempGoodsInfo.value.name;
      info.frontName = tempGoodsInfo.value.frontName;
      info.manufacturer = tempGoodsInfo.value.manufacturer;
    }
    return info;
  });
  const getDetail = async (callback?: () => void) => {
    try {
      goodLoading.value = true;
      let api;
      if (type == StoreGoodsEnum.Goods) {
        api = queryGoodsDetail;
      }
      if (type == StoreGoodsEnum.IntegralGoods) {
        api = queryIntergralDetail;
      }
      if (type == StoreGoodsEnum.WelfareTicket) {
        api = queryWelfareDetail;
      }
      const res = await api(detailId.value);
      tempGoodsInfo.value = res || {};
      callback && callback();
    } catch (e) {
      message.createMessageError(`获取失败：${e}`);
    } finally {
      goodLoading.value = false;
    }
  };
  /** 获取门店基础信息 */
  async function getStoreInfoByStoreId() {
    try {
      const _params = { id: storeUserInfo.storeId };
      const resp = await getStoreBasicInfo(_params);
      storeInfo.value = resp;
    } catch (error) {
      console.log(error);
    }
  }

  const customStyleTag = computed<StyleValue>(() => {
    return {
      backgroundColor: "#F8F8F8",
      borderColor: "#F8F8F8",
      color: "#000",
    };
  });

  return {
    goodLoading,
    tempGoodsInfo,
    initSaled,
    showSku,
    detailId,
    productInsertsDTO,
    skuInfo,
    getDetail,
    productRemark,
    customStyleTag,
    curType,
    showIntegralModal,
    productImgDTOList,
    isInitSku,
    firstImg,
    storeInfo,
    showWelfareModal,
    getStoreInfoByStoreId,
  };
}
