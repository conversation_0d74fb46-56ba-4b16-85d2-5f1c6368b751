import { ref, computed } from "vue";
import { queryMyCashCoupon } from "@/services/storeApi";
export function useCoupon() {
  const couponUseList = ref([]);
  const isUseCoupon = computed(() => couponUseList.value.length > 0);
  const getUseCouponList = async (maxAmt: number) => {
    try {
      let res = await queryMyCashCoupon();
      if (!res.couponReceiveRecordList) {
        return;
      }
      couponUseList.value = res?.couponReceiveRecordList.filter(item => item.cashCouponAmt <= maxAmt);
    } catch (error) {
      console.log(error);
    }
  };
  return {
    isUseCoupon,
    couponUseList,
    getUseCouponList,
  };
}
