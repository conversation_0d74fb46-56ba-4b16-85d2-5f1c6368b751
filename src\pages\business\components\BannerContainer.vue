<template>
  <view class="wrapper">
    <view class="banner-header">
      <slot name="title"></slot>
      <slot name="headerRight"></slot>
    </view>
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
defineOptions({ name: '<PERSON><PERSON>ontaine<PERSON>' })
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  background: linear-gradient(180deg, #fff3ea 0%, #ffffff 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
  .banner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }
}
</style>
