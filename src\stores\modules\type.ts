import { UserType, StoreQualityEnum, StorePayMode, StoreFyPayMode } from '@/enum'

export interface UserInfoInterface {
  img: string
  nickname: string
  mobile: string
  idNo: string
  name: string
  gender: string
  isAuth: boolean
  id: string
  /**当前登录用户的type，默认为会员 */
  type: UserType
  /**群管id */
  mgrId?: string
}

export interface UserInfoState {
  _token: string | null
  _userInfo: UserInfoInterface
  //   isBindGroup: boolean
  defaultChannelId: string
}

export interface SystemInterface {
  /** 版本 */
  version: string
  /** 是否限制显示健康板块内容 */
  isLimit: boolean
  /** 是否开启积分商城 */
  isShowIntegralStore: boolean
  /** 商城性质 */
  storeQuality: StoreQualityEnum
  /** 导航配置 */
  navigationConfigList?: []
  /** 支付方式  */
  payMode: StorePayMode
  /** 富友支付方式 */
  fyPayMode: StoreFyPayMode
  /** 商城类型 */
  storeType: number
}
