const fs = require('fs')
const path = require('path')
// const sharp = require("sharp");

/**
 * 清理指定目录
 * @param {string} directory - 要清理的目录路径
 * @returns {Promise<boolean>} - 返回一个 Promise，表示操作是否成功
 */
exports.cleanupByDirectory = (directory) => {
  return new Promise((resolve, reject) => {
    if (fs.existsSync(directory)) {
      try {
        // 递归删除目录及其内容
        fs.rmdirSync(directory, { recursive: true })
        resolve(true)
      } catch (err) {
        reject(`read directory error: ${err}`)
      }
    } else {
      resolve(true)
    }
  })
}

/**
 * 覆盖指定文件的内容
 * @param {string} filePath - 文件路径
 * @param {string} content - 要写入的内容
 * @returns {Promise<boolean>} - 返回一个 Promise，表示操作是否成功
 */
exports.coverFileContentByPath = (filePath, content) => {
  return new Promise((resolve, reject) => {
    try {
      const folderPath = path.dirname(filePath)
      // 确保目录存在
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true })
      }
      // 写入文件内容
      fs.writeFileSync(filePath, content, 'utf8')
      resolve(true)
    } catch (e) {
      reject(`coverFileContentByPathError: ${e}`)
    }
  })
}
