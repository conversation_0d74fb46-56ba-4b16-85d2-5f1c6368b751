<template>
  <div class="listWarpper">
    <div class="contioner">
      <div class="title">
        <div class="vertical-line"></div>
        <div class="titleSpan">
          <div class="name">{{props.fixed?'店员合计':listData.staffNickname}}</div>
          <div
            v-if="!props.fixed"
            :class="['span',{'blueStyle':listData.identityType == 1},{'yellowBg':listData.identityType != 1}]"
          >
            {{listData.identityType == 1 ? '店长':'店员'}}
          </div>
        </div>
      </div>
      <div :class="['content',{'fixedStyle':props.fixed},{'listCard':!props.fixed}]">
        <div class="list">
          <div v-for="item in list" class="item">
            <div>{{ item.lable }}</div>
            <div class="num">{{ item.value }}</div>
          </div>
        </div>
      </div>
      <div v-if="!props.fixed" class="footer">
        <div class="btn" @click="handleToMember">会员</div>
        <div class="btn" @click="handleToSalesStatistics">销售统计</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { RoutesName } from "@/enums/routes";
import { ref,reactive,computed } from "vue";
import { useRouterUtils } from "@/views/StoreModule/hooks";
type ListCardProps = {
    fixed:boolean,
    listData:any
}
const type = ref(1)
const props = withDefaults(defineProps<ListCardProps>(),{
    fixed:false,
    listData:{}
})
const { routerPushByRouteName } = useRouterUtils();
const list = reactive([
    {lable:'销售订单数',value:props.listData.orderNum || 0},
    {lable:'销售商品数',value:props.listData.productNum || 0},
    {lable:'销售订单额',value:props.listData?.orderAmount ? (props.listData.orderAmount / 100).toFixed(2) : 0},
    {lable:'会员人数',value:props.listData.csNum || 0},
    {lable:'退款订单数',value:props.listData.refundOrderNum || 0},
    {lable:'退款商品数',value:props.listData.refundProductNum || 0},
    {lable:'退款订单额',value:props.listData?.refundOrderAmount ? (props.listData.refundOrderAmount / 100).toFixed(2) : 0},
    {lable:'新增会员数',value:props.listData.incrCsNum || 0},
])

/** 点击会员 */
const handleToMember = () => {
    const { staffShortId, storeId, customerId } = props.listData;
    routerPushByRouteName(RoutesName.MemberManagement, { staffShortId, storeId, customerId });
}

/** 点击销售统计 */
const handleToSalesStatistics = () => {
    const { staffShortId, storeId } = props.listData;
    routerPushByRouteName(RoutesName.StoreSalesStatistics, { staffShortId, storeId });
}
</script>
<style scoped lang="less">
.listWarpper{
    width: 100%;
    .contioner{
        padding: 15px;
        background-color: white;
    }
    .title{
        display: flex;
        height: 16px;
        .titleSpan{
            display: flex;
            align-items: center;
        }
        .vertical-line{
            border-left: 4px solid #EF1115;
            border-radius: 60px;
            margin-right: 7px;
        }
        .name{
            font-weight: 600;
            font-size: 16px;
        }
        .span{
            margin-left: 5px;
            font-size: 10px;
            color: white;
            padding: 4px 7px;
            border-radius: 5px;
        }
        .blueStyle{
            background-color: #1677FF;
        }
        .yellowBg{
            background-color: #FFBC47;
        }
    }
    .fixedStyle{
        background: linear-gradient(to bottom,#FFE3DF 0%, white 50%, white 100%);
        border: 1px solid #FFE7E7;
        border-radius: 10px;
    }
    .listCard{
        background-color: white;
    }
    .content{
        margin-top: 10px;
        font-size: 15px;
        .list{
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            .item{
                width: 22%;
                padding: 8px 2px;
                div{
                    margin: 5px 0px;
                }
                .num{
                    margin-top: 13px;
                    font-weight: 600;
                }
            }
        }
    }
    .footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
        .btn {
            width: 96px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            background: #F8F8F8;
            border-radius: 8px 8px 8px 8px;
            color: #EF1115;
            margin-right: 12px;
        }
    }
}
</style>
