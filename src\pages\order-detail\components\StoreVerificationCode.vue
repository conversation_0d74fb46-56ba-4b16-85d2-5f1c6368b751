<template>
  <view class="store_verification_code">
    <!-- header -->
    <view class="store_verification_code_header">
      <!-- 核销码状态 -->
      <view class="store_verification_code_status">待使用</view>
      <view class="store_verification_code_info">
        <text class="title">
          {{ `核销码：${orderInfoRef?.orderVerificationDTO?.code ?? '-'}` }}
        </text>
        <text
          class="btn"
          @click="copyVerificationCode(orderInfoRef?.orderVerificationDTO?.code ?? '')"
        >
          复制
        </text>
      </view>
    </view>
    <view class="qrCode-container">
      <!-- 提示 -->
      <view v-if="isStoreVerification" class="tip">
        该订单需商品到店后才可提货，请耐心等待，并留意通知！
      </view>
      <!-- <VanImage width="120" height="120" fit="contain" :src="qrCodeRef" /> -->
      <canvas class="code-item" canvas-id="qrcode" style="height: 240rpx; width: 240rpx"></canvas>
      <view class="title-wrapper">
        <text class="title">核销码</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch, computed } from 'vue'
import wxcode from 'uniapp-qrcode'
import { copyText } from '@/utils/clipboardUtils'
import { useMessages } from '@/hooks/common'
import {
  StoreOrderDetailVerifyStatusEnum,
  StoreOrderDetailRouteTypeEnum,
  StoreScanTypeEnum,
  OrderVerificationTypeEnum,
} from '@/enum'
import { getMyOrderVerifyCode } from '@/services/api'

defineOptions({ name: 'StoreVerificationCode' })

interface OrderVerificationDTO {
  id: string
  code: string // 核销码
  status: StoreOrderDetailVerifyStatusEnum // 核销状态
  verificationTime?: string // 核销时间
  storeId?: string // 店铺id
}

/** props */
const props = defineProps<{
  orderInfo: {
    code?: string
    // 订单核销记录
    orderVerificationDTO?: OrderVerificationDTO
    verificationType?: OrderVerificationTypeEnum // 核销类型
  }
}>()

const { orderInfo: orderInfoRef } = toRefs(props)

const { createMessageError, createMessageSuccess } = useMessages()
const qrCodeRef = ref('')

/** 商品是否下单门店到货后核销 */
const isStoreVerification = computed(() => {
  return orderInfoRef.value?.verificationType == OrderVerificationTypeEnum.STORE_VERIFICATION
})

/** 复制核销码 */
function copyVerificationCode(id: string) {
  try {
    copyText(id)
    createMessageSuccess('复制核销码成功')
  } catch (e) {
    createMessageError('复制核销码失败')
  }
}

/** 查询我的订单核销码 */
async function queryVerificationCode() {
  try {
    const resp = await getMyOrderVerifyCode({ orderCode: orderInfoRef.value?.code })
    if (resp) {
      // let prefixUrl = window.location.origin
      // const qrcodeLink = `${prefixUrl}/verify?orderCode=${orderInfoRef.value?.code}&routeType=${StoreOrderDetailRouteTypeEnum.SCAN}&scanType=${StoreScanTypeEnum.ORDER}`
      qrCodeRef.value = await generateQRCode('qwe')
    }
  } catch (e) {
    console.log('查询核销码失败：', e)
  }
}

/** 生成二维码 */
async function generateQRCode(url: string): Promise<string | null> {
  try {
    wxcode.qrcode('qrcode', '78222', 120, 120)
  } catch (err) {
    createMessageError('生成二维码失败：' + err)
    return null
  }
}

/** 组件挂载 */
watch(
  () => orderInfoRef.value,
  (newVal) => {
    if (newVal) {
      queryVerificationCode()
    }
  },
  { deep: true, immediate: true },
)
</script>

<style lang="scss" scoped>
.store_verification_code {
  position: relative;
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 28rpx;
  margin-bottom: 16rpx;
  /* 两侧半圆形缺口效果 */
  -webkit-mask: radial-gradient(circle at 0 76rpx, #0000 16rpx, red 0),
    radial-gradient(circle at right 76rpx, #0000 16rpx, red 0);
  -webkit-mask-size: 51%;
  -webkit-mask-position: 0, 100%;
  -webkit-mask-repeat: no-repeat;

  /* 横穿的虚线 */
  &::after {
    content: '';
    position: absolute;
    top: 76rpx;
    left: 16rpx; /* 避开左侧半圆 */
    right: 16rpx; /* 避开右侧半圆 */
    height: 2rpx;
    background: repeating-linear-gradient(
      to right,
      #ccc 0,
      #ccc 8rpx,
      transparent 8rpx,
      transparent 16rpx
    );
  }
  .store_verification_code_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .store_verification_code_status {
      height: 40rpx;
      background: #1677ff;
      border-radius: 8rpx;
      padding: 4rpx 20rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 32rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .store_verification_code_info {
      display: flex;
      align-items: center;
      gap: 20rpx;
      .title {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
      .btn {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 24rpx;
        color: #1677ff;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .qrCode-container {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 16rpx;
    box-sizing: border-box;
    .tip {
      padding: 8rpx 16rpx;
      background: #fff0ef;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      color: #ff6864;
      line-height: 44rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .title-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      .title {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 44rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
