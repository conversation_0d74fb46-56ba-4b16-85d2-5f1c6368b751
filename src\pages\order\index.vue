<template>
  <JCrossPlatformTabs
    :tabs="orderStatusList"
    v-model:active="activeTabRef"
    @change="onTabsChange"
    :isLoading="isPageLoadingRef"
  >
    <template #tab="{ tab, active }">
      <!-- #ifdef MP-WEIXIN -->
      <scroll-view
        scroll-y
        enhanced
        :show-scrollbar="false"
        refresher-enabled
        :refresher-triggered="refreshingRef"
        @scrolltolower="onLoadData"
        @refresherrefresh="onRefresh"
        class="tab-content"
      >
        <template v-if="storeMineOrderList.length">
          <template v-if="[OrderStatusEnum.REFUND].includes(active)">
            <StoreAfterSalesOrderCard
              v-for="item in storeMineOrderList"
              :key="item.id"
              :orderInfo="item"
              @click=""
            />
          </template>
          <template v-else>
            <StoreMineOrderCard
              v-for="item in storeMineOrderList"
              :key="item.id"
              :orderInfo="item"
              @writeOffCode=""
              @cancelOrder=""
              @click="handleToOrderDetail"
              @confirmReceipt=""
              @viewLogistics=""
            />
          </template>
          <view class="no-data">没有更多数据了</view>
        </template>
        <!-- 数据为空 -->
        <template v-else>
          <JEmptyData style="min-height: 400px" />
        </template>
      </scroll-view>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <JPullRefresh
        v-model="refreshingRef"
        @refresh="onRefresh"
        class="j-tab-content"
        :class="`j-tab-content_${tab.value}`"
        @scroll=""
      >
        <template v-if="storeMineOrderList.length">
          <JList
            v-model:loading="isLoadingRef"
            :finished="isFinishedRef"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <template v-if="[OrderStatusEnum.REFUND].includes(activeTabRef)">
              <StoreAfterSalesOrderCard
                v-for="item in storeMineOrderList"
                :key="item.id"
                :orderInfo="item"
                @click=""
              />
            </template>
            <template v-else>
              <StoreMineOrderCard
                v-for="item in storeMineOrderList"
                :key="item.id"
                :orderInfo="item"
                @writeOffCode=""
                @cancelOrder=""
                @click="handleToOrderDetail"
                @confirmReceipt=""
                @viewLogistics=""
              />
            </template>
          </JList>
        </template>
        <template v-else>
          <JEmptyData />
        </template>
      </JPullRefresh>
      <!-- #endif -->
    </template>
  </JCrossPlatformTabs>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad, onPullDownRefresh } from '@dcloudio/uni-app'
import { OrderStatusEnum, StoreOrderDetailRouteTypeEnum } from '@/enum'
import { useGetMineOrder } from './hooks'
import { useRouter } from '@/hooks/common'
/** 相关组件 */
import JEmptyData from '@/components/JEmptyData/index.vue'
import StoreMineOrderCard from './components/StoreMineOrderCard.vue'
import StoreAfterSalesOrderCard from './components/StoreAfterSalesOrderCard.vue'
import JCrossPlatformTabs from '@/components/JCrossPlatformTabs/index.vue'

defineOptions({ name: 'Order' })

const { routerPushByKey } = useRouter()
const {
  orderStatusList,
  activeTabRef,
  storeMineOrderList,
  initStoreMineOrderList,
  initStoreMineOrderRefundList,
  onLoadData,
  onTabsChange,
  onRefresh,
  isFinishedRef,
  refreshingRef,
  isPageLoadingRef,
  isLoadingRef,
} = useGetMineOrder({ orderType: OrderStatusEnum.ALL })

/** 跳转订单详情 */
function handleToOrderDetail(code: string) {
  routerPushByKey('StoreDetail', {
    orderCode: code,
    routeType: StoreOrderDetailRouteTypeEnum.MY_ORDER,
  })
}

onLoad((options) => {
  const v = Number(options?.status)
  if (!Number.isNaN(v)) {
    activeTabRef.value = v as OrderStatusEnum
  }
})
</script>

<style lang="scss" scoped>
/*  #ifdef MP-WEIXIN */
:deep(.van-tab) {
  height: 80rpx;
  flex: auto;
}
:deep(.van-tabs__line) {
  bottom: 8rpx;
}
/* #endif  */
/*  #ifdef MP-WEIXIN */
.tab-content {
  height: calc(100vh - 80rpx);
  background: #f8f8f8;
  padding: 0rpx 24rpx 24rpx 24rpx;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  box-sizing: border-box;
  .no-data {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 24rpx 0;
    margin-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  }
}
/*  #endif  */
/*  #ifdef H5 */
.j-tab-content {
  height: 100%;
  background: #f8f8f8;
  padding: 24rpx 20rpx;
  box-sizing: border-box;
  overflow-y: auto;
}
/*  #endif  */
</style>
