import { defHttp } from "@/services";
import { getStoreApiUrl  } from "@/utils/http/urlUtils";
const enum StorStatisticsApi{
    couponStatDataPage = "/applet/store/personal/couponStatDataPage",//福利券统计
    couponDetailStatDataPage = "/applet/store/personal/couponDetailStatDataPage",//福利券统计详情
    queryWriteOffStatistics = "/applet/store/personal/queryWriteOffStatistics",//核销统计
    productSellStatDataPage = "/applet/store/personal/productSellStatDataPage",//销售统计
    staffSurveyStatData = "/applet/store/personal/staffSurveyStatData",//店员概览数据统计
    staffStatDataPage= "/applet/store/personal/staffStatDataPage",//店员数据统计分页

}
interface GetStatisticsPageParams{
    data:{
        staffId?:string,
        csId?:string,
    },
    pageVO:{
        current:number,
        size:number,
    }
}
export function getCouponStatDataPage(params:GetStatisticsPageParams){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.couponStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getCouponDetailStatDataPage(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.couponDetailStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getqueryWriteOffStatistics(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.queryWriteOffStatistics),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getproductSellStatDataPage(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.productSellStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getstaffSurveyStatData(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.staffSurveyStatData),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getstaffStatDataPage(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.staffStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}