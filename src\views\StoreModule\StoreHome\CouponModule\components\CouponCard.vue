<template>
    <div class="coupon-card-wrapper">
        <!-- 内容 -->
        <div class="coupon-card-content">
            <div class="left">
                <StoreTitle :title="couponInfoRef?.categoryName ?? `福利券`" style="margin-bottom: 8px;" />
                <p class="brief-introduction">{{ couponInfoRef?.exchangeNote ?? `-`}}</p>
            </div>
            <div class="right">
                <span class="coupon-count">{{ `${couponCount ?? 0}张` }}</span>
            </div>
        </div>
        <!-- footer -->
        <div class="coupon-card-footer">
            <!-- 兑换说明 -->
            <div class="coupon-card-btn" @click.stop="handleClick(OperationTypeEnum.EXCHANGE)">
                <img :src="exchangeIcon" alt="">
                <span class="btn-name van-ellipsis">兑换说明</span>
            </div>
            <div class="moulding"></div>
            <!-- 券明细 -->
            <div class="coupon-card-btn" @click.stop="handleClick(OperationTypeEnum.VOUCHER_DETAILS)">
                <img :src="voucherDetailsIcon" alt="">
                <span class="btn-name van-ellipsis">券明细</span>
            </div>
            <div v-if="props.type == StoreCouponRouteTypeEnum.SCAN_COUPON && useStatusRef == CouponStatusEnum.NOT_USED" class="moulding"></div>
            <!-- 核销 -->
            <div v-if="props.type == StoreCouponRouteTypeEnum.SCAN_COUPON && useStatusRef == CouponStatusEnum.NOT_USED" class="coupon-card-btn" @click.stop="handleClick(OperationTypeEnum.VERIFICATION)">
                <img :src="verificationIcon" alt="">
                <span class="btn-name van-ellipsis">核销</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { toRefs, computed } from "vue";
import { OperationTypeEnum, CouponStatusEnum, StoreCouponRouteTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import CouponCard from "@/assets/storeImage/storeHome/coupon-card.png";
import exchangeIcon from "@/assets/storeImage/storeHome/exchange.png";
import verificationIcon from "@/assets/storeImage/storeHome/verification.png";
import voucherDetailsIcon from "@/assets/storeImage/storeHome/voucher-details.png";
/** 相关组件 */
import StoreTitle from "./StoreTitle.vue";

defineOptions({ name: 'CouponCard' });

/** props */
const props = defineProps<{
    type: StoreCouponRouteTypeEnum;
    useStatus?: CouponStatusEnum;
    couponInfo: {
        categoryName?: string;
        description?: string;
        useStatus?: CouponStatusEnum;
        unUseNum?: number;
        useNum?: number;
        invalidNum?: number;
        exchangeNote?: string;
    };
}>();

/** emit */
const emit = defineEmits<{
    (e: 'onChange', type: OperationTypeEnum): void;
}>();

const { couponInfo: couponInfoRef, useStatus: useStatusRef } = toRefs(props);

/** 福利券数量 */
const couponCount = computed(() => {
    const { useNum, unUseNum, invalidNum, useStatus } = couponInfoRef.value;
    const couponCountMap = {
        [CouponStatusEnum.NOT_USED]: unUseNum,
        [CouponStatusEnum.USED]: useNum,
        [CouponStatusEnum.EXPIRED]: invalidNum,
    };
    return couponCountMap[props.useStatus];
})

function handleClick(type: OperationTypeEnum) {
    emit('onChange', type);
}
</script>


<style lang="less" scoped>
.coupon-card-wrapper {
    width: 100%;
    background-color: #fff;
    margin-bottom: 8px;
    border-radius: 8px;
    .coupon-card-content {
        width: 100%;
        height: 91px;
        background: url(@/assets/storeImage/storeHome/coupon-card.png) no-repeat;
        background-size: 100% 100%;
        padding: 12px 16px 12px 16px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .brief-introduction {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
        .right {
            width: 80px;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            .coupon-count {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 24px;
                color: #EF1115;
                letter-spacing: 2px;
                text-align: right;
                font-style: normal;
                text-transform: none;
            }
        }
    }
    .coupon-card-footer {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 0px 6px 6px 6px;
        box-sizing: border-box;
        .coupon-card-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            padding: 6px 0;
            img {
                width: 18px;
                height: 18px;
            }
            .btn-name {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                text-align: center;
                font-style: normal;
                text-transform: none;
                margin-left: 2px;
            }

            &:hover {
                background: #F5F5F5;
                border-radius: 6px;
            }
        }

        .moulding {
            width: 0px;
            height: 16px;
            border: 1px solid #EEEEEE;
            margin: 0 5px;
        }
    }
}
</style>