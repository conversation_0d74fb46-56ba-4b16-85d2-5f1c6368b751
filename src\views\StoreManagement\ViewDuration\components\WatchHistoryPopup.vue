<template>
  <van-popup
    :show="props.show"
    position="bottom"
    round
    closeable
    close-icon="close"
    @click-close-icon="handleClose"
    @click-overlay="handleClose"
    style="height: auto"
    safe-area-inset-bottom
  >
    <div class="search-popup-content">
      <div style="text-align:center;font-weight: 600;">观看记录</div>
      <div class="content">
        <div class="memberStyle">
          <span>{{props.memberName}}</span>&nbsp;
          <span v-if="props.memberId">ID:{{ props.memberId || '-' }}</span>
        </div>
        <div class="listWarpper">
          <van-list
            :offset="15"
            v-if="listData.length"
            :loading="listStatus.isNextPageLoading"
            @load="handleListNextPageLoad"
            :finished="listStatus.isNextPageFinished"
          >
          <div class="timeStyle" v-for="time in listData">{{ time.watchStart }}-{{ time.watchEnd }}</div>
        </van-list>
        </div>
       
      </div>
    </div>
  </van-popup>
</template>
<script setup lang="ts">
import { ref,reactive,onMounted } from "vue";
import { getViewDurationDetial } from "@/services/storeApi";
import { useMessages } from "@/hooks/useMessage";
const { createMessageError } = useMessages();
const props = withDefaults(defineProps<{ show: boolean,params:any,memberId:string,memberName:string }>(), {
  show: false,
  params:{},
  memberId:'',
  memberName:''
});
const emits = defineEmits<{
  (e: "update:show", val: boolean): void;
  (e: "close"): void;
  (e: "update:value", val: any): void;
}>();
const listData = ref([]);
const listStatus = reactive({
  isPullLoading: false,
  isNextPageLoading: false,
  isNextPageFinished: false,
});
const viewDurationPageVO = {
  current: 1,
  size: 50,
  total: 1,
};
const handleClose = () => {
  emits("update:show", false);
};
const isPageLoadingRef = ref(false)
const handleListNextPageLoad = () => {
  if (Number(viewDurationPageVO.current) * Number(viewDurationPageVO.size) < Number(viewDurationPageVO.total)) {
    viewDurationPageVO.current++;
    listStatus.isNextPageLoading = true;
    loadData();
  }
};
onMounted(async () => {
  await loadData();
});
const loadData = async (isClear = false) => {
  isPageLoadingRef.value = true;
  listStatus.isNextPageFinished = false;
  let param = {
    data: {
      csId:props.params.csId,
      dateType:props.params.dateType
    },
    pageVO: {
      current: viewDurationPageVO.current,
      size: viewDurationPageVO.size,
    },
  };
  try {
    const { current, size, total, records } = await getViewDurationDetial(param);
    isClear && (listData.value = []);
    viewDurationPageVO.current = Number(current);
    viewDurationPageVO.size = Number(size);
    viewDurationPageVO.total = Number(total);
    listData.value.push(...records);
    if (Number(viewDurationPageVO.current) * Number(viewDurationPageVO.size) >= Number(viewDurationPageVO.total)) {
      listStatus.isNextPageFinished = true;
    }
  } catch (error) {
    createMessageError("获取观看时长明细异常");
  } finally {
    // listStatus.isNextPageFinished = true;
    listStatus.isNextPageLoading = false;
    listStatus.isPullLoading = false;
    isPageLoadingRef.value = false;
  }
};
</script>
<style scoped lang="less">
.search-popup-content {
  height: 300px;
  padding:20px 5px 0px 5px;
  .content{
    padding: 20px;
    height: calc(100% - 22px);
   
    .listWarpper{
      height: calc(100% - 34px);
      overflow-y: scroll;
    }
    .memberStyle{
      padding-bottom: 10px;
      font-size: 14px;
    }
    .timeStyle{
      padding: 10px 0px;
      font-weight: 600;
    }
  }

}
</style>
  