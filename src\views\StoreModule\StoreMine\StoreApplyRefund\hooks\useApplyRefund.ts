import { ref } from "vue";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import { isArray } from "@/utils/isUtils";
import { uploadBase64ImgApi } from "@/services/storeApi";
import { StoreAfterSaleTypeEnum, AfterSaleReasonEnum } from "@/views/StoreModule/enums";

export default function useApplyRefund() {
  const { createMessageError, createMessageSuccess } = useMessages();

  /** 售后订单提交表单 */
  const model = ref({
    type: StoreAfterSaleTypeEnum.REFUND,
    orderCode: "",
    reason: null,
    reasonDescription: "",
    recordNo: "",
    causeName: null,
    phone: "",
    refundAmount: 0,
    afterSaleImgVOList: [],
  });
  const tempAfterSaleImgVOList = ref([]);

  const showRefundReason = ref(false);

  /** 退款原因 */
  const refundReasonList = [
    {
      label: "其他",
      value: AfterSaleReasonEnum.OTHER,
    },
    {
      label: "拍错/多拍",
      value: AfterSaleReasonEnum.WRONG_OR_EXTRA_ORDER,
    },
    {
      label: "不想要了",
      value: AfterSaleReasonEnum.NO_LONGER_WANTED,
    },
    {
      label: "无快递信息",
      value: AfterSaleReasonEnum.NO_EXPRESS_INFO,
    },
    {
      label: "包裹为空",
      value: AfterSaleReasonEnum.EMPTY_PACKAGE,
    },
    {
      label: "已拒签包裹",
      value: AfterSaleReasonEnum.REJECTED_PACKAGE,
    },
    {
      label: "快递长时间未送达",
      value: AfterSaleReasonEnum.DELIVERY_DELAYED,
    },
    {
      label: "与商品描述不符",
      value: AfterSaleReasonEnum.DESCRIPTION_MISMATCH,
    },
    {
      label: "质量问题",
      value: AfterSaleReasonEnum.QUALITY_ISSUE,
    },
    {
      label: "卖家发错货",
      value: AfterSaleReasonEnum.WRONG_ITEM_SENT,
    },
    {
      label: "三无产品",
      value: AfterSaleReasonEnum.NO_CERTIFICATION,
    },
    {
      label: "假冒产品",
      value: AfterSaleReasonEnum.FAKE_PRODUCT,
    },
  ];

  /** 文件读取完成后的回调函数 */
  async function handleAfterRead(file) {
    try {
      if (isArray(file)) {
        // 如果是数组，遍历每个文件并依次上传
        for (const singleFile of file) {
          await handleSingleFile(singleFile);
        }
      } else {
        // 如果不是数组，直接处理单个文件
        await handleSingleFile(file);
      }
    } catch (error) {
      createMessageError("上传图片失败：" + error);
    }
  }

  /** 上传单个文件 */
  async function handleSingleFile(file) {
    try {
      file.status = "uploading";
      file.message = "上传中...";
      if (file.content) {
        const base64Data = file.content;
        // 去掉可能的数据URL前缀
        const pureBase64 = base64Data.replace(/^data:image\/\w+;base64,/, "");
        let _params = {
          fileName: removeExtension(file?.file?.name ?? "未知文件"),
          base64: pureBase64,
        };
        const imgFilePath = await uploadBase64ImgApi(_params);
        if (imgFilePath) {
          model.value.afterSaleImgVOList.push({
            img: removeExtension(file?.file?.name ?? "未知文件"),
            path: imgFilePath,
          });
          file.status = "done";
        }
      }
    } catch (error) {
      file.status = "failed";
      file.message = "上传失败";
    }
  }

  /** 删除图片 */
  function handleDeleteUploader(field, { index }) {
    // 实际存储的文件数据中删除
    model.value.afterSaleImgVOList.splice(index, 1);
  }

  function removeExtension(filename) {
    return filename.replace(/\.(png|jpg|jpeg|gif|webp)$/i, '');
  }

  return {
    model,
    showRefundReason,
    refundReasonList,
    tempAfterSaleImgVOList,
    handleAfterRead,
    handleDeleteUploader
  };
}
