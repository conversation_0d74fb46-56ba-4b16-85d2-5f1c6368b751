import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import "dayjs/locale/zh-cn";

dayjs.extend(quarterOfYear)
dayjs.extend(weekday);
dayjs.locale('zh-cn')
type DateType = "day" | "week" | "month" | "year";
type DateValue = {
  timestamp: [number, number];
  formatValue: [string, string];
};


export const enum TimeFormat {
  DEFAULT = "YYYY-MM-DD HH:mm:ss",
  DATE = "YYYY-MM-DD",
}

export const dataTimes = {
  today: {
    name: "今天",
    start_time: dayjs().startOf("day").add(0, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  yesterday: {
    name: "昨天",
    start_time: dayjs().startOf("day").add(-1, "day").valueOf(),
    end_time: dayjs().endOf("day").add(-1, "day").valueOf(),
  },
  this_week: {
    name: "本周",
    start_time: dayjs().startOf("week").valueOf(),
    end_time: dayjs().endOf("day").valueOf(),
  },
  last_week: {
    name: "上周",
    // start_time: dayjs().add(-1, "week").startOf("week").add(1, "day").valueOf(),
    // end_time: dayjs().add(-1, "week").endOf("week").add(1, "day").valueOf(),
    start_time: dayjs().add(-1, "week").startOf("week").valueOf(),
    end_time: dayjs().add(-1, "week").endOf("week").valueOf(),
  },
  this_month: {
    name: "本月",
    start_time: dayjs().startOf("month").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  last_month: {
    name: "上月",
    start_time: dayjs().add(-1, "month").startOf("month").valueOf(),
    end_time: dayjs().add(-1, "month").endOf("month").valueOf(),
  },
  this_year: {
    name: "本年",
    start_time: dayjs().startOf("year").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  this_quarter: {
    name: "本季度",
    start_time: dayjs().startOf("quarter").valueOf(),
    end_time: dayjs().endOf("quarter").valueOf(),
  },
  last_quarter:{
    name: "上季度",
    start_time: dayjs().add(-1, 'quarter').startOf('quarter').valueOf(),
    end_time:dayjs().add(-1, 'quarter').endOf('quarter').valueOf()
  },
  last_7_day:{
    name:"近七天",
    start_time: dayjs().add(-7, 'day').valueOf(),
    end_time:dayjs().valueOf()
  },
  last_30_day:{
    name:"近三十天",
    start_time: dayjs().add(-30, 'day').valueOf(),
    end_time:dayjs().valueOf()
  },
  last_90_day:{
    name:"近九十天",
    start_time: dayjs().add(-90, 'day').valueOf(),
    end_time:dayjs().valueOf()
  }
};



export const enum DateTimeNameEnum {
  all='全部',
  today='今天',
  yesterday='昨天',
  ereyesterday='前天',
  last_7_day='近七天',
  last_365_day='近年'
}

export type DateTimeNameKey = keyof typeof DateTimeNameEnum;
export type DateTimeObject = {
  name:DateTimeNameEnum,
  start_time:number|null,
  end_time:number|null
}
export const dateTimeList:Record<DateTimeNameKey,DateTimeObject> = {
  all: {
    name: DateTimeNameEnum.all,
    start_time: null,
    end_time: null,
  },
  today: {
    name:DateTimeNameEnum.today,
    start_time: dayjs().startOf("day").add(0, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  yesterday: {
    name: DateTimeNameEnum.yesterday,
    start_time: dayjs().startOf("day").add(-1, "day").valueOf(),
    end_time: dayjs().endOf("day").add(-1, "day").valueOf(),
  },
  ereyesterday: {
    name: DateTimeNameEnum.ereyesterday,
    start_time: dayjs().startOf("day").add(-2, "day").valueOf(),
    end_time: dayjs().endOf("day").add(-2, "day").valueOf(),
  },
  last_7_day: {
    name: DateTimeNameEnum.last_7_day,
    start_time: dayjs().startOf("day").add(-7, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
  last_365_day: {
    name: DateTimeNameEnum.last_365_day,
    start_time: dayjs().startOf("day").add(-365, "day").valueOf(),
    end_time: dayjs().endOf("day").add(0, "day").valueOf(),
  },
};

export function getTimeRangeByType(
  type: DateType,
  date?: Date | undefined,
  format: TimeFormat = TimeFormat.DEFAULT,
): Array<string> {
  dayjs.extend(weekday);
  dayjs.locale("zh-cn");
  const dateInstance = !date ? dayjs() : dayjs(date);
  const formatTimeRange = [];
  formatTimeRange.push(dateInstance.startOf(type).format(format));
  formatTimeRange.push(dateInstance.endOf(type).format(format));
  return formatTimeRange;
}

export function getDate(
  type: "today" | "yesterday" | "this_week" | "last_week" | "this_month" | "last_month" | "this_year",
  format: TimeFormat = TimeFormat.DEFAULT,
): DateValue {
  return {
    timestamp: [dataTimes[type].start_time, dataTimes[type].end_time],
    formatValue: [
      dayjs(dataTimes[type].start_time).format(format),
      dayjs(dataTimes[type].end_time).format(format),
    ],
  };
}

//基于baseDate获取number天的时间范围（不包括baseDate当天）
export function getDateRangeByNumber(
  number: number,
  format: TimeFormat = TimeFormat.DEFAULT,
  baseDate?: string | number | undefined,
): DateValue {
  if(number == 0) throw new Error("number can not be 0");
  let baseDateInstance = baseDate?dayjs(baseDate):dayjs();
  const targerDateObj = {
    start_time: number<0?baseDateInstance.startOf("day").add(number, "day").valueOf():baseDateInstance.startOf("day").add(2, "day").valueOf(),
    end_time: number<0?baseDateInstance.endOf("day").add(-1, "day").valueOf():baseDateInstance.endOf("day").add(number, "day").valueOf(),
  }
  return {
    timestamp: [targerDateObj.start_time, targerDateObj.end_time],
    formatValue: [
      dayjs(targerDateObj.start_time).format(format),
      dayjs(targerDateObj.end_time).format(format),
    ],
  };
}
export const enum DataTypeValueEnum{
  today=1,
  yesterday=2,
  ereyesterday=3,
  last_7_day=4,
  all=5
}
export type DateTypeObject = {
  name:DateTimeNameEnum,
  value:DataTypeValueEnum,
}
export const dateTypeList:Record<keyof typeof DataTypeValueEnum,DateTypeObject> = {
  all: {
    name: DateTimeNameEnum.all,
    value: DataTypeValueEnum.all,
  },
  today: {
    name:DateTimeNameEnum.today,
    value: DataTypeValueEnum.today,
  },
  yesterday: {
    name: DateTimeNameEnum.yesterday,
    value: DataTypeValueEnum.yesterday,
  },
  ereyesterday: {
    name: DateTimeNameEnum.ereyesterday,
    value: DataTypeValueEnum.ereyesterday,
  },
  last_7_day: {
    name: DateTimeNameEnum.last_7_day,
    value: DataTypeValueEnum.last_7_day,
  },
  
};



export function formatTimeToCommentDisplay(_inputTime) {
  var inputTime = convertToISO8601CST(_inputTime)
  var inputDate = new Date(inputTime);
  var now = new Date();

  // Check for invalid date
  if (isNaN(inputDate.getTime())) {
    return 'Invalid date';
  }

  var diff = now.getTime() - inputDate.getTime();

  if (diff < 60000) {
    return '刚刚';
  }

  if (inputDate.toDateString() === now.toDateString()) {
    return twoDigits(inputDate.getHours()) + ':' + twoDigits(inputDate.getMinutes());
  }

  return inputDate.getFullYear().toString().substr(-2) + '-' +
         twoDigits(inputDate.getMonth() + 1) + '-' +
         twoDigits(inputDate.getDate()) + ' ' +
         twoDigits(inputDate.getHours()) + ':' +
         twoDigits(inputDate.getMinutes());
}




export function twoDigits(value) {
  return (value < 10 ? '0' : '') + value;
}

export function convertToISO8601CST(dateTimeString) {
  // 检查字符串是否已包含毫秒
  var hasMilliseconds = dateTimeString.includes('.');

  // 将空格替换为'T'，以符合ISO 8601标准
  var isoString = dateTimeString.replace(' ', 'T');

  // 如果没有毫秒，则添加'.000'
  if (!hasMilliseconds) {
    isoString += '.000';
  }

  // 添加中国时区信息（UTC+8）
  isoString += '+08:00';

  return isoString;
}

export function formatRelativeDate(formatTimeString: string): string {
  const now = new Date();
  const inputDate = new Date(convertToISO8601CST(formatTimeString));
  if (inputDate.getFullYear() === now.getFullYear() &&
    inputDate.getMonth() === now.getMonth() &&
    inputDate.getDate() === now.getDate()) {
    return "今天";
  }
  const diffTime = Math.abs(now.getTime() - inputDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays <= 7) {
      // 如果时间差不超过一周
      return diffDays === 1 ? "一天前" : `${diffDays}天前`;
  } else {
      // 超过一周显示具体日期
      return `${inputDate.getFullYear()}年${inputDate.getMonth() + 1}月${inputDate.getDate()}日`;
  }
}

export function getAdjustedTime(date: Date) {
  const seconds = date.getSeconds();
  const minutes = date.getMinutes();
  let adjustedDate = new Date(date);

  if (seconds >= 0 && seconds <= 9) {
    adjustedDate.setSeconds(59);
    adjustedDate.setMinutes(minutes === 0 ? 59 : minutes - 1);
  } else if (seconds >= 10 && seconds <= 19) {
    adjustedDate.setSeconds(9);
  } else if (seconds >= 20 && seconds <= 29) {
    adjustedDate.setSeconds(19);
  } else if (seconds >= 30 && seconds <= 39) {
    adjustedDate.setSeconds(29);
  } else if (seconds >= 40 && seconds <= 49) {
    adjustedDate.setSeconds(39);
  } else if (seconds >= 50 && seconds <= 59) {
    adjustedDate.setSeconds(49);
  } 
  // else if ([9, 19, 29, 39, 49, 59].includes(seconds)) {
  //   return new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), seconds, 0).getTime();
  // }

  adjustedDate.setMilliseconds(0);

  return adjustedDate.getTime();
}
