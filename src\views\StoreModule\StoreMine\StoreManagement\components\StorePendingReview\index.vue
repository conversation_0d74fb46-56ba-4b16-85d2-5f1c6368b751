<template>
  <div class="store_pending_review_container">
    <!-- 门店标题 -->
    <BannerContainer v-for="item in storePendingListRef" :key="item?.id" style="margin-bottom: 8px;background: #FFFFFF;border-radius: 12px;">
      <template #title>
        <StoreTitle :title="item?.applyStructureName" />
      </template>
      <!-- 内容 -->
      <div class="content_wrapper">
        <!-- 店长姓名 -->
        <div class="store_pending_review_info_item">
          <span class="store_pending_review_info_item_label">店长姓名</span>
          <span class="store_pending_review_info_item_value">{{ item?.applyCsName ?? '-' }}</span>
        </div>
        <!-- 手机号 -->
        <div class="store_pending_review_info_item">
          <span class="store_pending_review_info_item_label">手机号</span>
          <span class="store_pending_review_info_item_value">{{ item?.applyCsPhone ?? '-' }}</span>
        </div>
        <!-- 提货地址 -->
        <div class="store_pending_review_info_item">
          <span class="store_pending_review_info_item_label">提货地址</span>
          <span class="store_pending_review_info_item_value">
            {{ item?.addressDetail ?? '-' }}
          </span>
        </div>
        <!-- 申请备注 -->
        <div class="store_pending_review_info_item">
          <span class="store_pending_review_info_item_label">申请备注</span>
          <span class="store_pending_review_info_item_value">{{ item?.applyRemark ?? '-' }}</span>
        </div>
        <!-- 申请时间 -->
        <div class="store_pending_review_info_item">
          <span class="store_pending_review_info_item_label">申请时间</span>
          <span class="store_pending_review_info_item_value">{{ item?.applyTime ?? '-' }}</span>
        </div>
      </div>
      <!-- footer -->
      <div class="footer">
        <!-- 审核不通过 -->
        <VanButton type="danger" block plain @click="handleAudit(StoreAuditEnum.REVIEW_NOT_PASSED, item?.id)" style="width: 100%;height: 40px;">审核不通过</VanButton>
        <!-- 审核通过 -->
        <VanButton type="danger" block @click="handleAudit(StoreAuditEnum.REVIEW_PASSED, item?.id)" style="width: 100%;height: 40px;">审核通过</VanButton>
      </div>
    </BannerContainer>
    <!-- 二次确认 -->
    <JDoubleConfirm v-model:show="showDoubleConfirmRef" :tip="doubleConfirmRef.tip" @confirm="handleConfirm" />
  </div>
</template>

<script lang="ts" setup>
import { toRefs, ref } from "vue";
import { showToast } from 'vant';
import { useBoolean } from "@/views/StoreModule/hooks";
import { useMessages } from "@/hooks/useMessage";
import { StoreAuditEnum } from "@/views/StoreModule/enums";
import { auditStoreApi } from "@/services/storeApi";
/** 相关组件 */
import BannerContainer from "@/views/StoreModule/components/BannerContainer.vue";
import StoreTitle from "@/views/StoreModule/components/StoreTitle.vue";
import JDoubleConfirm from "@/views/StoreModule/components/JDoubleConfirm.vue";

defineOptions({ name: 'StorePendingReview' });

/** props */
const props = defineProps<{
  storePendingList: Array<any>;
}>();

/** emits */
const emit = defineEmits<{
  /** 成功 */
  (e: 'success'): void;
}>();

const { storePendingList: storePendingListRef } = toRefs(props);

const { createMessageSuccess, createMessageError } = useMessages();

/** 当前选择申请ID */
const currentIdRef = ref<string | null>(null);

/** 二次确认 start */
const { bool: showDoubleConfirmRef, setTrue: showDoubleConfirm, setFalse: hideDoubleConfirm } = useBoolean(false);
const doubleConfirmRef = ref<{
  type: StoreAuditEnum;
  tip: string;
}>({
  type: StoreAuditEnum.REVIEW_NOT_PASSED,
  tip: '是否不通过该门店审核？',
});
async function handleConfirm() {
  const { type } = doubleConfirmRef.value;
  try {
    let _params = {
      id: currentIdRef.value,
      auditStatus: type == StoreAuditEnum.REVIEW_NOT_PASSED ? StoreAuditEnum.REVIEW_NOT_PASSED : StoreAuditEnum.REVIEW_PASSED,
    };
    await auditStoreApi(_params);
    showToast(`操作成功`);
    emit('success');
  } catch (error) {
    createMessageError(`操作失败：` + error);
  }
}
/** end */

/** 审核 start */
function handleAudit(type: StoreAuditEnum, id: string) {
  doubleConfirmRef.value = {
    type,
    tip: type == StoreAuditEnum.REVIEW_NOT_PASSED ? '是否不通过该门店审核？' : '是否通过该门店审核？',
  };
  showDoubleConfirm();
  currentIdRef.value = id;
}
/** 审核 end */
</script>

<style lang="less" scoped>
.store_pending_review_container {
    .content_wrapper {
        background: #F8F8F8;
        border-radius: 8px;
        padding: 8px 12px;
        box-sizing: border-box;
        margin-top: 12px;
        .store_pending_review_info_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            .store_pending_review_info_item_label {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .store_pending_review_info_item_value {
                flex: 1;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                text-align: right;
                font-style: normal;
                text-transform: none;
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    .footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;
        margin-top: 12px;
        :deep(.van-button__text) {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
    }
}
</style>
