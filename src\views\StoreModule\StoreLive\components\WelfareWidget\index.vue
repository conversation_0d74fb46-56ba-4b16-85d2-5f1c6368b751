<template>
  <div v-if="isShowWelfareIcons">
    <WelfareCountDownIcon
      v-for="(item,index) in threeLiveCouponData"
      :key="item.couponBatchId"
      :show="true"
      :live-coupon-data="item"
      :count-down-time="item.requiredSeconds"
      :type="item.requiredSeconds ? 'countDown' : 'normal'"
      :style="{top:80+'px',right: (5 + 50 * index) + 'px' }"
      @click="()=>handleBtnClick(item)"
      @countDownEnd="()=>handleCouponCountDownEnd(item)"
    />
  </div>
  <WelfarePopup ref="welfarePopupRef" />
</template>

<script setup lang="ts">
import { ref, onUnmounted, onMounted, computed } from "vue";
import WelfareCountDownIcon from "@/views/StoreModule/StoreLive/components/WelfareCountDownIcon/index.vue";
import { useUserStore } from "@/stores/modules/user";
import { isNullOrUnDef } from "@/utils/isUtils";
import {
  getLiveWatchTimeCouponList,
  type ListWatchTimeCouponResponse,
  type LiveCouponInfoResponse,
  receiveCoupon,
  type receiveCouponVo,
} from "@/services/storeApi";
import { useMessages } from "@/hooks/useMessage";
import WelfarePopup from "@/views/StoreModule/StoreLive/components/WelfarePopup/index.vue";
import { _debounce } from "@/utils/storeUtils";

type Props = {
  liveInfo: {
    /* 直播状态 */
    status: number,
    /* 直播链接 */
    hsLiveLink: string,
    /* 直播间id */
    liveRoomId: number | string,
  }
}

/* 直播状态 */
enum LiveStatus {
  /* 直播中 */
  LIVE = "直播中",
  /* 预告 */
  TRAILER = "预告",
  /* 回放 */
  PLAYBACK = "回放",
  /* 直播结束 */
  ENDED = "结束",
}

const props = withDefaults(defineProps<Props>(), {
  liveInfo: () => {
    return {
      status: 0,
      hsLiveLink: "",
      liveRoomId: "",
    };
  },
});

const message = useMessages();

const userStore = useUserStore();
const welfarePopupRef = ref<InstanceType<typeof WelfarePopup> | null>(null);
/* 定时器，每5秒保存一次看直播时长 */
let saveInterval = null;
/* 直播状态 */
let liveStatus = ref("");
/* 保存观看时长间隔时间 */
const INTERVAL_TIME = 5;
/* 时长券列表数据 */
const liveCouponData = ref<LiveCouponInfoResponse[]>([]);

/* 最多只能显示三张 */
const threeLiveCouponData = computed(() => {
  if (liveCouponData.value.length <= 3) {
    return liveCouponData.value;
  }
  let _tempArr = [];
  if (liveCouponData.value?.length > 3) {
    liveCouponData.value.forEach(item => {
      if (item.receiveFlag != 1 && _tempArr.length < 3) {
        _tempArr.push(item);
      }
    });
  }
  return _tempArr;
});

onMounted(() => {
  /* 添加监听 */
  window.addEventListener("message", handleMessage);
  const _userStoreInfoCache = userStore.storeUserInfo;
  if (!isNullOrUnDef(_userStoreInfoCache.liveRoomId)) {
    if (_userStoreInfoCache.liveRoomId !== props.liveInfo.liveRoomId) {
      // 清空之前其他直播间观看时长
      _userStoreInfoCache.liveRoomId = props.liveInfo.liveRoomId;
      _userStoreInfoCache.liveWatchTime = 0;
    }
  }
  userStore.setStoreUserInfo(_userStoreInfoCache);
});

/* 组件卸载时取消监听 */
onUnmounted(() => {
  removeLiveListener();
});

const isShowWelfareIcons = computed(() => {
  return props.liveInfo.hsLiveLink && liveStatus.value === LiveStatus.LIVE;
});

/**
 * 按钮点击事件
 * @param item
 */
function handleBtnClick(item: LiveCouponInfoResponse) {
  welfarePopupRef.value.acceptParams({
    show: true,
    couponData: item,
  });
}

/**
 * 时长券倒计时结束
 * @param liveCouponItem 倒计时结束的时长券
 */
async function handleCouponCountDownEnd(liveCouponItem: LiveCouponInfoResponse) {
  await handleReceiveCoupon(
    {
      couponBatchId: liveCouponItem.couponBatchId,
      categoryId: liveCouponItem.categoryId,
      liveRoomId: props.liveInfo.liveRoomId,
    });
  const index = liveCouponData.value.findIndex(item => item.couponBatchId === liveCouponItem.couponBatchId);
  if (index !== -1) {
    // 创建一个新对象，保留原对象的属性并修改需要更新的属性
    const updatedItem = {
      ...liveCouponData.value[index],
      // 假设修改领取标志为已领取
      receiveFlag: 1,
      // 可以添加其他需要修改的属性
    };
    // 使用 Vue 的响应式特性更新数组
    liveCouponData.value.splice(index, 1, updatedItem);
    setTimeout(() => {
      handleBtnClick(updatedItem);
    }, 2000);
  }
}

async function handleReceiveCoupon(couponData: receiveCouponVo) {
  try {
    await receiveCoupon(couponData);
  } catch (err) {
    message.createMessageError(`'领取福利卷失败,请重试':${err}`);
    new Error("领取福利卷失败");
  }
}

async function changeLiveCouponData() {
  const _userStoreInfoCache = userStore.storeUserInfo;
  if (!isNullOrUnDef(_userStoreInfoCache.liveWatchTime)
    && !isNullOrUnDef(_userStoreInfoCache.liveRoomId)) {
    let _liveWatchTime = _userStoreInfoCache.liveWatchTime;

    for (let item of liveCouponData.value) {
      // 缓存中观看时长秒数已超过要求的观看时长
      if (_liveWatchTime >= item.requiredSeconds) {
        if (item.receiveFlag == 0) {
          await handleReceiveCoupon(
            {
              couponBatchId: item.couponBatchId,
              categoryId: item.categoryId,
              liveRoomId: props.liveInfo.liveRoomId,
            });
        }
        item.receiveFlag = 1;
      }
    }
  }
}

/* 获取当前直播间时长券列表 */
async function getCurrentLiveCouponList() {
  try {
    /* 直播间id */
    let activityId = Number(props.liveInfo.liveRoomId);
    let result: ListWatchTimeCouponResponse = await getLiveWatchTimeCouponList(activityId);
    if (!result || result?.watchTimeCouponDTOList.length === 0) {
      return;
    }
    let watchTimeCouponDTOList = result.watchTimeCouponDTOList;
    liveCouponData.value = watchTimeCouponDTOList;
    changeLiveCouponData();
    Already_play = true;
  } catch (err) {
    console.log(`获取时长券数据失败:${err}`);
    throw new Error("获取时长券数据失败");
  }
}

// 已经开始观看
let Already_play = false;
/* 直播开始事件 */
let playerPlayEvent = async (data) => {
  if (Already_play) {
    return;
  }
  /* 获取当前直播间时长券列表 */
  await getCurrentLiveCouponList();
  if (!saveInterval) {
    saveInterval = setInterval(() => {
      saveLiveWatchTime();
    }, INTERVAL_TIME * 1000);
  }
};

const handlePlayerPlay = _debounce(playerPlayEvent, 500);

/* 缓存直播间观看时长 */
function saveLiveWatchTime() {
  if (isNullOrUnDef(props.liveInfo.liveRoomId)) {
    return;
  }
  const _userStoreInfoCache = userStore.storeUserInfo;
  if (isNullOrUnDef(_userStoreInfoCache.liveWatchTime)
    || isNullOrUnDef(_userStoreInfoCache.liveRoomId)) {
    _userStoreInfoCache.liveRoomId = props.liveInfo.liveRoomId;
    _userStoreInfoCache.liveWatchTime = INTERVAL_TIME;
  } else {
    let _liveWatchTime = _userStoreInfoCache.liveWatchTime;
    // 如果当前直播间id跟缓存不同
    if (_userStoreInfoCache.liveRoomId == props.liveInfo.liveRoomId) {
      /* 修改缓存中的当前直播间观看时长 */
      _liveWatchTime += INTERVAL_TIME;
    } else {
      _userStoreInfoCache.liveRoomId = props.liveInfo.liveRoomId;
      _liveWatchTime = INTERVAL_TIME;
    }
    _userStoreInfoCache.liveWatchTime = _liveWatchTime;
  }
  userStore.setStoreUserInfo(_userStoreInfoCache);
}

/* 直播事件处理函数 */
const handleMessage = (event: MessageEvent) => {
  const rawData = event?.data;
  let data = null;
  try {
    // 优先使用原始数据，为空时兜底空JSON字符串
    const parseTarget = typeof rawData === 'string' ? rawData : '{}';
    data = JSON.parse(parseTarget);
  } catch (e) {
    return; // 解析失败时终止后续逻辑
  }
  if (!data || !data?.action){
    return
  }
  try {
    switch (data.action) {
      case "player.play":
        /* 这里可能会监听到多次 */
        handlePlayerPlay(data);
        break;
      case "player.destroy":
        removeLiveListener();
        break;
      case "player.status":
        liveStatus.value = data.payload;
        break;
      default:
        return;
    }
  } catch (e) {
  }
};

function removeLiveListener() {
  window.removeEventListener("message", handleMessage);
  if (saveInterval) {
    clearInterval(saveInterval);
    saveInterval = null;
  }
}
</script>

<style scoped lang="less"></style>
