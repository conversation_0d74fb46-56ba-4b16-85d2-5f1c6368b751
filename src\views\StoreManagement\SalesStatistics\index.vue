<template>
  <div class="verWarpper">
    <JLoadingWrapper :show="isPageLoadingRef">
      <div>
        <div class="tip">按支付时间统计购物类订单数据，已排除发起退款的订单</div>
        <div class="dataSelect">
          <div class="lableSelect">
            <div style="display: flex;">
              <div
                style="margin-right: 5px;"
                v-for="(date, key) in tagOptionsList"
                @click="handlerSelectDate(date)"
                :key="key"
              >
                <div
                  :class="['dateTag', searchDateName == date.label || (!searchDateName && date.value == 'today') ? 'active' : null]"
                >
                  <span>{{ date.label }}</span>
                </div>
              </div>
            </div>
            <div class="screen" @click="changeSelect">
              <span>筛选</span>
              <SvgIcon
                name="dropDown"
                style="font-size: 18px;"
                :class="{'rotate-180': showSearchTag, 'icon': true }"
              />
            </div>
          </div>
          <div class="selectTime" @click="timeSelect">
            <span style="margin-left:10px">{{ _tempValReactive.startTime }} &nbsp;~</span>
            <span style="margin-left:10px">{{ _tempValReactive.endTime }}&nbsp;</span>
            <van-icon name="arrow-down" />
          </div>
        </div>
      </div>
      <div class="content">
        <ListTable :listDatas="listData" :searchParam="searchParams" v-if="listData.length" />
        <EmptyData v-else />
      </div>
      <van-calendar
        teleport="body"
        v-model:show="calendarSelectShowRef"
        type="range"
        color="#EF1115"
        @confirm="onDateConfirm"
        :max-range="31"
        :min-date="new Date(calendarMinDate)"
        allow-same-day
        :default-date="[new Date(_tempValReactive.startTime),new Date(_tempValReactive.endTime)]"
      />
    </JLoadingWrapper>
  </div>
  <TagSearchPopup v-model:show="showSearchTag" @update:search="updateSearch" />
</template>
<script setup lang="ts">
import { ref,reactive,computed,onMounted } from "vue";
import {dataTimes } from '@/utils/dateUtils';
import {SalesStatisticsList} from './hooks/index'
import dayjs from "dayjs";
import { useRoute } from "vue-router";
/** 相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import ListTable from '@/views/StoreManagement/VerificationStatistics/components/ListTable/index.vue';
import TagSearchPopup from "@/views/StoreManagement/WelfareVoucherStatistics/components/TabSearchPopup/index.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

/** props */
const props = defineProps<{
    storeId?: string; // 店铺id
}>();

const today = dayjs().format('YYYY-MM-DD');
const _tempValReactive = reactive({
    value:'',
    startTime:today,
    endTime:today,
    isCustomeTime:false,
})
const route = useRoute();

const searchParams = reactive({
    staffShortId: route?.query?.staffShortId || null,
    csShortId: '',
    dateStart: `${_tempValReactive.startTime} 00:00:00`,
    dateEnd: `${_tempValReactive.startTime} 23:59:59`,
    productName: "",
    storeId: props.storeId ? props.storeId : undefined,
})
const {
  loadData,
  listData,
  verifiPageVO,
  isPageLoadingRef
} = SalesStatisticsList(searchParams)

const searchDateName = ref(null);
const calendarSelectShowRef = ref(false)
const showSearchTag = ref(false)
const tagOptionsList = [
    {
        label: '今日',
        value: 'today'
    },
    {
        label: '近7天',
        value: 'last_7_day'
    },
    {
        label: '近30天',
        value: 'last_30_day'
    },
    {
        label: '近90天',
        value: 'last_90_day'
    },
]
const calendarMinDate = computed(()=>{
   return dayjs(_tempValReactive.startTime).startOf('day').add(-365,'day').valueOf()
})
function timeSelect(){
    calendarSelectShowRef.value = true
}
onMounted(async()=>{
    await loadData(true)
})
function onDateConfirm(valueList:Array<Date>){
    _tempValReactive.startTime = dayjs(valueList[0]).format('YYYY-MM-DD')
    _tempValReactive.endTime = dayjs(valueList[1]).format('YYYY-MM-DD')
    let start_time = dayjs(valueList[0]).format('YYYY-MM-DD 00:00:00')
    let end_time = dayjs(valueList[1]).format('YYYY-MM-DD 23:59:59')
    calendarSelectShowRef.value = false
    _tempValReactive.isCustomeTime = true
    searchParams.dateStart = `${_tempValReactive.startTime} 00:00:00`
    searchParams.dateEnd = `${_tempValReactive.endTime} 23:59:59`
    loadData(true)
}
function handlerSelectDate(date){
    searchDateName.value = date.label || date.name;
    if(searchDateName.value == '近7天'){
        _tempValReactive.startTime = dayjs(dataTimes[date.value].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes[date.value].end_time).format('YYYY-MM-DD')
        date.startTime =  _tempValReactive.startTime;
        date.endTime =  _tempValReactive.endTime;
    }else if(searchDateName.value == '近30天'){
        _tempValReactive.startTime = dayjs(dataTimes[date.value].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes[date.value].end_time).format('YYYY-MM-DD')
    }else if(searchDateName.value == '近90天'){
        _tempValReactive.startTime = dayjs(dataTimes[date.value].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes[date.value].end_time).format('YYYY-MM-DD')
    }
    else{
        _tempValReactive.startTime = dayjs(dataTimes[date.value].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes[date.value].end_time).format('YYYY-MM-DD')
    }
    searchParams.dateStart = `${_tempValReactive.startTime} 00:00:00`
    searchParams.dateEnd = `${_tempValReactive.endTime} 23:59:59`
    loadData(true)
}
const changeSelect =()=>{
    showSearchTag.value = true
}
function updateSearch(data){
    searchParams.staffShortId = data.staffShortId
    searchParams.csShortId = data.csShortId
    searchParams.productName = data.productName
    loadData(true)
}
</script>
<style scoped lang="less">
.verWarpper{
    width: 100%;
    height: 100vh;
    .tip{
        width: 100%;
        height: 36px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #1677FF;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .dataSelect{
        // display: flex;
        padding: 10px;
        background-color: white;
        .lableSelect{
            display: flex;
            justify-content: space-between;
            align-items: center;
            .screen{
                display:flex;
                align-items: center;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 20px;
                text-align: right;
                font-style: normal;
                text-transform: none;
                .search-icon{
                    margin-left: 4px;
                    width: 0;
                    height: 0;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 6px solid #666666;
                }
            }
        }
        .selectTime{
            width: 100%;
            height: 30px;
            background-color: #F8F8F8;
            margin-top: 10px;
            border-radius: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;

        }
    }
    .dateTag {
        text-align: center;
        height: 24px;
        width: 64px;
        background: #F8F8F8;
        color: #999999;
        line-height: 24px;
        font-size: 12px;
        border-radius: 4px;
        font-weight: 600;
    }

    .active {
        background: #FFF4F4;
        color: #EF1115;
    }
    .content{
        width: 100%;
        height: calc(100% - 130px);
        background-color: white;
        margin-top: 10px;

    }
    .rotate-180 {
        transform: rotate(-180deg);
    }
}
</style>
