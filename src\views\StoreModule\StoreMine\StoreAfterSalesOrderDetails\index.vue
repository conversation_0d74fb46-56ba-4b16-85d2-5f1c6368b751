<template>
  <JLoadingWrapper class="wrapper" :show="isPageLoadingRef">
    <div class="store_after_order_detail_wrapper">
      <div class="store_after_order_detail_bg"></div>
      <div class="store_after_order_detail_content">
        <!-- 售后状态 -->
        <StoreAfterSalesStatus :state="afterSalesOrderDetailRef?.state" />
        <!-- 售后描述 -->
        <StoreReasonAfterSalesDes
          v-if="isShowReturnDescription"
          :afterSalesInfo="afterSalesOrderDetailRef"
        />
        <!-- 退货物流 -->
        <StoreReturnLogistics
          v-if="afterSalesOrderDetailRef?.state == AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT && !isStoreManagerReview"
          :afterSalesInfo="afterSalesOrderDetailRef"
        />
        <!-- 商家退货信息 -->
        <StoreMerchantReturnInfo
          v-if="[AfterSaleStatusEnum.PENDING_RETURN, AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT].includes(afterSalesOrderDetailRef?.state)"
          @fillLogisticsNumber="fillLogisticsNumber"
          :afterSalesInfo="afterSalesOrderDetailRef"
        />
        <!-- 售后商品 -->
        <StoreCommodityInfo :afterSalesInfo="afterSalesOrderDetailRef" />
        <!-- 售后类型 -->
        <StoreAfterSalesInfo :afterSalesInfo="afterSalesOrderDetailRef" />
      </div>
      <!-- footer -->
      <div class="footer">
        <div v-if="isMyOrderOperation" class="btn-group">
          <!-- 撤销申请 -->
          <div v-if="isPendingCustomer" class="withdraw_btn" @click.stop="handleWithdraw">撤销申请</div>
          <!-- 删除 -->
          <div v-if="isPendingMerchant" class="delete_btn" @click.stop="handleDelete">删除</div>
        </div>
        <!-- 店长审核 -->
        <div v-if="isStoreManagerReview" class="btn-group">
          <!-- 同意 -->
          <div
            v-if="isPendingMerchantAndRefundReturn || isPendingMerchantAndReturn"
            class="agree-btn"
            @click.stop="handleAgree"
          >
            同意
          </div>
          <!-- 收货并退款 -->
          <div v-if="isPendingMerchantReceive" class="receive-btn" @click.stop="handleReceiveAndRefund">收货并退款</div>
          <!-- 拒绝 -->
          <div
            v-if="isPendingMerchantAndRefundReturn || isPendingMerchantAndReturn || isPendingMerchantReceive"
            class="reject-btn"
            @click.stop="handleReject"
          >
            拒绝
          </div>
        </div>
      </div>
    </div>
    <!-- 二次确认 -->
    <JDoubleConfirm v-model:show="showDoubleConfirmRef" :tip="doubleConfirmRef.tip" @confirm="handleConfirm" />
    <!-- 拒绝 -->
    <StoreRefundReject
      v-model:show="showRejectRef"
      :recordNo="afterSalesOrderDetailRef?.recordNo"
      @success="getAfterSalesDetail"
    />
    <!-- 退货地址确认 -->
    <StoreReturnAddress
      v-model:show="showReturnAddressRef"
      :belongStoreId="afterSalesOrderDetailRef?.belongStoreId"
      @confirm="handleRefundAndReturnConfirm"
    />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, toRefs } from "vue";
import { useRouter } from "vue-router";
import { showToast } from 'vant';
import { isArray } from "@/utils/isUtils";
import useGetAfterSales from "./hooks/useGetAfterSales";
import { useMessages } from "@/hooks/useMessage";
import { useBoolean } from "@/views/StoreModule/hooks";
import { useUserRole } from "@/views/StoreModule/hooks";
import {
  AfterSaleStatusEnum,
  CustomerRoleOperationEnum,
  StoreAfterSaleDetailRouteTypeEnum,
  StoreAfterSaleTypeEnum,
  StoreRoleOperationEnum
} from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { executeAfterSaleActionApi, deleteAfterSaleRecordApi, executeAfterSaleActionByAdminApi } from "@/services/storeApi";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreAfterSalesStatus from "./components/StoreAfterSalesStatus.vue";
import StoreCommodityInfo from "./components/StoreCommodityInfo.vue";
import StoreAfterSalesInfo from "./components/StoreAfterSalesInfo.vue";
import StoreReasonAfterSalesDes from "./components/StoreReasonAfterSalesDes.vue";
import StoreMerchantReturnInfo from "./components/StoreMerchantReturnInfo.vue";
import StoreReturnLogistics from "./components/StoreReturnLogistics.vue";
import JDoubleConfirm from "@/views/StoreModule/components/JDoubleConfirm.vue";
import StoreRefundReject from "@/views/StoreModule/StoreMine/StoreRefundAudit/components/StoreRefundReject.vue";
import StoreReturnAddress from "../components/StoreReturnAddress.vue";

defineOptions({ name: 'StoreAfterSalesOrderDetails' });

/** props */
const props = defineProps<{
  recordNo: string;
  routeType: StoreAfterSaleDetailRouteTypeEnum;
}>();

const { routerPushByRouteName } = useRouterUtils();
const { recordNo: afterSalesRecordNo, routeType: afterSalesRouteType } = toRefs(props);
const router = useRouter();
const { isStoreOwner } = useUserRole();
const { createMessageSuccess, createMessageError } = useMessages();
const { isPageLoadingRef, afterSalesOrderDetailRef, getAfterSalesDetail } = useGetAfterSales({
  recordNo: afterSalesRecordNo.value,
});

/** 退货退款，退货地址确认 */
const { bool: showReturnAddressRef, setFalse: hideReturnAddress, setTrue: showReturnAddress } = useBoolean(false);

/**
 * @description computed
 */
/** 是否展示退货描述 */
const isShowReturnDescription = computed(() => {
  if (isStoreManagerReview.value) {
    return [
      AfterSaleStatusEnum.MERCHANT_REFUSED, 
      AfterSaleStatusEnum.REFUNDING, 
      AfterSaleStatusEnum.REFUND_COMPLETED,
      AfterSaleStatusEnum.RETURN_CLOSED,
      AfterSaleStatusEnum.CUSTOMER_WITHDRAWN,
      AfterSaleStatusEnum.PENDING_PAYMENT,
      AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT
    ].includes(afterSalesOrderDetailRef.value?.state);
  } else {
    return [
      AfterSaleStatusEnum.MERCHANT_REFUSED, 
      AfterSaleStatusEnum.REFUNDING, 
      AfterSaleStatusEnum.REFUND_COMPLETED,
      AfterSaleStatusEnum.RETURN_CLOSED,
      AfterSaleStatusEnum.CUSTOMER_WITHDRAWN,
      AfterSaleStatusEnum.PENDING_PAYMENT,
    ].includes(afterSalesOrderDetailRef.value?.state);
  }
});

/** 是否可撤销申请 */
const isPendingCustomer = computed(() => {
  if (isArray(afterSalesOrderDetailRef.value?.action) && afterSalesOrderDetailRef.value?.action.includes(CustomerRoleOperationEnum.WITHDRAW_APPLICATION)) {
    return [AfterSaleStatusEnum.PENDING_MERCHANT, AfterSaleStatusEnum.PENDING_RETURN].includes(afterSalesOrderDetailRef.value?.state);
  }
  return false;
});

/** 是否可删除 */
const isPendingMerchant = computed(() => {
  return [
    AfterSaleStatusEnum.MERCHANT_REFUSED,
    AfterSaleStatusEnum.CANCEL_REJECTED,
    AfterSaleStatusEnum.RETURN_CLOSED,
    AfterSaleStatusEnum.CUSTOMER_WITHDRAWN,
    AfterSaleStatusEnum.CANCEL_APPROVED,
    AfterSaleStatusEnum.REFUND_COMPLETED
  ].includes(afterSalesOrderDetailRef.value?.state);
});

/** 是否归属我的订单操作 */
const isMyOrderOperation = computed(() => {
  return afterSalesRouteType.value == StoreAfterSaleDetailRouteTypeEnum.MY_AFTER_SALE;
});

/** 是否店长审核 */
const isStoreManagerReview = computed(() => {
  return isStoreOwner && afterSalesRouteType.value == StoreAfterSaleDetailRouteTypeEnum.REFUND_AUDIT;
});

/** 是否待商家受理且退货退款 */
const isPendingMerchantAndRefundReturn = computed(() => {
  return afterSalesOrderDetailRef.value?.state == AfterSaleStatusEnum.PENDING_MERCHANT && afterSalesOrderDetailRef.value?.type == StoreAfterSaleTypeEnum.REFUND_RETURN;
});

/** 是否待商家受理且仅退款 */
const isPendingMerchantAndReturn = computed(() => {
  return afterSalesOrderDetailRef.value?.state == AfterSaleStatusEnum.PENDING_MERCHANT && afterSalesOrderDetailRef.value?.type == StoreAfterSaleTypeEnum.REFUND;
});

/** 是否待商家收货 */
const isPendingMerchantReceive = computed(() => {
  return afterSalesOrderDetailRef.value?.state == AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT;
});

/**
 * @description 二次确认
 * withdraw 撤销售后记录
 * delete 删除售后记录
 * agreeToRefundAndReturn 同意退货退款
 * agreeToRefund 同意退款
 * receiveAndRefund 收货并退款
 */
type DoubleConfirmType = "withdraw" | "delete" | "agreeToRefundAndReturn" | "agreeToRefund" | "receiveAndRefund";
const { bool: showDoubleConfirmRef, setTrue: showDoubleConfirm, setFalse: hideDoubleConfirm } = useBoolean(false);
const doubleConfirmRef = ref<{
  type: DoubleConfirmType;
  tip: string;
}>({
  type: 'delete',
  tip: '确认删除该售后单吗？',
});
/** 二次确认回调 */
async function handleConfirm() {
  const { type } = doubleConfirmRef.value;
  try {
    // 删除售后单
    if (type == 'delete') {
      await deleteAfterSaleRecordApi({
        recordNo: afterSalesRecordNo.value,
      });
      showToast("删除成功");
    }
    // 撤销申请
    if (type == 'withdraw') {
      let _params = {
        recordNo: afterSalesRecordNo.value,
        action: CustomerRoleOperationEnum.WITHDRAW_APPLICATION,
      };
      await executeAfterSaleActionApi(_params);
      showToast("撤销申请成功");
    }
    // 同意退货退款
    if (type == 'agreeToRefundAndReturn') {
      showReturnAddress();
      return;
    }

    // 同意退款
    if (type == 'agreeToRefund') {
      let _params = {
        recordNo: afterSalesRecordNo.value,
        action: StoreRoleOperationEnum.AGREE_TO_REFUND_ONLINE,
      };
      await executeAfterSaleActionByAdminApi(_params);
      showToast("同意退款成功");
    }

    // 收货并退款
    if (type == 'receiveAndRefund') {
      let _params = {
        recordNo: afterSalesRecordNo.value,
        action: StoreRoleOperationEnum.RECEIPT_AND_REFUND_ONLINE,
      };
      await executeAfterSaleActionByAdminApi(_params);
      showToast("收货并退款成功");
    }
    router.back();
  } catch (error) {
    createMessageError(`${type == 'delete' ? '删除' : '撤销申请'}失败：` + error);
  }
}

/** 确认退货退款 */
async function handleRefundAndReturnConfirm() {
  try {
    let _params = {
      recordNo: afterSalesRecordNo.value,
      action: StoreRoleOperationEnum.AGREE_TO_REFUND_AND_RETURN,
    };
    await executeAfterSaleActionByAdminApi(_params);
    showToast("同意退货退款成功");
    // 刷新
    getAfterSalesDetail();
  } catch (error) {
    createMessageError(`操作失败：` + error);
  }
}
/** 二次确认 end */

/** 同意 start */
function handleAgree() {
  const { type } = afterSalesOrderDetailRef.value;
  doubleConfirmRef.value = {
    type: type  == StoreAfterSaleTypeEnum.REFUND_RETURN ? 'agreeToRefundAndReturn' : 'agreeToRefund',
    tip: type  == StoreAfterSaleTypeEnum.REFUND_RETURN ? '是否同意退货退款？' : '是否同意退款？',
  };
  showDoubleConfirm();
}
/** 同意 end */

/** 拒绝 start */
const showRejectRef = ref(false);
const handleReject = () => {
  showRejectRef.value = true;
};
/** 拒绝 end */

/** 收货并退款 start */
function handleReceiveAndRefund() {
  doubleConfirmRef.value = {
    type: 'receiveAndRefund',
    tip: '是否确认收货并退款？',
  };
  showDoubleConfirm();
}
/** 收货并退款 end */

/** 删除售后记录 start */
async function handleDelete() {
  doubleConfirmRef.value = {
    type: 'delete',
    tip: '确认删除该售后单吗？',
  };
  showDoubleConfirm();
}
/** 删除售后记录 end */

/** 撤销售后记录 start */
async function handleWithdraw() {
  doubleConfirmRef.value = {
    type: 'withdraw',
    tip: '确认撤销申请吗？',
  };
  showDoubleConfirm();
}
/** 撤销售后记录 end */

/** 填写物流单号 */
function fillLogisticsNumber() {
  routerPushByRouteName(RoutesName.StoreFillLogisticsNo, { recordNo: afterSalesRecordNo.value });
}

/** 组件挂载 */
onMounted(() => {
  if (!afterSalesRecordNo.value) {
    createMessageError("售后单号不能为空");
    return;
  }
  getAfterSalesDetail();
});
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100vh;
  .store_after_order_detail_wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      background: #F8F8F8;
      display: flex;
      flex-direction: column;
      .store_after_order_detail_bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 480px;
          z-index: 1;
          background: url(@/assets/storeImage/storeAfterSales/rectangle.png) no-repeat;
          background-size: 100% 100%;
      }
      .store_after_order_detail_content {
          flex: 1;
          width: 100%;
          height: 100%;
          position: relative;
          z-index: 2;
          display: flex;
          flex-direction: column;
          gap: 12px;
          padding: 12px;
          box-sizing: border-box;
          overflow-y: auto;
      }
    }
    .footer {
      width: 100%;
      background: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
      box-sizing: border-box;
      .btn-group {
        display: flex;
        align-items: center;
        .withdraw_btn,
        .reject-btn,
        .receive-btn,
        .agree-btn,
        .delete_btn {
          margin: 8px 6px;
          min-height: 32px;
          height: 100%;
          border-radius: 999px;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .withdraw_btn {
          width: 120px;
          background: #FFF4F4;
          color: #EF1115;
        }
        .delete_btn {
          width: 80px;
          background: #FFF4F4;
          color: #EF1115;
        }
        .agree-btn {
          width: 80px;
          background: #ECF5FF;
          color: #4DA4FF;
        }
        .receive-btn {
          width: 114px;
          background: #EF1115;
          color: #FFFFFF;
        }
        .reject-btn {
          width: 80px;
          background: #FFF4F4;
          color: #EF1115
        }
      }

      :deep(.van-button__text) {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
  }
}
</style>
