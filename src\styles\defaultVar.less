@primary-color: #1677FF;

@success-color: #00b42a;

@error-color: #FF4747;

@primary-color-hover: #7879f8;

@main-header-height: 60px;
@main-footer-height: 0px;
@main-content-height:calc (100vh - @main-header-height - @main-footer-height);
@main-content-width: 100vw;

@blank-background-color: #f2f3f5;

@default-border-radius: 5px;

@default-padding-md: 10px;
@default-padding-lg: 16px 12px;

@blank-page-padding: 4px;


@default-text-color:#333333;
@secondary-text-color: #666666;

@default-border-color: #eeeeee;

@inner-bg-height: calc(100vh - @main-header-height - @main-footer-height - @blank-page-padding*2);

@tab-pane-inner-bg-height: calc(100vh - 42px - @main-header-height - @main-footer-height - @blank-page-padding*2);

@font-size-sm: 14px;
@font-size-base: 16px;
@font-size-lg: 18px;

@previe-padding:0px 0px 4px;
@preview-height:80px;

@default-border-radius: 10px;
@default-padding: 16px 12px;

@warning-color-gradient: linear-gradient( 90deg, #FF8A00 0%, #FF4D00 100%);
@warning-color:#FF4D00;