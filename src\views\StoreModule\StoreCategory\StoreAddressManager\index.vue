<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <div class="store_address_manager">
      <div class="store_address_manager_wrapper">
        <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="store_address_manager_content">
          <template v-if="addressList.length">
            <VanList v-model:loading="loadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
              <AddressCard
                v-for="item in addressList"
                :key="item.id"
                :addressInfo="item"
                @click="handleClickAddress(item)"
                @deleteAddress="handleDeleteAddress"
                @editAddress="handleEditAddress"
                @setDefaultAddress="handleUpdateDefaultAddress(item)"
              />
            </VanList>
          </template>
          <template v-else>
            <EmptyAddress />
          </template>
        </VanPullRefresh>
      </div>
      <div class="footer">
        <VanButton @click="handleAddAddress" round type="danger" style="width: 100%;height: 40px;">添加新地址</VanButton>
      </div>
    </div>
    <!-- 二次确认 -->
    <JDoubleConfirm
      v-model:show="showDoubleConfirmRef"
      tip="确认删除该地址吗？"
      @confirm="handleConfirmDeleteAddress"
    />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { showToast } from "vant";
import { useRouter } from "vue-router";
import { useMessages } from "@/hooks/useMessage";
import { useRouterUtils, usePaginatedFetch } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { AddressModeEnum, StoreAddressRouteTypeEnum } from "@/views/StoreModule/enums";
import { deleteAddress, updateDefaultAddress } from "@/services/storeApi";
import { useUserAddress } from "../hooks";
import { getCustomerAddress } from "@/services/storeApi";
/** 相关组件 */
import EmptyAddress from "./components/EmptyAddress.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import AddressCard from "./components/AddressCard.vue";
import JDoubleConfirm from "@/views/StoreModule/components/JDoubleConfirm.vue";

defineOptions({ name: 'StoreAddressManager' });

/** props */
const props = withDefaults(defineProps<{
  routeType: StoreAddressRouteTypeEnum;
  customerId: string
}>(), {
  routeType: StoreAddressRouteTypeEnum.ORDER_ADDRESS,
  customerId:""
});

const router = useRouter();
const { createMessageError, createMessageSuccess } = useMessages();
const { routerPushByRouteName } = useRouterUtils();
const showDoubleConfirmRef = ref(false);
const deleteAddressIdRef = ref(null);
const { currentAddressInfo } = useUserAddress();

const {
  isPageLoadingRef,
  setPageLoadingTrue,
  setPageLoadingFalse,
  refreshingRef,
  loadingRef,
  isFinishedRef,
  searchParamsRef,
  pageListRef: addressList,
  initPageDataRequest: initAddressList,
  onLoad,
  onRefresh,
} = usePaginatedFetch({
  fetchApi: getCustomerAddress,
  beforeRequest: (params) => {
    return new Promise((resolve) => {
      resolve({
        ...params,
        data: {
          ...params.data,
          customerId: props.customerId
        }
      });
    });
  }
});

/** 新增地址 */
function handleAddAddress() {
  routerPushByRouteName(RoutesName.StoreAddressAdd, { type: AddressModeEnum.ADD });
}

/** 删除地址 */
function handleDeleteAddress(id: string) {
  deleteAddressIdRef.value = id;
  showDoubleConfirmRef.value = true;
}
/** 确认删除地址 */
async function handleConfirmDeleteAddress() {
  try {
    const _params = { id: deleteAddressIdRef.value, customerId: props?.customerId};
    await deleteAddress(_params);
    showToast('删除成功');
    initAddressList();
    showDoubleConfirmRef.value = false;
  } catch (error) {
    createMessageError('删除失败：' + error);
  }
}

/** 编辑地址 */
function handleEditAddress(id: string) {
  routerPushByRouteName(RoutesName.StoreAddressAdd, { type: AddressModeEnum.EDIT, id });
}

/** 点击地址 */
function handleClickAddress(addressInfo) {
  if (props.routeType == StoreAddressRouteTypeEnum.MY_ADDRESS) return;
  currentAddressInfo.value = addressInfo;
  router.back();
}

/** 更改默认地址 */
async function handleUpdateDefaultAddress(addressInfo) {
  try {
    const _params = {
      id: addressInfo.id,
      isDefault: 1,
      customerId: props.customerId
    };
    await updateDefaultAddress(_params);
    showToast('修改默认地址成功');
    initAddressList();
    showDoubleConfirmRef.value = false;
  } catch (error) {
    createMessageError('修改默认地址失败：' + error);
    initAddressList();
  }
}

onMounted(async () => {
  setPageLoadingTrue();
  /** 初始化 */
  await initAddressList();
  setPageLoadingFalse();
});

</script>

<style lang="less" scoped>
.store_address_manager {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #F8F8F8;
    .store_address_manager_wrapper {
      flex: 1;
      width: 100%;
      height: calc(100vh - 76px - env(safe-area-inset-bottom));
      .store_address_manager_content {
        width: 100%;
        height: 100%;
        padding: 12px 10px;
        box-sizing: border-box;
        overflow-y: auto;
      }
    }
    .footer {
        width: 100%;
        padding: 12px 16px;
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
        box-sizing: border-box;
        background: #fff;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);

        .add-btn {
          height: 44px;
          font-size: 16px;
          font-weight: 500;
        }
        :deep(.van-button__text) {
          font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 14px;
          line-height: 24px;
        }
    }
}
</style>
