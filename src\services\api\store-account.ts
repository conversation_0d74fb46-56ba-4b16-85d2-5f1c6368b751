import { JRequest } from '@/services/index'
import { UserType } from '@/enum/userTypeEnum'
export const enum StoreAccountApi {
  getStoreLoginWxappID = '/h5/globalConfigs/getGlobalConfigs',
  getPhone = '/applet/getAppletPhone',
  login = '/applet/login',
}
export interface LoginResp {
  idNo: string
  mobile: string
  nickname: string
  token: string
  name: string
  gender: string
  type: UserType
}
interface AccountLoginParams {
  code: string
  sharingInfo?: string
  sharingType: number
}
export function getStoreLoginWxappID() {
  return JRequest.get({
    url: StoreAccountApi.getStoreLoginWxappID,
    requestConfig: {
      withToken: false,
    },
    option: {
      timeout: 5000,
    },
  })
}
export async function getUserPhoneByCode(phoneCode: string) {
  return JRequest.post<string>({
    url: `${StoreAccountApi.getPhone}?phoneCode=${phoneCode}`,
  })
}
export async function accountLogin(params: AccountLoginParams) {
  let _url = `${StoreAccountApi.login}`
  return JRequest.post<LoginResp>({
    url: _url,
    params: {
      data: {
        code: params.code,
        ...(params.sharingInfo ? { sharingInfo: params.sharingInfo } : {}),
        sharingType: params.sharingType,
      },
    },
    requestConfig: {
      withToken: true,
    },
  })
}
