<template>
  <view class="wrapper" @click="emit('viewDetails', orderInfoRef?.code)">
    <view class="pending-orders-header">
      <view class="order-code">
        <text class="van-ellipsis order-code-text">{{ `订单编号：${orderInfoRef?.code}` }}</text>
        <img
          :src="CopySrc"
          alt="复制"
          class="order-code-copy"
          @click.stop="handleCopyID(orderInfoRef?.code)"
        />
      </view>
      <!-- 状态 -->
      <text
        class="pending-orders-status"
        :style="{ color: orderStatusColor, fontSize: isStoreVerification ? '24rpx' : '28rpx' }"
      >
        {{ orderStatusMap[orderInfoRef?.status] }}
      </text>
    </view>
    <!-- 订单信息 -->
    <view class="pending-orders-info">
      <img
        :src="firstOrderItem?.productImgPath ? firstOrderItem?.productImgPath : CouponSrc"
        alt=""
        class="pending-orders-info-img"
      />
      <!-- 是否积分兑换、福利券兑换 -->
      <view
        v-if="[StoreOrderTypeEnum.COUPON, StoreOrderTypeEnum.INTEGRAL].includes(orderInfoRef?.type)"
        class="pending-orders-type"
        :style="{
          backgroundColor: orderInfoRef?.type === StoreOrderTypeEnum.COUPON ? '#4BE092' : '#FFBC47',
        }"
      >
        {{ orderTypeMap[orderInfoRef?.type] }}
      </view>
      <view class="pending-orders-info-text">
        <view class="pending-orders-info-title">
          <p class="van-multi-ellipsis--l2 commodity-title">
            {{ firstOrderItem?.productFrontName }}
          </p>
          <view class="pending-orders-info-right">
            <!-- 价格 -->
            <text v-if="[StoreOrderTypeEnum.INTEGRAL].includes(orderInfoRef?.type)" class="price">
              {{ firstOrderItem.exchangePoints || 0 }}积分
              <text v-if="firstOrderItem.exchangePrice">
                +￥{{ (firstOrderItem.exchangePrice / 100).toFixed(2) }}
              </text>
            </text>
            <text v-else class="price">
              {{ `¥ ${Number((firstOrderItem?.price ?? 0) / 100).toFixed(2)}` }}
            </text>
            <!-- 数量 -->
            <text class="count">{{ `x ${firstOrderItem?.count}` }}</text>
          </view>
        </view>
        <!-- 规格 -->
        <view class="specification">{{ firstOrderItem?.specName }}</view>
        <!-- 订单金额 -->
        <view class="order-amount">
          {{ `订单金额￥${Number(orderInfoRef?.money / 100).toFixed(2)}` }}
        </view>
      </view>
    </view>
    <!-- footer -->
    <view class="footer">
      <template v-if="[StoreOrderStatusEnum.WAIT_PAY].includes(orderInfoRef?.status)">
        <!-- 取消订单 -->
        <view class="cancellation-order" @click.stop="handleCancelOrder">取消订单</view>
        <!-- 付款 -->
        <view class="payment-order" @click.stop="handlePay">付款</view>
      </template>
      <template
        v-if="
          [StoreOrderStatusEnum.WAIT_SEND].includes(orderInfoRef?.status) &&
          isSelfPickUp &&
          !isAutoVerification
        "
      >
        <!-- 核销码 -->
        <view class="write-off-order" @click.stop="handleWriteOff">核销码</view>
      </template>
      <!-- 查看物流与确认收货 -->
      <template v-if="[StoreOrderStatusEnum.WAIT_RECEIVE].includes(orderInfoRef?.status)">
        <!-- 查看物流 -->
        <view class="logistics-order" @click.stop="handleViewLogistics">查看物流</view>
        <!-- 确认收货 -->
        <view class="receipt-order" @click.stop="handleConfirmReceipt">确认收货</view>
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { toRefs, computed } from 'vue'
import { copyText } from '@/utils/clipboardUtils'
import {
  StoreOrderTypeEnum,
  StoreOrderStatusEnum,
  ProductPickupModeEnum,
  OrderVerificationTypeEnum,
} from '@/enum'
import { StoreGoodsEnum } from '@/enum'
import { useMessages } from '@/hooks/common'
import { isInFrame, isIOSEnv } from '@/utils/isUtils'
import { RoutesName } from '@/routes/enums/routeNameEnum'
// import { MessageEventEnum, useWindowMessage } from '@/hooks/useWindowMessage'
/** 静态资源 */
import CopySrc from '@/static/images/storeUser/copy-icon.png'
import CouponSrc from '@/static/images/storeHome/coupon.png'
/** 相关组件 */
import StorePrice from '@/views/StoreModule/components/StorePrice.vue'

defineOptions({ name: 'StoreMineOrderCard' })

/** props */
const props = defineProps<{
  orderInfo: {
    type?: StoreOrderTypeEnum
    status?: StoreOrderStatusEnum
    verificationType?: OrderVerificationTypeEnum
    code?: string
    pickupType?: number
    money?: number
    goodsAmount?: number
    payStatus?: number
    afterSaleState?: number
    orderItemDTOList?: Array<{
      type?: 1 | 2 | 3
      orderId?: string
      productImgPath?: string
      productFrontName?: string
      specName?: string
      price?: number
      count?: number
      exchangePoints?: number
      exchangePrice?: number
    }>
  }
}>()

/** emit */
const emit = defineEmits<{
  /** 核销码 */
  (e: 'writeOffCode', orderCode: string): void
  /** 取消订单 */
  (e: 'cancelOrder', orderCode: string): void
  /** 付款 */
  (e: 'paymentOrder'): void
  /** 确认收货 */
  (e: 'confirmReceipt', orderCode: string): void
  /** 查看物流 */
  (e: 'viewLogistics'): void
  /** 查看详情 */
  (e: 'viewDetails', orderCode: string): void
}>()

const { orderInfo: orderInfoRef } = toRefs(props)

// const { sendMessageToWindows } = useWindowMessage()
const { createMessageSuccess, createMessageError } = useMessages()

/**
 * @description 计算属性
 */

/** 商品是否下单门店到货后核销 */
const isStoreVerification = computed(() => {
  return orderInfoRef.value?.verificationType == OrderVerificationTypeEnum.STORE_VERIFICATION
})

/** 商品是否自提 */
const isSelfPickUp = computed(() => {
  return orderInfoRef.value?.pickupType == ProductPickupModeEnum.STORE_PICKUP
})

/** 是否下单自动核销 */
const isAutoVerification = computed(() => {
  return orderInfoRef.value?.verificationType == OrderVerificationTypeEnum.AUTO_VERIFICATION
})

/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
  return orderInfoRef.value?.orderItemDTOList?.[0]
})

/** 商品是否快递到家 */
const isHomeDelivery = computed(() => {
  return orderInfoRef.value?.pickupType == ProductPickupModeEnum.HOME_DELIVERY
})

/** 待发货提示语 */
function getWaitSendStatus() {
  if (isHomeDelivery.value) return '待发货'
  if (isStoreVerification.value) return '待发货至门店后提货'
  return '待提货'
}

/** 订单类型 */
const orderTypeMap = {
  [StoreOrderTypeEnum.INTEGRAL]: '积分兑换',
  [StoreOrderTypeEnum.COUPON]: '福利券兑换',
}

/** 订单状态 */
const orderStatusMap = computed(() => ({
  [StoreOrderStatusEnum.WAIT_PAY]: '待付款',
  [StoreOrderStatusEnum.WAIT_SEND]: getWaitSendStatus(),
  [StoreOrderStatusEnum.WAIT_RECEIVE]: '待收货',
  [StoreOrderStatusEnum.FINISHED]: '已完成',
  [StoreOrderStatusEnum.CANCELLED]: '已取消',
}))

/** 订单状态字体颜色 */
const orderStatusColorMap = {
  [StoreOrderStatusEnum.WAIT_PAY]: '#FF6864',
  [StoreOrderStatusEnum.WAIT_SEND]: '#4DA4FF',
  [StoreOrderStatusEnum.WAIT_RECEIVE]: '#4DA4FF',
  [StoreOrderStatusEnum.FINISHED]: '#4BE092',
  [StoreOrderStatusEnum.CANCELLED]: '#999999',
}
const orderStatusColor = computed(() => {
  return orderStatusColorMap[orderInfoRef.value?.status]
})

/** 复制订单ID */
function handleCopyID(data) {
  try {
    copyText(data)
    // showToast('复制订单号成功')
  } catch (e) {
    // showToast('复制订单号失败')
  }
}

/** 付款 */
const handlePay = () => {
  const typeMap = {
    [StoreOrderTypeEnum.NORMAL]: StoreGoodsEnum.Goods,
    [StoreOrderTypeEnum.INTEGRAL]: StoreGoodsEnum.IntegralGoods,
    [StoreOrderTypeEnum.COUPON]: StoreGoodsEnum.WelfareTicket,
  }
  if (isInFrame()) {
    // const url = `${location.origin}/st/cashier?orderCode=${orderInfoRef.value?.code}&state=${route.query.state}&type=${typeMap[orderInfoRef.value?.type]}`
    if (isIOSEnv()) {
      //   sendMessageToWindows(MessageEventEnum.sendUrlToWindow, url)
    } else {
      //   window.open(url)
    }
  } else {
    // router.push({
    //   name: RoutesName.StoreCashier,
    //   query: {
    //     type: typeMap[orderInfoRef.value?.type],
    //     orderCode: orderInfoRef.value?.code,
    //   },
    // })
  }
}

function handleWriteOff() {
  emit('writeOffCode', orderInfoRef.value?.code)
}

function handleCancelOrder() {
  emit('cancelOrder', orderInfoRef.value?.code)
}

/** 确认收货 */
function handleConfirmReceipt() {
  emit('confirmReceipt', orderInfoRef.value?.code)
}

/** 查看物流 */
function handleViewLogistics() {
  emit('viewLogistics')
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 16rpx;

  .pending-orders-header {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .order-code {
      display: flex;
      align-items: center;

      .order-code-text {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 500;
        font-size: 24rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .order-code-copy {
        width: 32rpx;
        height: 32rpx;
        margin-left: 8rpx;
      }
    }

    .pending-orders-status {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
      margin-left: auto;
      line-height: 32rpx;
    }
  }

  .pending-orders-info {
    display: flex;
    gap: 16rpx;
    position: relative;

    .pending-orders-info-img {
      width: 128rpx;
      height: 128rpx;
      border-radius: 16rpx;
    }

    .pending-orders-type {
      position: absolute;
      top: 0;
      left: 0;
      width: 106rpx;
      height: 32rpx;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 500;
      font-size: 16rpx;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      text-transform: none;
      border-top-left-radius: 16rpx;
      border-bottom-right-radius: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
    }

    .pending-orders-info-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .pending-orders-info-title {
        display: flex;
        gap: 24rpx;

        .commodity-title {
          flex: 1;
          font-family:
            Source Han Sans CN,
            Source Han Sans CN;
          font-weight: 500;
          font-size: 32rpx;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 44rpx;
        }

        .pending-orders-info-right {
          display: flex;
          flex-direction: column;
          gap: 16rpx;

          .price {
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .count {
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 400;
            font-size: 26rpx;
            color: #333333;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .specification {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .order-amount {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 48rpx;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .footer {
    width: 100%;
    margin-top: 12rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16rpx;
    box-sizing: border-box;

    .cancellation-order,
    .payment-order,
    .write-off-order,
    .logistics-order,
    .receipt-order {
      height: 40rpx;
      padding: 8rpx 0rpx;
      border-radius: 999rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .cancellation-order,
    .logistics-order {
      width: 160rpx;
      background: #f8f8f8;
      color: #333333;
    }

    .write-off-order,
    .payment-order,
    .receipt-order {
      width: 160rpx;
      background: #fff4f4;
      color: #ef1115;
    }
  }
}
</style>
