import { ref } from "vue";
import { StoreIntegralRouteTypeEnum } from "@/views/StoreModule/enums";
import { getIntegralBalance } from "@/services/storeApi";

export default function useGetIntegralBalance(_params?: {
  customerId: string;
  requestPageSource: StoreIntegralRouteTypeEnum;
}) {
  /** 积分余额 */
  const integralBalance = ref<number>(0);

  /** 获取积分余额 */
  async function queryIntegralBalance() {
      try {
        if (!_params) return;
        const { customerId, requestPageSource } = _params;
        const resp = await getIntegralBalance({
          customerId: customerId ? customerId : undefined,
          requestPageSource
        });
        if (resp) {
            integralBalance.value = resp?.availPoints ?? 0;
        }
      } catch (error) {
        console.log("获取积分余额失败", error);
      }
  }

  return {
    integralBalance,
    queryIntegralBalance
  }
}
