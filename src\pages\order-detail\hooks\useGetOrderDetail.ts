import { ref } from 'vue'
import { useMessages } from '@/hooks/common'
import { getOrderDetailByOrderCode } from '@/services/api'

export default function useGetOrderDetail() {
  const isPageLoadingRef = ref(false)
  const { createMessageSuccess, createMessageError } = useMessages()

  /** 订单详情 */
  const orderDetailRef = ref<any>({})

  /** 根据订单编号获取订单详情 */
  async function getOrderDetail(orderCode: string) {
    try {
      isPageLoadingRef.value = true
      const resp = await getOrderDetailByOrderCode({
        orderCode,
      })
      if (resp) {
        orderDetailRef.value = resp
      }
      isPageLoadingRef.value = false
    } catch (error) {
      if (error && error.includes('不存在此订单')) {
        createMessageSuccess('无核销权限')
      } else {
        createMessageError('获取订单详情失败：' + error)
      }
      isPageLoadingRef.value = false
    }
  }

  return {
    isPageLoadingRef,
    getOrderDetail,
    orderDetailRef,
  }
}
