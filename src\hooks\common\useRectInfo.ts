import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

interface RectInfo {
  /** 胶囊宽度 */
  width: number
  /** 胶囊高度 */
  height: number
  /** 上边界坐标 */
  top: number
  /** 左边界坐标 */
  left: number
  /** 右边界坐标 */
  right: number
  /** 下边界坐标 */
  bottom: number
  /** 胶囊距顶部距离(导航栏) */
  mTop: number
  /** 胶囊距左侧距离(导航栏) */
  mLeft: number
  /** 胶囊距右侧距离(导航栏) */
  mRight: number
  /** 胶囊距底部距离(导航栏) */
  mBottom: number
  /** 胶囊高度差 胶囊顶部距屏幕顶部距离 - 状态栏高度 */
  altitudeDiff: number
  /** 导航栏高度 胶囊高度 + (胶囊顶部距屏幕顶部距离 - 状态栏高度) x 2 */
  navbarHeight: number
  /** 整体的高度 状态栏高度 + 导航栏高度 */
  overallHeight: number
  /** 状态栏高度 */
  statusBarHeight: number
}

/**
 * @description 获取胶囊按钮信息
 */
export default function useRectInfo() {
  /** 胶囊按钮信息 */
  const rectInfo = ref<RectInfo>({
    width: 0,
    height: 0,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    mTop: 0,
    mLeft: 0,
    mRight: 0,
    mBottom: 0,
    altitudeDiff: 0,
    navbarHeight: 0,
    overallHeight: 0,
    statusBarHeight: 0,
  })

  /** api */
  function getRectSizeInfo() {
    uni.getSystemInfo({
      success: (res) => {
        const { width, height, top, left, right, bottom } = uni.getMenuButtonBoundingClientRect()
        const { statusBarHeight, screenWidth } = res
        rectInfo.value = {
          width,
          height,
          top,
          left,
          right,
          bottom,
          mTop: top - statusBarHeight,
          mLeft: left,
          mRight: screenWidth - right,
          mBottom: top - statusBarHeight,
          altitudeDiff: top - statusBarHeight,
          navbarHeight: height + (top - statusBarHeight) * 2,
          overallHeight: statusBarHeight + height + (top - statusBarHeight) * 2,
          statusBarHeight,
        }
        console.log('胶囊按钮信息', rectInfo.value)
      },
      fail: (err) => {
        console.log(err)
      },
    })
  }

  /** 监听页面加载 */
  onLoad(() => {
    getRectSizeInfo()
  })

  return {
    getRectSizeInfo,
    rectInfo,
  }
}
