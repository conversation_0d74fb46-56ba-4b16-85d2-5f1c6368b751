import type { TAxiosConfig } from "./type";
import { ContentTypeEnum } from "@/enums/http.js";
import { deepmerge } from "deepmerge-ts";
import { usePremissionStoreWithoutSetup } from "@/stores/modules/premission";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { isObject } from "@/utils/isUtils";
import { isStoreApi } from "../urlUtils";

function responeseTypeHandler(config: TAxiosConfig): void {
  const { requestOptions } = config;
  if (requestOptions.responeseType === "stream") {
    config.responseType = "blob";
  } else if (requestOptions.responeseType === "json") {
    config.responseType = "json";
  }
}

function requestContentTypeHandler(config: TAxiosConfig): void {
  const { requestOptions } = config;
  if (requestOptions.requestContentType === "json") {
    config.headers["Content-Type"] = ContentTypeEnum.JSON;
  } else if (requestOptions.requestContentType === "form-urlencoded") {
    config.headers["Content-Type"] = ContentTypeEnum.FORM_URLENCODED;
  } else if (requestOptions.requestContentType === "form-data") {
    config.headers["Content-Type"] = ContentTypeEnum.FORM_DATA;
  }
}

function tokenHandler(config: TAxiosConfig): void {
  const { requestOptions } = config;
  const userStore = useUserStoreWithoutSetup();
  const _token = isStoreApi(config.url)? userStore.storeToken : userStore.token;
  if (_token && requestOptions.withToken) {
    config.headers[import.meta.env.VITE_TOKEN_NAME] = _token;
  }
}

function extendHeadersHandler(config: TAxiosConfig): void {
  const { requestOptions } = config;

  if(isObject(requestOptions.extendHeaders)){
    config.headers = {
      ...config.headers,
      ...requestOptions.extendHeaders
    }
  }
}


export function axiosConfigTransform(config: TAxiosConfig): TAxiosConfig {
  const _configAfterTransform: TAxiosConfig = deepmerge({}, config);
  responeseTypeHandler(_configAfterTransform);
  requestContentTypeHandler(_configAfterTransform);
  tokenHandler(_configAfterTransform);
  extendHeadersHandler(_configAfterTransform)
  return _configAfterTransform;
}
