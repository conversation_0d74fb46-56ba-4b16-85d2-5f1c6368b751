<template>
	<div class="number-box">
		<div class="pre-btn" @click.stop="handleChange('decrease')">
			<van-icon name="minus" size="12" />
		</div>
		<div class="content">
			<input type="text" :readonly="disabledInput" :value="tempValue" @input="handleInput"
				@blur="handleBlur">
		</div>
		<div class="sub-btn" @click.stop="handleChange('add')">
			<van-icon name="plus" size="12" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref,getCurrentInstance,watch } from "vue";
import { useMessages } from "@/hooks/useMessage";
interface Props {
	value: number;
	maxSize: number;
	minSize: number;
	step: number;
	maxStock: number;
	isUseStockTip: boolean;
	disabledInput:boolean;
}
type StepType = 'add' | 'decrease'
const message = useMessages()
const props = withDefaults(defineProps<Props>(), {
	value: 1,
	maxSize: 1,
	minSize: 1,
	step: 1,
	maxStock: 1,
	isUseStockTip: true,
	disabledInput: false,
})
const emits = defineEmits<{
	(e: 'update:value', value: number): void;
	(e: 'onStep', value: number, count: number): void;
}>()
const instance = getCurrentInstance()
const tempValue = ref<number | string>(props.value)
const handleChange = (type: StepType) => {
	let tempVal = Number(tempValue.value)
	switch (type) {
		case 'add':
			if (props.isUseStockTip && tempVal >= props.maxStock) {
				message.createMessageWarning(`库存不足`)
				return
			}
			if (tempVal >= props.maxSize) {
				message.createMessageWarning(`不能超出购买上限！`)
				return
			}
			tempVal += props.step
			emits('onStep', props.step, tempVal)
			break;
		case 'decrease':
			if (tempVal <= props.minSize) {
				message.createMessageWarning(`低于最低购买件数！`)
				return
			}
			tempVal -= props.step
			emits('onStep', -props.step, tempVal)
			break;
		default:
			break;
	}
	tempValue.value = tempVal
	emits('update:value', tempVal)
}
const handleInput = (e: Event) => {
	const target = e.target as HTMLInputElement
	const val = target.value.replace(/[^\d]/ig, '')
	tempValue.value = val
	instance.proxy.$forceUpdate()
}
const handleBlur = () => {
	let val = Number(tempValue.value)
	if (props.isUseStockTip && val >= props.maxStock) {
		val = props.maxStock
		tempValue.value = val
		return emits('update:value', val)
	}
	if (val > props.maxSize) {
		val = props.maxSize
		tempValue.value = val
	}
	
	if (val < props.minSize) {
		val = props.minSize
		tempValue.value = val
	}
	emits('update:value', val)
}
watch(() => props.value, (val) => {
	tempValue.value = val
})
</script>

<style scoped lang="less">
@import '@/styles/storeVar.less';
.number-box {
	display: flex;
	align-items: center;
	font-size: 14px;

	.pre-btn,
	.sub-btn {
		font-weight: bold;
		font-size: 20px;
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		box-sizing: border-box;
	}

	.pre-btn {
		border: 1px solid #EEEEEE;
		color: #D9D9D9;
	}

	.content {
		font-size: 14px;

		input {
			border: none;
			text-align: center;
		    width: 30px;
		}
	}

	.sub-btn {
		background-color: @error-color;
		color: #fff;
	}
}
</style>
