<template>
    <div class="category-box">
        <view class="search-box">
            <div @click="handleSearch">
                <van-search shape="round" readonly placeholder="请输入搜索关键词" />
            </div>
        </view>
        <div class="content-box">
            <div class="sidebar">
                <SideBar v-model:value="modal.cateId" :list="cateList">
                </SideBar>
            </div>
            <div class="content-detail">
                <van-list finished-text="" :loading="goodsLoading"
                    @load="loadGoodsData" :finished="finished">
                    <div class="goods-list" v-if="goodsList.length">
                        <GoodsCard class="card-item" v-for="item in goodsList" @click="jumpDetail(item)" :key="item.id"
                            :cardInfo='item' :type="StoreGoodsEnum.WelfareTicket" @chooseSku='chooseSku'>
                        </GoodsCard>
                    </div>
                    <van-empty description="暂无商品" :image="goodsEmpty" v-else />
                </van-list>
            </div>
        </div>
    </div>
    <welfareModal v-model:show="showWelfareModal" :state="skuState" :safeBottom='true' />
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted } from "vue"
import { useGoodData, useCateData } from "./hooks"
import SideBar from "../components/SideBar.vue";
import GoodsCard from "../components/GoodsCard.vue";
import goodsEmpty from "@/assets/storeImage/product/goodsEmpty.png";
import { useRouter, useRoute } from "vue-router"
import { RoutesName } from "@/enums/routes";
import welfareModal from "../components/welfareModal/index.vue";
import { GoodsTypeEnum, StoreGoodsEnum } from "@/enums/storeGoods";
const router = useRouter()
const route = useRoute()
const initParams: any = {
    type: null,
    cateId: '',
}
const modal = ref<{
    type: any,
    cateId: string,
}>({ ...initParams })
const { goodsList, loadGoodsData, goodsLoading, reloadGoodsData, finished } = useGoodData({
    isUseToken: true,
    modal,
    isShowLoading: true,
})
const { cateList, getCateList } = useCateData({
    isAll: true,
	modal,
	isShowLoading: true
})
const showWelfareModal = ref<boolean>(false)
const skuState = ref<any>({})
//购买
const chooseSku = (info) => {
    //获取当前商品
    skuState.value = JSON.parse(JSON.stringify(info))
    showWelfareModal.value = true
}
const jumpDetail = (info: any) => {
    router.push({
        name: RoutesName.StoreDetail,
        query: {
            ...route.query,
            id: info.id || info.productId,
            type: StoreGoodsEnum.WelfareTicket
        }
    })
}
const handleSearch = () => {
    router.push({
        name: RoutesName.StoreSearch,
        query: {
            ...route.query,
            type: StoreGoodsEnum.WelfareTicket
        }
    })
}
watch(()=>modal.value.cateId,()=>{
    console.log(modal.value.cateId);
    reloadGoodsData()
})
onMounted(() => {
    getCateList(()=>{
        reloadGoodsData()
    })
})
</script>

<style scoped lang="less">
:deep(.van-cell){
	background: none !important;
	padding: 0 !important;
}
.category-box {
    box-sizing: border-box;
    height: calc(100vh - env(safe-area-inset-bottom));
    display: flex;
    flex-direction: column;

    .header-search-box {
        width: 100vw;
        box-sizing: border-box;

        .search-box {
            width: 100%;
            display: flex;
            align-items: center;

            .search-input {
                flex: 1;
                margin-left: 5px;
                height: 100%;
                border-radius: 22.5px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                padding: 10px;
                background-color: #F8F8F8;

                img {
                    width: 15px;
                    height: 15px;
                    margin-right: 5px;
                }

                .input-warp {
                    color: #999;
                    font-size: 14px;
                    flex: 1;
                }
            }
        }
    }

    .content-box {
        flex: 1;
        overflow: auto;
        display: flex;

        .sidebar {
            height: 100%;
        }

        .content-detail {
            overflow: auto;
            width: calc(100vw - 112px);
            flex: 1;
            background-color: #fff;
            box-sizing: border-box;
            padding: 12px 12px 0 12px;
            display: flex;
            flex-direction: column;

            .detail-title {
                margin-bottom: 10px;
                color: #666666;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }

            .card-list {
                overflow: auto;
                flex: 1;
            }
        }
    }
}

:deep(.card-box .van-image__img) {
    border-radius: 8rpx;
}
</style>