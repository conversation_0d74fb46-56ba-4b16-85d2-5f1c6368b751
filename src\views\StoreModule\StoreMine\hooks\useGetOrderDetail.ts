import { ref } from "vue";
import { showToast } from 'vant';
import { useMessages } from "@/hooks/useMessage";
import { getOrderDetailByOrderCode } from "@/services/storeApi";

export default function useGetOrderDetail(_params: { orderCode: string }) {
  const isPageLoadingRef = ref(false);
  const orderCodeRef = ref(_params.orderCode);
  const { createMessageSuccess, createMessageError } = useMessages();
  /** 订单详情 */
  const orderDetailRef = ref<any>({});

  /** 根据订单编号获取订单详情 */
  async function getOrderDetail() {
    try {
      isPageLoadingRef.value = true;
      const _params = {
        orderCode: orderCodeRef.value,
      }
      const resp = await getOrderDetailByOrderCode(_params);
      if (resp) {
        orderDetailRef.value = resp;
      }
      isPageLoadingRef.value = false;
    } catch (error) {
      if (error && error.includes("不存在此订单")) {
        showToast("无核销权限");
      } else {
        createMessageError("获取订单详情失败：" + error);
      }
      isPageLoadingRef.value = false;
    }
  }

  return {
    isPageLoadingRef,
    getOrderDetail,
    orderDetailRef,
  };
}
