<template>
    <van-popup v-model:show="_show" position="center" :style="_customStyle" teleport="body" :overlay="false" @close="onClose" lock-scroll>
        <view class="preview-box" @click.stop="onClose">
            <view class="close-icon" @close.stop="onClose">
                <van-icon name="clear" color="#636363" size="40px" />
            </view>
            <van-swipe class="swiper-warp" ref="swiperRef" :show-indicators="false" :loop="false" @change="onChange" :current="currentIndex" :indicator-dots="false"
                :autoplay="false">
                <van-swipe-item v-for="(item, index) in previewList" :key="index">
                    <view class="swiper-item">
                        <MediaVideo ref="videoRef" v-if="item.type == MediaTypeEnum.Video" :src="item.path" />
                        <van-image v-else fit="contain" width="100%" height="100%" :src="item.path" />
                    </view>
                </van-swipe-item>
            </van-swipe>
        </view>
    </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, watch, nextTick, type StyleValue } from "vue";
import MediaVideo from "./components/MediaVideo/index.vue";
import type { SwipeInstance } from 'vant';
import { isArray, isNullOrUnDef } from "@/utils/isUtils";
import { MediaTypeEnum } from "@/enums/storeGoods";
type MediaType = 0 | 1
interface Preview {
    type: MediaType;
    path: string;
}
interface SwiperReactive {
    currentIndex: number;
    type: MediaType;
}
interface VideoPlayStatus {
    currentTime: number;
}
const props = withDefaults(defineProps<{
    currentIndex: number;
    videoPlayStatus: VideoPlayStatus;
    type: MediaType;
    previewList: Preview[];
    show: boolean;
    customStyle?: StyleValue;
}>(), {
    previewList: () => ([]),
    videoPlayStatus:()=>({
        currentTime:0
    }),
    type:MediaTypeEnum.Image,
    currentIndex: 0,
    show: false,
    customStyle: ''
})
const emits = defineEmits<{
    (e: 'update:show', val: boolean): void;
    (e: 'update:currentIndex', index: number): void;
    (e: 'update:type', type: MediaType): void;
    (e: 'closePreview', params: { currentTime?: number }): void;
}>()
const swiperRef = ref<SwipeInstance>(null)
const _customStyle = computed<StyleValue>(() => {
    return [
        props.customStyle,
        {
            maxWidth:'100vw',
        }
    ]
})
const videoRef = ref(null)
const videoIndexMap = ref<Record<number | string,number>>({
    0: 0
})
const swiperReactive = reactive<SwiperReactive>({
    currentIndex: 0,
    type: MediaTypeEnum.Image
})
const _show = computed({
    get: () => props.show,
    set: (val) => emits('update:show', val)
})
const curVideoIndex = computed(() => {
    return videoIndexMap.value[swiperReactive.currentIndex]
})
const onChange = (index:number) => {
    const item = props.previewList[index]
    swiperReactive.currentIndex = index
    swiperReactive.type = item.type
    if (isNullOrUnDef(curVideoIndex.value)) {
        pauseAllVideo()
    }
}
const pauseAllVideo = () => {
    if (videoRef.value && isArray(videoRef.value)) {
        videoRef.value.forEach((item: any) => {
            item.pause()
        })
    }
}
const onClose = () => {
    _show.value = false
    const params: any = {}
    if (swiperReactive.type == MediaTypeEnum.Video) {
        //停止播放
        pauseAllVideo()
        params.currentTime = videoRef.value ? videoRef.value[curVideoIndex.value].getCurrentTime() : 0
    }
    emits('update:type', swiperReactive.type)
    emits('update:currentIndex', swiperReactive.currentIndex)
    emits('closePreview', params)
}
const playVideo = (time: number) => {
    videoRef.value && videoRef.value[curVideoIndex.value].seek(time)
    videoRef.value && videoRef.value[curVideoIndex.value].play()
}
watch(() => [props.currentIndex, props.type], (val) => {
    swiperRef.value && swiperRef.value.swipeTo(props.currentIndex)
    swiperReactive.currentIndex = props.currentIndex
    swiperReactive.type = props.type
})
watch(() => props.show, async (val) => {
    if (val && props.type == MediaTypeEnum.Video) {
        await nextTick()
        if (props.videoPlayStatus && props.videoPlayStatus.currentTime) {
            playVideo(props.videoPlayStatus.currentTime)
        } else {
            videoRef.value && videoRef.value[curVideoIndex.value].init()
        }
    }
}, {
    flush: 'post'
})
watch(() => props.previewList, (val) => {
    //设置视频映射索引
    let i = 0
    val.forEach((item, index) => {
        if (item.type == MediaTypeEnum.Video) {
            videoIndexMap.value[index] = i
            i++
        }
    })
}, {
    deep: true
})
</script>

<style scoped lang="less">
:deep(.van-popup--center){
    max-width: 100vw !important;
    width: 100vw !important;
}
.preview-box {
    width: 100vw;
    height: 100vh;

    background-color: #000;
    display: flex;
    align-items: center;

    .close-icon {
        position: absolute;
        left: 10px;
        top: 30px;
    }

    .swiper-warp {
        height: 50vh;
        width: 100%;

        .swiper-item {
            height: 100%;
            width: 100%;
        }
    }
}
</style>