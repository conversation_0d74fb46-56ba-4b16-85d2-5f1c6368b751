import axios from "axios";
import { isFunction } from "@/utils/isUtils.js";
import type { AxiosRequestConfig, Canceler } from "axios";
const CancelToken = axios.CancelToken;
let pendingMap = new Map<string, Canceler>();

export function getPendingKey(config) {
  const { method, url, param, data } = config;
  return [method, url, JSON.stringify(param), JSON.stringify(data)].join("&");
}

export class AxiosCanceler {
  constructor() {}
  addPending(config: AxiosRequestConfig) {
    this.checkPending(config);
    const key = getPendingKey(config);
    config.cancelToken =
      config.cancelToken ||
      new CancelToken(cancel => {
        //创建cancel函数
        if (!pendingMap.has(key)) {
          pendingMap.set(key, cancel);
        }
      });
  }

  removeAllPending() {
    pendingMap.forEach(cancel => {
      cancel && isFunction(cancel) && cancel();
    });
    pendingMap.clear();
  }

  clearPending(config: AxiosRequestConfig) {
    const key = getPendingKey(config);
    key && pendingMap.delete(key);
  }

  checkPending(config: AxiosRequestConfig) {
    const key = getPendingKey(config);
    if (key) {
      const cancel = pendingMap.get(key);
      cancel && isFunction(cancel) && cancel(key);
      pendingMap.delete(key);
    }
  }
  reset() {
    pendingMap = new Map<string, Canceler>();
  }
}
