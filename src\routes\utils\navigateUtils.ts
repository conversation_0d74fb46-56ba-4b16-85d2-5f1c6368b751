import { isFunction, isObject } from '@/utils/isUtils'
import type { NavigateBackParams, NavigateDefaultParams, NavigateToParams } from '../types'
import { getPageUrlByRouteName } from './routeUtils'

/**
 * @description 根据传入的 URL 和 props 生成完整的 URL
 * @param {NavigateDefaultParams} params - 包含 URL 和 props 的参数
 * @returns {string} 生成的完整 URL
 */
function urlChangedByProps({ url, props }: NavigateDefaultParams) {
  let _url = getPageUrlByRouteName(url) // 获取基础 URL
  if (isObject(props) && Object.keys(props).length) {
    // 如果 props 存在且不为空，构建查询参数
    const queryParams = Object.entries(props)
      .map(([key, value]) => `${key}=${value}`) // 将每个键值对转换为查询字符串
      .join('&') // 使用 & 连接所有查询参数
    _url = `${_url}?${queryParams}` // 拼接完整 URL
  }
  return _url
}

/**
 * @description 跳转到指定页面
 * @param {NavigateToParams} params - 跳转参数
 * @returns {Promise} 跳转结果
 */
export function navigateTo(params: NavigateToParams) {
  return uni.navigateTo({
    ...params,
    url: urlChangedByProps(params), // 生成完整 URL
  })
}

/**
 * @description 重定向到指定页面
 * @param {NavigateDefaultParams} params - 重定向参数
 * @returns {Promise} 重定向结果
 */
export function redirectTo(params: NavigateDefaultParams) {
  return uni.redirectTo({
    ...params,
    url: urlChangedByProps(params), // 生成完整 URL
  })
}

/**
 * @description 重新启动应用并跳转到指定页面
 * @param {NavigateDefaultParams} params - 启动参数
 * @returns {Promise} 启动结果
 */
export function reLaunch(params: NavigateDefaultParams) {
  return uni.reLaunch({
    ...params,
    url: getPageUrlByRouteName(params.url), // 获取基础 URL
  })
}

/**
 * @description 切换到指定标签页
 * @param {Omit<NavigateDefaultParams, 'props'>} params - 切换参数
 * @returns {Promise} 切换结果
 */
export function switchTab(params: Omit<NavigateDefaultParams, 'props'>) {
  return uni.switchTab({
    ...params,
    url: urlChangedByProps(params), // 生成完整 URL
  })
}

/**
 * @description 返回到上一个页面
 * @param {NavigateBackParams} params - 返回参数
 * @returns {Promise} 返回结果
 */
export function navigateBack(params: NavigateBackParams = {}) {
  const index = getCurrentPages().length // 获取当前页面栈的长度
  if (index === 1 && !isFunction(params.fail)) {
    // 如果只有一个页面且没有 fail 回调，返回失败
    return Promise.reject('navigateBack fail')
  } else {
    return uni.navigateBack({
      ...params, // 返回参数
    })
  }
}
