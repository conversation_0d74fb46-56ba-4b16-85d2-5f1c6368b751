module.exports = {
  "env": {
      "browser": true,
      "es2021": true,
      "node": true
  },
  "extends": [
      "eslint:recommended",
      "plugin:vue/vue3-essential",
  ],
  "overrides": [
  ],
  "parser": "vue-eslint-parser",
  "parserOptions": {
      "ecmaVersion": "latest",
      "parser": "@typescript-eslint/parser",
      "sourceType": "module",
      "ecmaFeatures": { 
          "jsx": true,
          "tsx": true,
      },
  },
  "plugins": [
      "vue",
      "@typescript-eslint"
  ],
  "rules": {
      "no-unused-vars": "off",
      "no-prototype-builtins": "off",
      "no-empty": "off",
      "no-empty-pattern": "off",
      "no-redeclare":"off", //禁止多次声明同一变量
      "vue/no-unused-vars": "off",
      "vue/multi-word-component-names": "off",
      "vue/no-setup-props-destructure": "off",
      "vue/no-deprecated-v-on-native-modifier": "off",
  }
}

