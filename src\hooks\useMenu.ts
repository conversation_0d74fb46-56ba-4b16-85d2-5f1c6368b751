import { ref, watch } from "vue";
import { useRouter } from "vue-router";
import { isArray } from "@/utils/isUtils";
import { useActiveRoute } from "@/hooks/useActiveRoute";
import { RoutesName } from "@/enums/routes";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { getRoutesMapByConfig } from "@/utils/routerUtils";
import { routesMap } from "@/router/maps";
import { isStoreMode } from "@/utils/envUtils";

export type MenuOption = {
  id:string;
  title: string;
  icon: {
    active:string,
    inactive:string
  };
  path: string;
  show:boolean;
  parentKey?:string | null,
  children?:Array<MenuOption>,
  level?:number
}

const activeMenuKeyRef = ref<string | null>(null);
const menuListRef = ref<Array<MenuOption>>([]);



export function useMenu(props: undefined | { isMainMenu: boolean } = { isMainMenu: false }) {
  const { activeRouteName } = useActiveRoute();
  const userStore = useUserStoreWithoutSetup();
  const router = useRouter();
 

  function createMenuByRoutesConfig(routesConfig, parentsKey?: string): Array<MenuOption> {
    const _menu: Array<MenuOption> = [];
    routesConfig.forEach(route => {
      if (route.meta.isMenu || route.name === (isStoreMode()?RoutesName.StoreIndex:RoutesName.Root)) {
        const _menuItem: MenuOption = {
          id: route.name,
          path: route.name,
          title:route.meta.title || '',
          show: true,
          level: route.meta.level || 1,
          icon: {
            active: route.meta?.icon?.active || "",
            inactive: route.meta?.icon?.inactive || "",
          },
          
        };
        if(parentsKey){
          _menuItem.parentKey=parentsKey
        }
        if (route.meta.hasOwnProperty("isShow")) {
          _menuItem.show = route.meta.isShow;
        }
        if (isArray(route.children) && route.children.length) {
          if (parentsKey === RoutesName.Root) {
            createMenuByRoutesConfig(route.children, route.name);
          } 
          else {
            _menuItem.children = createMenuByRoutesConfig(route.children, route.name);
          }
        } 
        if (!route.meta.isMenu && isArray(_menuItem.children) && _menuItem.children.length) {
          _menuItem.children.forEach(item => _menu.push(item));
        }
        else _menu.push(_menuItem);
      }
    });
    return _menu;
  }
  watch(
    userStore.routeConfig,
    (newVal, oldVal) => {
      const _routes = getRoutesMapByConfig(newVal, routesMap);
      menuListRef.value = createMenuByRoutesConfig(_routes);
    },
    {
      immediate: true,
    },
  );
  watch(activeRouteName,(newVal)=>{
    activeMenuKeyRef.value = newVal as string;
  },{
    immediate:true
  })
  watch(activeMenuKeyRef, newVal => {
    if (routesMap[newVal]?.meta.isShow !== false) {
      router.push(routesMap[newVal]);
    }
  });
  
  return {
    menuListRef,
    activeMenuKeyRef,
  };
}
