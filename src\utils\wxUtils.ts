import { isDevEnv } from "./envUtils";
import { getRedirectUrl } from "./http/urlUtils";
import { getRandomQWOauthDomain, getRandomWXOauthDomain, getWxAuthType } from "./commonUtils";
export const enum QWAuthTypeEnum{
    WX=1,
    QW

}

export type WXAuthConfig = {
    type:QWAuthTypeEnum.WX,
    appid:string,
    state:string
}
export type QWAuthConfig = {
    type:QWAuthTypeEnum.QW,
    appid:string,
    state:string,
    agentId:string
}

export function redirectToWebpageAuthorization(params:WXAuthConfig | QWAuthConfig,pathName?:string){
    let {appid,state,type} = params
    if(!appid) {
        return false
    }
    const redirect_uri =`${getRedirectUrl()}${isDevEnv()?'/getCode': pathName?pathName:window.location.pathname}`
    const _pathName = pathName?pathName:window.location.pathname
    if(_pathName.endsWith('/c')|| _pathName.endsWith('/signup/member')){
        state = `${state}_tsstr_${new Date().getTime()}`
    }
    let url:string;
    const scope = type == QWAuthTypeEnum.WX?"snsapi_userinfo":'snsapi_privateinfo'
    if(type == QWAuthTypeEnum.WX){
        const prefix = getRandomWXOauthDomain(appid)
        const authType = getWxAuthType()
        if(authType == '1'){
            const redirect_uri =`${prefix}${isDevEnv()?'/getCode': pathName?pathName:window.location.pathname}`
            url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(redirect_uri)}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
        }
        else{
            const proxyUrl = `${prefix}/${redirect_uri.replace('https://','').replace('http://','')}`
            url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(proxyUrl)}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
        }
     }
    else if(type == QWAuthTypeEnum.QW){
        const {agentId} = params as QWAuthConfig
        //授权中心地址
        const prefix = getRandomQWOauthDomain()
        const proxyUrl = `${prefix}/${redirect_uri.replace('https://','')}`
        url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(proxyUrl)}&response_type=code&scope=${scope}&agentid=${agentId}&state=${state}#wechat_redirect`
    }
    window.location.href = url
}
