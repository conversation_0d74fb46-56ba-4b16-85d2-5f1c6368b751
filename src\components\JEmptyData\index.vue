<template>
  <view class="wrapper">
    <image :src="EmptyDataSrc" alt="" class="icon" />
    <text class="title">暂无数据</text>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import EmptyDataSrc from '@/static/images/storeHome/EmptyData.png'

defineOptions({ name: 'EmptyData' })
</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 48rpx;
  box-sizing: border-box;
  .icon {
    width: 400rpx;
    height: 400rpx;
  }
  .title {
    font-family: 'Source Han Sans CN', sans-serif;
    font-weight: 400;
    font-size: 32rpx;
    color: #333333;
    line-height: 48rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}
</style>
