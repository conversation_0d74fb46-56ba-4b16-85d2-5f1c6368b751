import type { ResponseInterceptor, TAxiosInterceptors } from "./type";

const retryRequest: ResponseInterceptor = {
  onRejected(error, options, axios) {
    let config = error.config;
    if (!config || !config.isRetry || config.retryTimes > 1) {
      return Promise.reject(error);
    }
    if (config.retryTimes === undefined) {
      config.retryTimes = 0;
    } else {
      config.retryTimes++;
    }
    config.ignoreCancelToken = true;
    const delay = new Promise(resolve => {
      setTimeout(() => {
        return resolve();
      }, 500);
    });
    console.log(`retry times:${config.retryTimes}`);
    return delay.then(() => {
      return axios(config);
    });
  },
};

export const defaultInterceptors: TAxiosInterceptors = {
  request: [],
  response: [retryRequest],
};
