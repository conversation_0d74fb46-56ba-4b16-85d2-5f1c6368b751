<template>
  <van-popup
    v-model:show="showRef"
    round
    closeable
    close-icon="close"
    position="bottom"
    @close="closeFn"
    style="height: 80%;"
  >
    <div class="store_address_wrapper">
      <!-- 标题 -->
      <div class="store_address_title">选择所在地区</div>

      <!-- 内容 -->
      <div class="store_address_content">
        <!-- 步骤条 -->
        <div class="store_address_steps">
          <VanSteps
            v-show="stepsShow"
            :steps="steps"
            :active="activeStepIndex"
            active-icon="circle"
            direction="vertical"
            active-color="#4DA4FF"
            @click-step="handleStepClick"
          >
            <VanStep v-for="(step, index) in steps" :key="index">
              {{ step.text }}
            </VanStep>
          </VanSteps>
        </div>

        <!-- 选择地址 -->
        <div class="store_address_select_content">
          <div class="store_address_select_address">
            <div class="store_address_select_address_title">选择地址</div>
            <div class="store_address_select_item" v-for="(classPy, index) in addressJson" :key="index">
              <div class="store_address_select_flex">
                <div class="store_address_select_py">{{ classPy.letter }}</div>
                <div class="store_address_select_radio_group">
                  <div
                    class="store_address_select_radio"
                    v-for="(item, itemIndex) in classPy.items"
                    :key="itemIndex"
                    @click="handleAddressClick(item)"
                    :class="{ active: item.id === activeId }"
                  >
                    {{ item.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- footer -->
      <div class="store_address_footer">
        <VanRow justify="space-between" gutter="8">
          <VanCol span="12">
            <VanButton round type="default" @click="resetFn" style="width: 100%;height: 40px;">重置</VanButton>
          </VanCol>
          <VanCol span="12">
            <VanButton
              type="danger"
              @click="confirmAddress"
              :disabled="disabled"
              round
              block
              style="width: 100%;height: 40px;"
            >
              确定
            </VanButton>
          </VanCol>
        </VanRow>
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts" setup>
import { ref, computed, reactive, watch } from "vue";
import { groupByPinyinInitial } from "@/views/StoreModule/utils/address";
import { useMessages } from "@/hooks/useMessage";
import { getAddress } from '@/services/storeApi';

interface AddressItem {
  id?: string;
  name?: string;
  code?: string;
  cateType?: number;
  pinYin?: string;
}

interface StepItem {
  text: string;
  code: string;
}

interface AddressForm {
  name: string;
  mobile: string;
  companyId: string;
  company: string;
  provinceId: string;
  province: string;
  provinceCode?: string;
  cityId: string;
  cityName: string;
  cityCode?: string;
  areaId: string;
  area: string;
  areaCode?: string;
  townId: string;
  town: string;
  townCode?: string;
  address: string;
  isDefault: number;
  id: string;
}

// 常量定义
const MAX_STEPS = 4;
const STEP_TEXTS = ['选择省份', '选择城市', '选择区县', '选择街道'];

defineOptions({ name: 'JStoreAddress' });

// Props
const props = defineProps<{
  show: boolean;
}>();

// Emits
const emits = defineEmits<{
  (e: 'update:show', show: boolean): void
  (e: 'confirm', addressData: {
    province: string;
    provinceId: string;
    provinceCode: string;
    city: string;
    cityId: string;
    cityCode: string;
    area: string;
    areaId: string;
    areaCode: string;
    town: string;
    townId: string;
    townCode: string;
  }): void
}>();

// 响应式数据
const showRef = computed({
  get() {
    return props.show;
  },
  set(show: boolean) {
    emits('update:show', show);
  }
});

const message = useMessages();
const stepsShow = ref(true);
const activeId = ref("0");
const addressJson = ref<Array<{letter: string; items: AddressItem[]}>>([]);

// 表单数据
const form = reactive<AddressForm>({
  "name": "",
  "mobile": "",
  "companyId": "",
  "company": "",
  "provinceId": "",
  "province": "",
  "cityId": "",
  "cityName": "",
  "areaId": "",
  "area": "",
  "townId": "",
  "town": "",
  "address": "",
  "isDefault": 1,
  "id": ""
});

// 步骤数据
const steps = ref<StepItem[]>([
  { text: STEP_TEXTS[0], code: '' }
]);

// 计算属性
const activeStepIndex = computed(() => Math.min(steps.value.length - 1, MAX_STEPS - 1));
const disabled = computed(() => !steps.value[2]?.code);

/**
 * 获取地址数据
 * @param parentCode 父级区域编码
 * @param cateType 地址类型 (1:省, 2:市, 3:区县, 4:街道)
 */
const getAddressData = async (parentCode: string, cateType: number) => {
  try {
    addressJson.value = [];
    const res = await getAddress({ parentCode, cateType });
    if (res) {
      addressJson.value = groupByPinyinInitial(res);
      activeId.value = "0"; // 重置选中状态
    }
  } catch (err) {
    message.createMessageError('获取级联地址失败' + err);
  }
};

/** 地址项点击处理 */
/** 地址项点击处理 */
const handleAddressClick = (item: AddressItem) => {
  activeId.value = item.id!;
  const currentStep = steps.value.length;

  if (currentStep === MAX_STEPS) {
    updateStepText(3, item.name!, item.code!);
    return;
  }

  updateStepText(currentStep - 1, item.name!, item.code!);

  // 新增层级保护：仅当 cateType < 4 时才允许请求下一级
  if (currentStep < MAX_STEPS && item.cateType! < 4) {
    steps.value.push({
      text: STEP_TEXTS[currentStep],
      code: ""
    });
    getAddressData(item.code!, item.cateType! + 1);
  }
};

/** 更新步骤文本 */
const updateStepText = (index: number, text: string, code: string) => {
  stepsShow.value = false;
  steps.value[index].text = text;
  steps.value[index].code = code;
  stepsShow.value = true;
};

/** 步骤条点击事件 */
const handleStepClick = (index: number) => {
  // 裁剪步骤数组到点击的位置
  steps.value = steps.value.slice(0, index + 1);

  // 获取父级编码（如果是第一步则使用"0"）
  const parentCode = index > 0 ? steps.value[index - 1].code : "0";

  // 重新加载该级地址数据（cateType = index + 1）
  getAddressData(parentCode, index + 1);
};

/** 重置表单 */
const resetFn = () => {
  steps.value = [{ text: STEP_TEXTS[0], code: '' }];
  getAddressData("0", 1);
};

/** 确认地址 */
function confirmAddress() {
  // 至少选择省、市、区 3 级
  if (steps.value.length < 3) {
    message.createMessageError('请至少选择省、市、区');
    return;
  }

  // 构造地址数据，街道可选，修正“选择街道”情况
  const townStep = steps.value[3];
  const town = (townStep && townStep.text && townStep.text !== STEP_TEXTS[3]) ? townStep.text : '';
  const townId = (townStep && townStep.code) ? townStep.code : '';
  const townCode = (townStep && townStep.code) ? townStep.code : '';

  const addressData = {
    province: steps.value[0].text,
    provinceId: steps.value[0].code,
    provinceCode: steps.value[0].code,
    city: steps.value[1].text,
    cityId: steps.value[1].code,
    cityCode: steps.value[1].code,
    area: steps.value[2].text,
    areaId: steps.value[2].code,
    areaCode: steps.value[2].code,
    town,
    townId,
    townCode,
  };

  emits('confirm', addressData);
  closeFn();
};

/** 关闭弹窗 */
const closeFn = () => {
  emits('update:show', false);
};

/** 监听显示状态变化 */
watch(() => props.show, (show) => {
  if (show) {
    resetFn();
  }
});
</script>

<style lang="less" scoped>
.store_address_wrapper {
  width: 100%;
  height: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .store_address_title {
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    padding: 12px 12px 0px 12px;
  }

  .store_address_content {
    flex: 1;
    overflow-y: auto;

    .store_address_steps {
      border-bottom: 1px solid #EEEEEE;
      padding-bottom: 10px;
      min-height: 50px;
      margin-bottom: 6px;

      :deep(.van-step--vertical:after) {
        border-bottom-width: 0px;
      }

      :deep(.van-step__title) {
        color: black !important;

        &::after {
          content: "";
          display: block;
          width: 10px;
          height: 10px;
          background: url("@/assets/image/address/rightArrow.png") no-repeat center center;
          background-size: 150% 150%;
          position: absolute;
          right: 10px;
          top: calc(50% - 5px);
        }
      }
    }

    .store_address_select_content {
       padding-left: 12px;
      .store_address_select_address {
        .store_address_select_address_title {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #999999;
          line-height: 22px;
          text-align: left;
          margin-bottom: 12px;
        }

        .store_address_select_item {
          font-size: 12px;

          .store_address_select_flex {
            display: flex;

            .store_address_select_py {
              width: 25px;
              text-align: center;
              line-height: 30px;
              font-size: 12px;
              color: #666666;
            }

            .store_address_select_radio_group {
              width: 100%;

              .store_address_select_radio {
                line-height: 30px;
                cursor: pointer;

                &.active {
                  color: var(--primary-color);
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
  }

  .store_address_footer {
    width: 100%;
    margin-top: 10px;
    padding: 4px 8px;
    box-sizing: border-box;

    :deep(.van-button__text) {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }
  }
}
</style>
