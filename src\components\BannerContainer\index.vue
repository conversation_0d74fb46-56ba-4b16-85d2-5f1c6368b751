<template>
  <view class="w-full p-16rpx box-border rounded-24rpx" :style="_contentStyle">
    <!-- 头部 -->
    <view
      v-if="props.isShowTitle"
      class="flex justify-between p-x-16rpx p-y-24rpx"
      :style="{
        borderBottom: props.isShowHeaderBorder ? `2rpx solid #EEEEEE` : `none`,
        ...props.headerStyle,
      }"
    >
      <!-- 标题 -->
      <view class="flex items-center">
        <!-- icon -->
        <slot v-if="props.isShowIcon" name="icon">
          <view
            class="w-6rpx h-32rpx rounded-20rpx mr-10rpx"
            :style="{ backgroundColor: props.lineColor }"
          ></view>
        </slot>
        <view class="text-32rpx font-bold">{{ props.title }}</view>
      </view>
      <!-- 更多 -->
      <view
        v-if="props.isShowMore"
        @click="emits('clickMore')"
        class="text-24rpx text-#666666 flex items-center"
      >
        {{ props.moreText }}
        <!-- <image class="w-24rpx h-24rpx" :src="rightIcon"></image> -->
      </view>
    </view>
    <!-- 内容 -->
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
/** 静态资源 */

defineOptions({ name: 'BannerContainer' })

/** props */
const props = withDefaults(
  defineProps<{
    title?: string
    moreText?: string
    isShowTitle?: boolean
    isShowIcon?: boolean
    isShowMore?: boolean
    lineColor?: string // 线的颜色
    bgColor?: string // 背景颜色
    contentStyle?: Object // 卡片内容区域的样式
    isShowHeaderBorder?: boolean
    headerStyle?: Object // 头部样式
  }>(),
  {
    title: '标题',
    moreText: '查看更多',
    isShowTitle: true,
    isShowIcon: true,
    isShowMore: false,
    lineColor: '#4DA4FF',
    bgColor: '#FFFFFF',
    contentStyle: undefined,
    isShowHeaderBorder: false,
    headerStyle: () => ({}),
  },
)

/** emits */
const emits = defineEmits<{
  (e: 'clickMore'): void
}>()

/** 卡片内容区域的样式 */
const _contentStyle = computed(() => {
  return {
    background: props.bgColor,
    ...props.contentStyle,
  }
})
</script>

<style lang="scss" scoped></style>
