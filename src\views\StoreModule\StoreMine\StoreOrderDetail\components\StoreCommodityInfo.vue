<template>
  <div class="store_commodity_info">
    <div class="store_commodity_info_title">商品信息</div>
    <!-- 订单信息 -->
    <div class="pending-orders-info">
      <img :src="firstOrderItem?.productImgPath ? firstOrderItem?.productImgPath : CouponSrc" alt="" />
      <div class="pending-orders-info-text">
        <div class="pending-orders-info-title">
          <p class="van-multi-ellipsis--l2">{{ firstOrderItem?.productFrontName }}</p>
          <div class="pending-orders-info-right">
            <!-- 普通订单 -->
            <span v-if="StoreOrderTypeEnum.NORMAL == orderInfoRef?.type" class="price">{{ `¥ ${Number((firstOrderItem?.price ?? 0) / 100).toFixed(2)}` }}</span>
            <!-- 积分订单 -->
            <span v-if="StoreOrderTypeEnum.INTEGRAL == orderInfoRef?.type" class="price">
              {{firstOrderItem.exchangePoints || 0 }}{{firstOrderItem.exchangePrice ? '积分+￥' + (firstOrderItem.exchangePrice / 100) : '积分'}}
            </span>
            <!-- 福利券订单 -->
            <span v-if="StoreOrderTypeEnum.COUPON == orderInfoRef?.type" class="price">{{ `${firstOrderItem?.exchangeCount}张${firstOrderItem?.couponName}` }}</span>
            <!-- 数量 -->
            <span class="count">{{`x ${firstOrderItem?.count ?? 0}`}}</span>
          </div>
        </div>
        <!-- 规格 -->
        <div class="specification">{{ firstOrderItem?.specName }}</div>
        <!-- 订单金额 -->
        <div class="order-amount">{{ `实付款￥${Number((orderInfoRef?.money ?? 0) / 100).toFixed(2)}` }}</div>
      </div>
    </div>
    <!-- 核销码状态 -->
    <div
      v-if="orderInfoRef?.orderVerificationDTO?.status == StoreOrderDetailVerifyStatusEnum.VERIFIED"
      class="verification_code_status_wrapper"
      @click="handleClickVerificationCodeStatus"
    >
      <!-- 核销码状态 -->
      <div class="verification_code_status">已使用</div>
      <div class="verification_code_status_right">
        <span>查看详情</span>
        <img :src="pullDownSrc" alt="" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
import { StoreOrderTypeEnum, StoreOrderStatusEnum, StoreOrderDetailVerifyStatusEnum, ProductPickupModeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import CouponSrc from "@/assets/storeImage/storeHome/coupon.png";
import pullDownSrc from "@/assets/storeImage/storeHome/pull-down.png";

defineOptions({ name: 'StoreCommodityInfo' });

interface OrderVerificationDTO {
    id: string;
    code: string; // 核销码
    status: StoreOrderDetailVerifyStatusEnum; // 核销状态
    verificationTime?: string; // 核销时间
    storeId?: string; // 店铺id
}

/** props */
const props = defineProps<{
    orderInfo: {
        type?: StoreOrderTypeEnum;
        status?: StoreOrderStatusEnum;
        code?: string;
        pickupType?: ProductPickupModeEnum;
        money?: number;
        goodsAmount?: number;
        payStatus?: number;
        afterSaleState?: number;
        orderItemDTOList?: Array<{
            type?: 1 | 2 | 3;
            orderId?: string;
            productImgPath?: string;
            productFrontName?: string;
            specName?: string;
            price?: number;
            count?: number;
            exchangePoints?: number;
            exchangePrice?: number;
            couponCateId?: string;
            couponName?: string;
            exchangeCount?: number;
        }>;
        // 订单核销记录
        orderVerificationDTO?: OrderVerificationDTO;
    };
}>();

const { orderInfo: orderInfoRef } = toRefs(props);
/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
    return orderInfoRef.value?.orderItemDTOList?.[0];
});

/** emit */
const emit = defineEmits<{
    /** 点击查看详情 */
    (event: 'clickVerificationCodeStatus'): void;
}>();

/** 点击查看详情 */
function handleClickVerificationCodeStatus() {
    emit('clickVerificationCodeStatus');
}
</script>

<style lang="less" scoped>
.store_commodity_info {
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 10px 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px;
    .store_commodity_info_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .pending-orders-info {
        display: flex;
        gap: 8px;
        img {
            width: 64px;
            height: 64px;
            border-radius: 8px;
        }
        .pending-orders-info-text {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            .pending-orders-info-title {
                display: flex;
                gap: 12px;
                p {
                    flex: 1;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    font-size: 16px;
                    color: #333333;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    line-height: 22px;
                }
                .pending-orders-info-right {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    .price {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 600;
                        font-size: 14px;
                        color: #333333;
                        text-align: right;
                        font-style: normal;
                        text-transform: none;
                    }
                    .count {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 13px;
                        color: #333333;
                        text-align: right;
                        font-style: normal;
                        text-transform: none;
                    }
                }
            }
            .specification {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .order-amount {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                line-height: 24px;
                text-align: right;
                font-style: normal;
                text-transform: none;
            }
        }
    }
    .verification_code_status_wrapper {
        height: 28px;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        border-top: none;
        background-image: linear-gradient(to right, #DEDCDA 50%, transparent 50%);
        background-size: 10px 1px;
        background-repeat: repeat-x;
        background-position: top;
        .verification_code_status {
            height: 20px;
            background: #4BE092;
            border-radius: 4px;
            padding: 2px 10px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #FFFFFF;
            line-height: 16px;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
        .verification_code_status_right {
            display: flex;
            align-items: center;
            gap: 2px;
            span {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 13px;
                color: #999999;
                line-height: 20px;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
            img {
                width: 14px;
                height: 14px;
                transform: rotate(-90deg);
            }
        }
    }
}
</style>
