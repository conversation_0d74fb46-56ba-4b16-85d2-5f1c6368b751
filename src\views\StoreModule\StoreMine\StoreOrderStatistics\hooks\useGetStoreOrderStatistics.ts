import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import dayjs from "dayjs";
import { DataRangeValue, OrderSortTypeEnum, OrderStatisticsTypeEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { useUserRole } from "@/views/StoreModule/hooks";
import { getOrderListStat } from "@/services/storeApi";

export default function useGetStoreOrderStatistics() {
  const scope = effectScope();
  const { createMessageSuccess, createMessageError } = useMessages();
  const { storeId, staffId } = useUserRole();

  /** 时间筛选 */
  const dataRangeList = [
    {
      label: "今日",
      value: DataRangeValue.TODAY,
    },
    {
      label: "近七天",
      value: DataRangeValue.SEVEN_DAYS,
    },
    {
      label: "近30天",
      value: DataRangeValue.THIRTY_DAYS,
    },
    {
      label: "近90天",
      value: DataRangeValue.NINETY_DAYS,
    },
  ];

  const isPageLoadingRef = ref(false);
  /** 订单数据 */
  const ordersList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);

  /** 筛选数据 */
  const initParams = {
    dataRangeRef: DataRangeValue.TODAY,
    timeRef: [dayjs().startOf("day").format("YYYY-MM-DD"), dayjs().endOf("day").format("YYYY-MM-DD")] as [
      string,
      string,
    ],

    type: OrderSortTypeEnum.AMOUNT,
    orderType: OrderStatisticsTypeEnum.ALL,
    storeId: storeId.value,
    staffShortId: null,
    memberShortId: null,
    productName: null
  };
  const model = ref({ ...initParams });

  /** 分页 */
  const pageVO = reactive({
    size: 30,
    current: 1,
    total: 0,
  });

  /** 获取时间 */
  function getDateRange(value: DataRangeValue): [string, string] {
    const today = dayjs().endOf("day");
    let startTime;

    switch (value) {
      case DataRangeValue.TODAY:
        startTime = today.startOf("day");
        break;
      case DataRangeValue.SEVEN_DAYS:
        startTime = today.subtract(6, "day").startOf("day");
        break;
      case DataRangeValue.THIRTY_DAYS:
        startTime = today.subtract(29, "day").startOf("day");
        break;
      case DataRangeValue.NINETY_DAYS:
        startTime = today.subtract(89, "day").startOf("day");
        break;
      default:
        startTime = today.startOf("day");
    }

    return [startTime.format("YYYY-MM-DD"), today.format("YYYY-MM-DD")];
  }

  /**
   * 判断时间区间属于哪个范围
   */
  function classifyTimeRange(start: string, end: string): DataRangeValue | null {
    const presetRanges = [
      DataRangeValue.TODAY,
      DataRangeValue.SEVEN_DAYS,
      DataRangeValue.THIRTY_DAYS,
      DataRangeValue.NINETY_DAYS,
    ].map(value => ({
      value,
      range: getDateRange(value),
    }));

    let startTime = dayjs(start).format("YYYY-MM-DD");
    let endTime = dayjs(end).format("YYYY-MM-DD");

    for (const { value, range } of presetRanges) {
      const [presetStart, presetEnd] = range;
      if (startTime === presetStart && endTime === presetEnd) {
        return value;
      }
    }
    return null;
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    const { timeRef, type, orderType, storeId, staffShortId, memberShortId, productName } = model.value;
    const [startTime, endTime] = timeRef;
    return {
      data: {
        startTime: dayjs(startTime).format("YYYY-MM-DD 00:00:00"),
        endTime: dayjs(endTime).format("YYYY-MM-DD 23:59:59"),
        type,
        orderType,
        storeId,
        staffShortId,
        memberShortId,
        productName
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取订单统计 */
  async function getStoreOrderStatistics() {
    const { current, size } = pageVO;

    try {
      const _params = getSearchParams();

      const { records, total } = await getOrderListStat(_params);
      if (!records?.length) return;

      current === 1 ? (ordersList.value = records) : ordersList.value.push(...records);

      const nextCurrent = current + 1;
      const remaining = Number(total) - nextCurrent * size;
      Object.assign(pageVO, {
        current: nextCurrent,
        total: Number(total),
      });

      isFinishedRef.value = remaining <= 0;
    } catch (error) {
      console.log("获取订单统计失败：" + error);
      createMessageError(`获取订单统计失败：${error}`);
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
    }
  }

  /** 加载分页数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getStoreOrderStatistics();
    }
  }

  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
    ordersList.value = [];
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    getStoreOrderStatistics();
  }

  /** 订单统计数据初始化 */
  async function initStoreOrderStatistics() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreOrderStatistics();
    isPageLoadingRef.value = false;
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    /** 监听 */
    watch(
      () => model.value.dataRangeRef,
      newVal => {
        if (newVal) {
          model.value.timeRef = getDateRange(newVal);
        }
      },
      {
        immediate: true,
      },
    );

    /** 监听时间范围变化尝试匹配预设值 */
    watch(
      () => model.value.timeRef,
      newVal => {
        if (newVal) {
          const [start, end] = newVal;
          const matchedPreset = classifyTimeRange(start, end);
          model.value.dataRangeRef = matchedPreset;

          initStoreOrderStatistics();
        }
      },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    model,
    dataRangeList,
    isPageLoadingRef,
    ordersList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onLoad,
    onRefresh,
    getStoreOrderStatistics,
    initStoreOrderStatistics,
  };
}
