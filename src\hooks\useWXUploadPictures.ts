import { getJSSDKConfig } from "@/services/storeApi";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { CacheConfig } from "@/utils/cache/config";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { isDevEnv } from "@/utils/envUtils";
import { isIOSEnv } from "@/utils/isUtils";
import { wxSdkInit } from "@/utils/wxSDKUtils";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { MessageEventEnum, useWindowMessage } from "./useWindowMessage";

const { initWindowMessage, sendMessageToWindows, listenQWSdkEvent } = useWindowMessage()

const isQWEnv = ref(false)
const isInitRef = ref(false)

export function useWXUploadPictures() {

    function getWxPictures() {
        return new Promise(async (resolve, reject) => {
            if (!isInitRef.value && !isQWEnv.value) {
                reject('JSSDK Not Ready')
                return
            }
            if (isQWEnv.value) {
                try {
                    await listenQWSdkEvent({
                        type: MessageEventEnum.ChooseImage,
                        success: async (localIds: Array<string>) => {
                            try {
                                if (localIds[0]) {
                                    await listenQWSdkEvent({
                                        type: MessageEventEnum.UploadImage,
                                        params: localIds[0],
                                        success: (serverId: string) => {
                                            resolve(serverId)
                                        },
                                        fail: (err) => {
                                            reject(err)
                                        }
                                    },2000)
                                }
                            }
                            catch (e) {
                                reject(e)
                            }
                        }
                    },90000)
                }
                catch (e) {
                    reject(e)
                }
            }
            else {
                window.wx.chooseImage({
                    count: 1,
                    sizeType: ['original', 'compressed'],
                    sourceType: ['album', 'camera'],
                    success: function (res) {
                        if (res.localIds && res.localIds[0]) {
                            uploadWxImage(res.localIds[0]).then(severId => {
                                resolve(severId)
                            }).catch(e => {
                                reject(e)
                            })
                        }
                        else {
                            reject()
                        }

                    },
                    fail: function (err) {
                        reject(err)
                    }
                })
            }
        })
    }

    function uploadWxImage(localId: string) {
        return new Promise((resolve, reject) => {
            if (isQWEnv.value) {
                reject(true)
                return
            }
            if (!isInitRef.value) {
                reject('JSSDK Not Ready')
                return
            }
            window.wx.uploadImage({
                localId: localId, // 需要上传的图片的本地ID，由chooseImage接口获得
                isShowProgressTips: 1, // 默认为1，显示进度提示
                success: function (res) {
                    resolve(res.serverId)
                },
                fail: function (err) {
                    reject(err)
                }
            })
        })
    }

    function initUploadPictureWXSdk() {
        isInitRef.value = false
        const stateCache = createCacheStorage(CacheConfig.State);
        const _stateInfo = stateCache.get();
        if (_stateInfo.corpId && _stateInfo.agentId) {
            isQWEnv.value = true
        }
        else {
            const route = useRoute()
            const systemStore = useSystemStoreWithoutSetup()
            // const _url = `${window.location.origin}${window.location.pathname}${window.location.search}`
            const _url = isIOSEnv() ? systemStore.entryUrl : `${window.location.origin}${route.fullPath}`
            getJSSDKConfig(_url).then(res => {
                const { signature, nonceStr, timestamp } = res;
                const stateCache = createCacheStorage(CacheConfig.State);
                let _cacheAppId = stateCache.get("appId");
                wxSdkInit({
                    debug: isDevEnv() ? false : false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                    appId: _cacheAppId as string, // 必填，公众号的唯一标识
                    timestamp, // 必填，生成签名的时间戳
                    nonceStr, // 必填，生成签名的随机串
                    signature,// 必填，签名
                    jsApiList: ["chooseImage", "previewImage", 'uploadImage', 'getLocalImgData'] // 必填，需要使用的JS接口列表
                }).then(() => {
                    isInitRef.value = true

                }).catch(err => {
                    console.log('Init WeChat JSSDK error:', err);
                })
            })
                .catch(err => {
                    console.log('getJSSDKConfig:', err);
                })
        }
    }
    return {
        getWxPictures,
        initUploadPictureWXSdk
    }
}