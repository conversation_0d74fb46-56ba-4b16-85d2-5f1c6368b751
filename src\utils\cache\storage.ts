import dayjs from 'dayjs'
import { isObject, isNullOrUnDef, isArray } from '@/utils/isUtils'
import { CacheKeysEnum } from '@/enum'
import { decrypt, encrypt } from '@/utils/crypto'
import { isDevEnv } from '@/utils/envUtils'
import type { CacheParams } from './type'

// type CacheMapType = Map<Cache_Key, WebStorage>

const ssMap = new Map()
/**
 * @description 定义了一个 WebStorage 类，用于操作 Storage
 */
class WebStorage {
  private hasEncrypt: boolean
  private KEY: string
  private expire: null | number

  constructor({
    hasEncrypt = true, // 是否加密
    key,
    expire,
  }: CacheParams) {
    this.hasEncrypt = isDevEnv() ? false : hasEncrypt
    this.KEY = key
    this.expire = expire || null
  }

  // 获取存储在 storage 中的值
  get(key?: string | number) {
    // 获取存储的值
    const val = uni.getStorageSync(this.KEY)
    // 如果值不存在，返回 null
    if (!val) return null
    // 解密存储的值
    const desVal = this.hasEncrypt ? decrypt(val) : val
    // 将解密后的值转换为对象
    const data = this.hasEncrypt ? desVal : JSON.parse(desVal)
    // 获取 value 和 expire 属性
    const { value, expire } = data
    // 如果 expire 不存在或者大于等于当前时间，返回 value
    if (isNullOrUnDef(expire) || expire >= dayjs().valueOf()) {
      // 如果 value 是对象并且 key 存在，返回 value[`cacheKey-${key}`]
      if (isObject(value) && !isNullOrUnDef(key)) return value[`${key}`]
      // 否则返回 value
      else return value
    } else {
      // 如果 expire 存在且小于当前时间，移除存储的值
      this.remove()
      return null
    }
  }

  set(value: string | number | object | [] | null, objectKey?: string | number) {
    let cacheValue
    // 如果 objectKey 存在，将 value 存储到对象中
    if (!isNullOrUnDef(objectKey)) {
      const tempCache = isObject(this.get()) ? this.get() : {}
      tempCache[`${objectKey}`] = value
      cacheValue = tempCache
    } else {
      cacheValue = value
    }
    // 将 value 和 expire 存储到对象中
    const data = JSON.stringify({
      value: cacheValue,
      expire: !isNullOrUnDef(this.expire) ? dayjs().valueOf() + this.expire * 1000 : null,
    })
    // 加密存储的值
    const encData = this.hasEncrypt ? encrypt(data) : data
    // 将加密后的值存储到 storage 中
    uni.setStorageSync(this.KEY, encData)
  }

  // 从 storage 中移除值
  remove() {
    uni.removeStorageSync(this.KEY)
  }
}

/**
 * @description 根据传入的 Cache 配置创建 WebStorage 实例
 */
export function createCacheStorage(cacheConfig: CacheParams): WebStorage {
  const { key, expire = null, hasEncrypt = true } = cacheConfig
  // 如果 key 不存在，抛出错误
  if (!key) throw new Error('please enter the Key')
  // 如果 ssMap 中存在 key，直接返回对应的 WebStorage 实例
  if (ssMap.get(key)) return ssMap.get(key)
  else {
    const newLs = new WebStorage({
      hasEncrypt,
      key,
      expire,
    })
    ssMap.set(key, newLs)
    return newLs
  }
}

/**
 * @description 清除缓存函数
 */
function clearStorageByType(exceptList: Array<CacheKeysEnum>) {
  if (isArray(exceptList) && exceptList.length) {
    ssMap.forEach((WebStorage, key) => {
      if (!exceptList.includes(key)) {
        WebStorage.remove()
      }
    })
  } else {
    uni.clearStorageSync()
    ssMap.clear()
  }
}

/**
 * @description 清空 Storage 中的所有值
 */
export const clearAllStorage = (exceptList: Array<CacheKeysEnum> = []) => {
  clearStorageByType(exceptList)
}
