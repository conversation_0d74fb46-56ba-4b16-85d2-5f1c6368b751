<template>
  <VanPullRefresh
    v-model="isRefresh"
    @refresh="onRefresh"
    :disabled="isPullRefreshDisabled"
  >
    <JLoadingWrapper :show="isPageLoadingRef">
      <div class="store-home-wrapper" @scroll="handleScroll">
        <!-- 轮播图 -->
        <div class="store-home-slideshow-container">
          <StoreHomeSlideshow />
        </div>
        <!-- 商品列表 -->
        <div class="store-home-content">
          <div class="advertising-wrapper">
            <StoreAddress :storeInfo="storeInfo" />
            <!-- 二维码 -->
            <StoreScanQRCodes @onScan="handleScan" />
            <!-- 直播跳转 -->
            <StoreLiveStreaming v-if="liveRoomLink" :liveRoomLink="liveRoomLink" />
          </div>
          <!-- 最新商品 -->
          <StoreCommodity ref="storeCommodityRef" :storeId="storeId" />
        </div>
      </div>
      <!-- 扫码 -->
      <StoreQrCode v-model:show="StoreQrCodeRef" :qrCodeUrl="qrCodeUrl" :title="qrCodeTitle" />
      <!-- 地址填写 -->
      <StoreAddressForm v-model:show="has_filled_address" @close="setHasUnFilledAddress" />
    </JLoadingWrapper>
  </VanPullRefresh>
</template>

<script lang="ts" setup>
import { ref, onActivated, nextTick, onMounted } from "vue";
import { showToast } from 'vant';
import QRcode from "qrcode";
import { RoutesName } from "@/enums/routes";
import { useMessages } from "@/hooks/useMessage";
import useGetLiveRoomLink from "./hooks/useGetLiveRoomLink";
import useGetAddressStatus from "./hooks/useGetAddressStatus";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { QRCodeTypeEnum, StoreScanTypeEnum, StoreIntegralRouteTypeEnum, KeepAliveRouteNameEnum } from "@/views/StoreModule/enums";
import { useRouterUtils, useUserRole, useGetStoreInfo, useBoolean } from "@/views/StoreModule/hooks";
import { getQrCodeUserId, getUserUnionId } from "@/services/storeApi";
/** 相关组件 */
import StoreHomeSlideshow from "./components/StoreHomeSlideshow.vue";
import StoreAddress from "@/views/StoreModule/components/StoreAddress.vue";
import StoreScanQRCodes from "./components/StoreScanQRCodes.vue";
import StoreLiveStreaming from "./components/StoreLiveStreaming.vue";
import StoreQrCode from "@/views/StoreModule/components/StoreQrCode.vue";
import StoreCommodity from "./components/StoreCommodity/index.vue";
import StoreAddressForm from "./StoreAddressForm/index.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

defineOptions({ name: KeepAliveRouteNameEnum.HOME });

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom, _findIndex } = useKeepAliveRoute();
const storeCommodityRef = ref<InstanceType<typeof StoreCommodity> | null>(null);
const StoreQrCodeRef = ref(false);
const qrCodeUrl = ref("");
const { routerPushByRouteName } = useRouterUtils();
const { storeId } = useUserRole();
const { createMessageError } = useMessages();
const { storeInfo, getStoreInfoByStoreId } = useGetStoreInfo();
const { liveRoomLink, getLiveRoomLinkData } = useGetLiveRoomLink();
const { bool: isPageLoadingRef, setTrue: setPageLoading, setFalse: unSetPageLoading } = useBoolean();
const { bool: isRefresh, setTrue: refresh, setFalse: unRefresh } = useBoolean();
const isPullRefreshDisabled = ref(false); // 是否禁用下拉刷新
/** 核销码标题 */
const qrCodeTitle = ref("核销码");
const { has_filled_address, setHasFilledAddress, setHasUnFilledAddress, getAddressStatus } = useGetAddressStatus();

/** 扫码处理 */
async function handleScan(type: QRCodeTypeEnum) {
  try {
    let url = "";
    let userId = "";
    let unionId = "";
    let prefixUrl = window.location.origin;
    // 根据类型生成二维码
    switch (type) {
      // 提货码
      case QRCodeTypeEnum.PICK_UP_CODE:
        qrCodeTitle.value = "提货码";
        userId = await getQrCodeUserId();
        url = `${prefixUrl}/st/pendingOrders?userId=${userId}&scanType=${StoreScanTypeEnum.PICKUP}`;

        if (!userId) {
          throw new Error("获取用户ID失败");
        }
        break;
      // 时长码
      case QRCodeTypeEnum.TIME_CODE:
        qrCodeTitle.value = "时长码";
        userId = await getQrCodeUserId();

        if (!userId) {
          throw new Error("获取用户ID失败");
        }
        url = `${prefixUrl}/st/watchTime?userId=${userId}&scanType=${StoreScanTypeEnum.TIME}`;
        break;
      // 福利券码
      case QRCodeTypeEnum.COUPON_CODE:
        qrCodeTitle.value = "福利券码";
        userId = await getQrCodeUserId();

        if (!userId) {
          throw new Error("获取用户ID失败");
        }
        url = `${prefixUrl}/st/couponReceiveRecord/getByCouponUseId?userId=${userId}&scanType=${StoreScanTypeEnum.COUPON}`;
        break;
      // 积分码
      case QRCodeTypeEnum.INTEGRAL_CODE:
        qrCodeTitle.value = "积分码";
        userId = await getQrCodeUserId();
        unionId = await getUserUnionId();
        if (!userId) {
          throw new Error("获取用户ID失败");
        }
        if (!unionId) {
          throw new Error("获取用户UnionId失败");
        }
        url = `${prefixUrl}/store_integral?userId=${userId}&unionId=${unionId}&scanType=${StoreScanTypeEnum.INTEGRAL}&type=${StoreIntegralRouteTypeEnum.SCAN_INTEGRAL}`;
        break;
      default:
        throw new Error(`不支持的二维码类型: ${type}`);
    }

    // 生成二维码
    if (url) {
      generateQRCode(url);
    }

  } catch (error) {
    createMessageError(`二维码生成失败: ${error}`);
  }
}

/** 页面下拉刷新 */
async function onRefresh() {
  refresh();
  await onInit();
  showToast('刷新成功');
  unRefresh();
}

/** 生成二维码 */
function generateQRCode(url: string) {
  QRcode.toDataURL(url, { width: 156, height: 156, margin: 2 }).then((res) => {
    qrCodeUrl.value = res;
    StoreQrCodeRef.value = true;
  }).catch((err) => {
    createMessageError("生成二维码失败：" + err);
  });
}

/** 滚动加载 */
function handleScroll(event: Event) {
  scrollEventHandler(event, KeepAliveRouteNameEnum.HOME);

  const target = event.target as HTMLElement;
  const scrollTop = target.scrollTop;
  
  isPullRefreshDisabled.value = scrollTop > 10;

  if (storeCommodityRef.value?.isLoadingRef || storeCommodityRef.value?.isFinishedRef) return;

  const clientHeight = target.clientHeight;
  const scrollHeight = target.scrollHeight;
  
  if (scrollTop + clientHeight >= scrollHeight - 50) {
    storeCommodityRef.value?.onLoad();
  }
}

async function onInit() {
  try {
    /** 获取门店信息 */
    await getStoreInfoByStoreId(storeId.value);
    /** 获取门店商品信息 */
    storeCommodityRef.value && await storeCommodityRef.value?.initStoreCommodity();
    /** 获取直播间信息 */
    await getLiveRoomLinkData();
    /** 获取邀请填写收货地址的状态 */
    await getAddressStatus();
    /** 判断门店是否停用 */
    if (!storeInfo.value?.storeStatus) {
      routerPushByRouteName(RoutesName.StoreDefault);
      return;
    }
  } catch (error) {
    console.log("error", error);
  }
}

onMounted(async () => {
  setPageLoading();
  await onInit();
  unSetPageLoading();
});

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName('store-home-wrapper')[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.HOME);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.HOME);
  });
});
</script>

<style lang="less" scoped>
.store-home-wrapper {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  .store-home-slideshow-container {
    width: 100%;
  }
  .store-home-content {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    .advertising-wrapper {
      padding: 4px 12px;
    }
  }
}
</style>
