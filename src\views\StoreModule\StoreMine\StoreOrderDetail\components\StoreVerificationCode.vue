<template>
  <div class="store_verification_code">
    <!-- header -->
    <div class="store_verification_code_header">
      <!-- 核销码状态 -->
      <div class="store_verification_code_status">待使用</div>
      <div class="store_verification_code_info">
        <span class="title">{{`核销码：${orderInfoRef?.orderVerificationDTO?.code ?? '-'}`}}</span>
        <span class="btn" @click="copyVerificationCode(orderInfoRef?.orderVerificationDTO?.code ?? '')">复制</span>
      </div>
    </div>
    <div class="qrCode-container">
      <!-- 提示 -->
      <div v-if="isStoreVerification" class="tip">
        该订单需商品到店后才可提货，请耐心等待，并留意通知！
      </div>
      <VanImage width="120" height="120" fit="contain" :src="qrCodeRef" />
      <div class="title-wrapper">
        <span class="title">核销码</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch, computed } from "vue";
import { showToast } from "vant";
import QRcode from "qrcode";
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
import { 
    StoreOrderDetailVerifyStatusEnum, 
    StoreOrderDetailRouteTypeEnum, 
    StoreScanTypeEnum, 
    OrderVerificationTypeEnum 
} from "@/views/StoreModule/enums";
import { getMyOrderVerifyCode } from "@/services/storeApi";

defineOptions({ name: 'StoreVerificationCode' });

interface OrderVerificationDTO {
    id: string;
    code: string; // 核销码
    status: StoreOrderDetailVerifyStatusEnum; // 核销状态
    verificationTime?: string; // 核销时间
    storeId?: string; // 店铺id
}

/** props */
const props = defineProps<{
    orderInfo: {
        code?: string;
        // 订单核销记录
        orderVerificationDTO?: OrderVerificationDTO;
        verificationType?: OrderVerificationTypeEnum; // 核销类型
    };
}>();

const { orderInfo: orderInfoRef } = toRefs(props);

const { createMessageError } = useMessages();
const qrCodeRef = ref('');

/** 商品是否下单门店到货后核销 */
const isStoreVerification = computed(() => {
    return orderInfoRef.value?.verificationType == OrderVerificationTypeEnum.STORE_VERIFICATION;
});

/** 复制核销码 */
function copyVerificationCode(id: string){
    try{
        copyText(id);
        showToast('复制核销码成功');
    }
    catch(e){
        showToast('复制核销码失败');
    }
}

/** 查询我的订单核销码 */
async function queryVerificationCode() {
    try {
        const resp = await getMyOrderVerifyCode({ orderCode: orderInfoRef.value?.code });
        if (resp) {
            let prefixUrl = window.location.origin;
            const qrcodeLink = `${prefixUrl}/verify?orderCode=${orderInfoRef.value?.code}&routeType=${StoreOrderDetailRouteTypeEnum.SCAN}&scanType=${StoreScanTypeEnum.ORDER}`;
            qrCodeRef.value = await generateQRCode(qrcodeLink);
        }
    } catch (e) {
        console.log("查询核销码失败：", e);
    }
}

/** 生成二维码 */
async function generateQRCode(url: string): Promise<string | null> {
    try {
      const qrCodeDataUrl = await QRcode.toDataURL(url, {
        width: 156,
        height: 156,
        margin: 2,
      });
      return qrCodeDataUrl;
    } catch (err) {
      createMessageError("生成二维码失败：" + err);
      return null;
    }
}

/** 组件挂载 */
watch(() => orderInfoRef.value, (newVal) => {
    if (newVal) {
        queryVerificationCode();
    }
}, { deep: true, immediate: true });
</script>

<style lang="less" scoped>
.store_verification_code {
    position: relative;
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 14px;
    /* 两侧半圆形缺口效果 */
    -webkit-mask:
        radial-gradient(circle at 0 38px, #0000 8px, red 0),
        radial-gradient(circle at right 38px, #0000 8px, red 0);
    -webkit-mask-size: 51%;
    -webkit-mask-position: 0, 100%;
    -webkit-mask-repeat: no-repeat;

    /* 横穿的虚线 */
    &::after {
        content: "";
        position: absolute;
        top: 38px;
        left: 8px; /* 避开左侧半圆 */
        right: 8px; /* 避开右侧半圆 */
        height: 1px;
        background: repeating-linear-gradient(to right, #ccc 0, #ccc 4px, transparent 4px, transparent 8px);
    }
    .store_verification_code_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .store_verification_code_status {
            height: 20px;
            background: #1677FF;
            border-radius: 4px;
            padding: 2px 10px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #FFFFFF;
            line-height: 16px;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
        .store_verification_code_info {
            display: flex;
            align-items: center;
            gap: 10px;
            .title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                color: #333333;
                text-align: right;
                font-style: normal;
                text-transform: none;
            }
            .btn {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                color: #1677FF;
                text-align: right;
                font-style: normal;
                text-transform: none;
            }
        }
    }
    .qrCode-container {
        flex: 1;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 8px;
        box-sizing: border-box;
        .tip {
            padding: 4px 8px;
            background: #FFF0EF;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #FF6864;
            line-height: 22px;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
        .title-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            .title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 22px;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>
