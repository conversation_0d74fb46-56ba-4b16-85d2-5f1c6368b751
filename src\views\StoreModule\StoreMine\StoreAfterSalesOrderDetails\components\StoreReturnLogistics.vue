<template>
  <div class="store_return_logistics_info">
    <div class="store_return_logistics_info_title">退货物流</div>
    <!-- 物流公司 -->
    <div class="store_return_logistics_info_item">
      <span class="store_return_logistics_info_item_label">物流公司</span>
      <span class="store_return_logistics_info_item_value">{{ afterSalesInfoRef?.shipCompanyName ?? '-' }}</span>
    </div>
    <!-- 物流单号 -->
    <div class="store_return_logistics_info_item">
      <span class="store_return_logistics_info_item_label">物流单号</span>
      <span class="store_return_logistics_info_item_value">{{ afterSalesInfoRef?.trackingNo ?? '-'}}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from "vue";

defineOptions({ name: 'StoreReturnLogistics' });

/** props */
const props = defineProps<{
  afterSalesInfo: {
    shipCompanyName?: string;
    trackingNo?: string;
  };
}>();
const { afterSalesInfo: afterSalesInfoRef } = toRefs(props);
</script>

<style lang="less" scoped>
.store_return_logistics_info {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    .store_return_logistics_info_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 16px;
    }
    .store_return_logistics_info_item {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;
        .store_return_logistics_info_item_label {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_return_logistics_info_item_value {
            max-width: 260px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        &:last-child {
            margin-bottom: 0px;
        }
    }
    .footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 12px;
      .count_down {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 13px;
        color: #FF6864;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
      .fill_logistics_number {
        width: 96px;
        min-height: 32px;
        height: 100%;
        border-radius: 999px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: #EF1115;
        color: #FFFFFF;
      }
    }
}
</style>
