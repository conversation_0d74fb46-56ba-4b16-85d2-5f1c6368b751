<template>
  <div class="store_order_info">
    <div class="store_order_info_title">售后信息</div>
    <!-- 售后类型 -->
    <div class="store_order_info_item">
      <span class="store_order_info_item_label">售后类型</span>
      <span class="store_order_info_item_value">{{afterSaleTypeMap[afterSalesInfoRef?.type]}}</span>
    </div>
    <!-- 申请原因 -->
    <div class="store_order_info_item">
      <span class="store_order_info_item_label">申请原因</span>
      <span class="store_order_info_item_value">{{ afterSaleReasonMap[afterSalesInfoRef?.reason] }}</span>
    </div>
    <!-- 具体描述 -->
    <div class="store_order_info_item">
      <span class="store_order_info_item_label">具体描述</span>
      <span class="store_order_info_item_value">{{ afterSalesInfoRef?.reasonDescription }}</span>
    </div>
    <!-- 相关图片 -->
    <div class="store_order_info_item">
      <span class="store_order_info_item_label">相关图片</span>
      <span class="store_order_info_item_value">
        <img v-for="item,index in imagePaths" :src="item.path" :key="item.id" alt="" @click="handlePreviewImage(index)" />
      </span>
    </div>
    <!-- 联系电话 -->
    <div class="store_order_info_item">
      <span class="store_order_info_item_label">联系电话</span>
      <span class="store_order_info_item_value">{{ afterSalesInfoRef?.phone ?? '-' }}</span>
    </div>
    <!-- 售后订单 -->
    <div class="store_order_info_item">
      <span class="store_order_info_item_label">售后订单</span>
      <span class="store_order_info_item_value">{{ afterSalesInfoRef?.orderCode }}</span>
    </div>
    <!-- 创建时间 -->
    <div class="store_order_info_item">
      <span class="store_order_info_item_label">创建时间</span>
      <span class="store_order_info_item_value">{{ afterSalesInfoRef?.createTime }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, computed } from "vue";
import { showImagePreview } from "vant";
import { StoreAfterSaleTypeEnum, AfterSaleReasonEnum } from "@/views/StoreModule/enums";

defineOptions({ name: 'StoreAfterSalesInfo' });

/** props */
const props = defineProps<{
    afterSalesInfo: {
        type?: StoreAfterSaleTypeEnum;
        refundAmount?: number;
        actualRefundAmount?: number;
        reason?: AfterSaleReasonEnum;
        reasonDescription?: string;
        afterSaleImgDTOList?: Array<{
            id?: string;
            path?: string;
        }>;
        orderCode?: string;
        phone?: string;
        createTime?: string;
        orderItemDTOList: {
            type?: 1 | 2 | 3;
            orderId?: string;
            productImgPath?: string;
            productFrontName?: string;
            specName?: string;
            price?: number;
            count?: number;
            exchangePoints?: number;
            exchangePrice?: number;
        }
    };
}>();

const { afterSalesInfo: afterSalesInfoRef } = toRefs(props);

/** 售后类型 */
const afterSaleTypeMap = {
    [StoreAfterSaleTypeEnum.REFUND_RETURN]: '退货退款',
    [StoreAfterSaleTypeEnum.REFUND]: '仅退款',
    [StoreAfterSaleTypeEnum.CANCEL_ORDER]: '仅取消订单',
};

/** 售后原因映射 */
const afterSaleReasonMap = {
    [AfterSaleReasonEnum.OTHER]: '其他',
    [AfterSaleReasonEnum.WRONG_OR_EXTRA_ORDER]: '拍错/多拍',
    [AfterSaleReasonEnum.NO_LONGER_WANTED]: '不想要了',
    [AfterSaleReasonEnum.NO_EXPRESS_INFO]: '无快递信息',
    [AfterSaleReasonEnum.EMPTY_PACKAGE]: '包裹为空',
    [AfterSaleReasonEnum.REJECTED_PACKAGE]: '已拒签包裹',
    [AfterSaleReasonEnum.DELIVERY_DELAYED]: '快递长时间未送达',
    [AfterSaleReasonEnum.DESCRIPTION_MISMATCH]: '与商品描述不符',
    [AfterSaleReasonEnum.QUALITY_ISSUE]: '质量问题',
    [AfterSaleReasonEnum.WRONG_ITEM_SENT]: '卖家发错货',
    [AfterSaleReasonEnum.NO_CERTIFICATION]: '三无产品',
    [AfterSaleReasonEnum.FAKE_PRODUCT]: '假冒产品',
};

const imagePaths = computed(() => {
  return props.afterSalesInfo?.afterSaleImgDTOList || [];
});

/** 预览图片 */
function handlePreviewImage(index: number) {
	showImagePreview({
		images: props.afterSalesInfo?.afterSaleImgDTOList.map((item) => item.path) || [],
		startPosition: index
	});
};
</script>

<style lang="less" scoped>
.store_order_info {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 10px 12px;
    box-sizing: border-box;
    margin-bottom: 12px;
    .store_order_info_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 16px;
    }
    .store_order_info_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        .store_order_info_item_label {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_order_info_item_value {
            max-width: 260px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: right;
            font-style: normal;
            text-transform: none;
            display: flex;
            align-items: center;
            gap: 8px;
            img {
                width: 56px;
                height: 56px;
                border-radius: 8px;
            }
        }
    }
}
</style>
