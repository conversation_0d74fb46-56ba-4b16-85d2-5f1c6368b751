import { ref, reactive, type Ref } from "vue";
import { queryIntergralList, getAvailPoints } from "@/services/storeApi/store-category";
import { useMessages } from "@/hooks/useMessage";
interface CateProps {
  spliceNum?: number;
  modal: Ref<any>;
  callback?: () => void;
}
export function useExchangeData(props: CateProps) {
  const { modal, spliceNum, callback } = props;
  const message = useMessages();
  //分页
  const pageVO = ref({
    size: 30,
    current: 1,
    total: 0,
  });
  const points = ref<number>(0);
  const exchangeLoadingRef = ref<boolean>(false);
  const isPullLoadingRef = ref<boolean>(false);
  const listFinishedRef = ref<boolean>(false);
  const exchangeList = ref([]);
  async function getList() {
    try {
      const params: any = {
        data: {
          minPoints: modal.value.minPoints,
          maxPoints: modal.value.maxPoints,
        },
        pageVO: {
          current: pageVO.value.current,
          size: pageVO.value.size,
        },
      };
      if (modal.value.frontName) {
        params.data.frontName = modal.value.frontName;
      }
      if (modal.value.cateId) {
        params.data.cateId = modal.value.cateId;
      }
      const { records, total } = await queryIntergralList(params);
      if (pageVO.value.current == 1) {
        exchangeList.value = records;
      } else {
        exchangeList.value.push(...records);
      }
      if (spliceNum && exchangeList.value.length > spliceNum) {
        exchangeList.value.splice(0, spliceNum);
      }
      pageVO.value.total = Number(total) || 0;
      //加载完成
      if (pageVO.value.current * pageVO.value.size >= pageVO.value.total) {
        listFinishedRef.value = true;
      }
      callback && callback();
    } catch (e) {
      message.createMessageError(`获取失败：${e}`);
    } finally {
      isPullLoadingRef.value = false;
      exchangeLoadingRef.value = false;
    }
  }
  const getPoints = async () => {
    try {
      const res = await getAvailPoints();
      points.value = res.points || 0;
      modal.value.maxPoints = points.value;
      reloadData();
    } catch (error) {
      message.createMessageError(`获取失败：${error}`);
    }
  };
  //加载分页数据
  const loadPageData = async () => {
    // 数据全部加载完成
    if (pageVO.value.current * pageVO.value.size < pageVO.value.total) {
      exchangeLoadingRef.value = true;
      pageVO.value.current++;
      getList();
    }
  };
  function onRefresh() {
    isPullLoadingRef.value = true;
    reloadData();
  }
  const reloadData = () => {
    exchangeList.value = [];
    listFinishedRef.value = false;
    pageVO.value.current = 1;
    pageVO.value.total = 1;
    getList();
  };
  return {
    exchangeList,
    exchangeLoadingRef,
    isPullLoadingRef,
    listFinishedRef,
    loadPageData,
    onRefresh,
    reloadData,
    points,
    getPoints,
  };
}
