import { ref, reactive } from "vue";
import { useMessages } from "@/hooks/useMessage";
import useBoolean from "./useBoolean";

interface PageResponse<T = any> {
  records: T[];
  total: number;
}

interface IUsePaginatedFetch<T = any> {
  /** 数据请求API */
  fetchApi: (params: {
    data: Record<string, string | number>;
    pageVO: { current: number; size: number };
  }) => Promise<PageResponse<T>>;

  /** 每页数据条数（默认30） */
  pageSize?: number;

  /** 初始搜索参数 */
  searchParams?: Record<string, string | number>;

  /**
   * 请求前的参数处理回调
   * @param params 当前请求参数
   * @returns 处理后的新参数（可以是异步）
   */
  beforeRequest?: (params: {
    data: Record<string, string | number>;
    pageVO: { current: number; size: number };
  }) => Promise<{
    data: Record<string, string | number>;
    pageVO: { current: number; size: number };
  }>;
}

/**
 * @description 分页数据获取
 */
export default function usePaginatedFetch({ fetchApi, pageSize = 30, searchParams = {}, beforeRequest }: IUsePaginatedFetch) {
  const { createMessageError } = useMessages();

  /** 分页数据列表 */
  const pageListRef = ref([]);
  /** 响应式搜索参数 */
  const searchParamsRef = ref({...searchParams});
  /** 页面Loading */
  const { bool: isPageLoadingRef, setTrue: setPageLoadingTrue, setFalse: setPageLoadingFalse } = useBoolean();
  /** 下拉刷新Loading */
  const { bool: refreshingRef, setTrue: setRefreshingTrue, setFalse: setRefreshingFalse } = useBoolean();
  /** 上拉加载Loading */
  const { bool: loadingRef, setTrue: setLoadingTrue, setFalse: setLoadingFalse } = useBoolean();
  /** 数据分页是否加载完 */
  const { bool: isFinishedRef, setTrue: setIsFinishedTrue, setFalse: setIsFinishedFalse } = useBoolean();
  /** 分页 */
  const pageVO = reactive({
    size: pageSize,
    current: 1,
    total: 0,
  });

  /** 获取处理后的搜索参数 */
  async function getProcessedParams() {
    const baseParams = {
      data: { ...searchParamsRef.value },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };

    // 执行回调处理参数（如果存在）
    return beforeRequest ? await beforeRequest(baseParams) : baseParams;
  }

  /** 初始化 */
  function initParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 上拉加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      setLoadingTrue();
      pageVO.current++;

      pageDataRequest();
    }
  }

  /** 下拉刷新 */
  function onRefresh() {
    initParams();
    // 重新加载数据
    setRefreshingTrue();

    initPageDataRequest();
  }

  /** 获取分页数据 */
  async function pageDataRequest() {
    const { current, size } = pageVO;

    try {
      const finalParams = await getProcessedParams();

      if (!fetchApi) {
        throw new Error("fetchApi is not defined");
      }

      const { records = [], total = 0 } = await fetchApi(finalParams);

      // 更新分页列表
      if (current === 1) {
        pageListRef.value = records;
      } else if (records.length) {
        pageListRef.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });

      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      initParams();
    } finally {
      setLoadingFalse();
      setRefreshingFalse();
    }
  }

  /** 数据初始化 */
  async function initPageDataRequest() {
    initParams();
    await pageDataRequest();
  }

  /** 重置 */
  function resetSearchParams(params: Record<string, string | number>) {
    searchParamsRef.value = params;
    onRefresh();
  }

  return {
    isPageLoadingRef,
    setPageLoadingFalse,
    setPageLoadingTrue,
    refreshingRef,
    setRefreshingTrue,
    setRefreshingFalse,
    loadingRef,
    setLoadingTrue,
    setLoadingFalse,
    isFinishedRef,
    setIsFinishedTrue,
    setIsFinishedFalse,
    searchParamsRef,
    pageListRef,
    pageVO,
    pageDataRequest,
    initPageDataRequest,
    onLoad,
    onRefresh,
    resetSearchParams,
  };
}
