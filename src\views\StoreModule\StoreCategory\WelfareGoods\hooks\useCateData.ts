import { ref, reactive, watch } from "vue";
import type { Ref } from "vue";
import { queryWelfareCate } from "@/services/storeApi/store-category";
import { isNullOrUnDef } from "@/utils/isUtils";
import { useMessages } from "@/hooks/useMessage";
interface CateProps {
  isAll?: boolean;
  spliceNum?: number;
  isFilterShow?: boolean;
  modal: Ref<any>;
  callback?: () => void;
  isShowLoading?: boolean;
}
export default function (props: CateProps) {
  const message = useMessages();
  const { modal, isAll, isFilterShow, spliceNum, callback, isShowLoading } = props;
  const cateList = ref<any[]>([]);
  const cateLoading = ref<boolean>(false);
  const getCateList = async (callback?: () => void) => {
    try {
      const params: any = {
        data:{}
      };
      cateLoading.value = true;
      let res = await queryWelfareCate(params);
      res = res.map((item: any) => {
        return {
          ...item,
          parentId: item.id,
        };
      });
      if (isFilterShow) {
        //扁平化数据
        let flatArr = res.reduce((pre, item, index) => {
          pre.push(item);
          if (item.children && item.children.length) {
            pre = [...pre, ...item.children];
          }
          return pre;
        }, []);
        flatArr.sort((a, b) => b.sort - a.sort);
        flatArr = flatArr.filter((item: any) => item.isShow == 1);
        if (spliceNum) {
          res = flatArr.splice(0, spliceNum);
        }
      }
      cateList.value = res;
      if (isAll) {
        cateList.value.unshift({
          id: "",
          name: "全部商品",
        });
      }
      callback && callback();
    } catch (e) {
      message.createMessageError(`获取失败：${e}`);
    } finally {
      cateLoading.value = false;
    }
  };
  return {
    cateList,
    getCateList,
  };
}
