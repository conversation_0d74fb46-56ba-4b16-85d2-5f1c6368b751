import { ref, reactive, computed, watch, effectScope, onScopeDispose } from "vue";
import { useRoute } from "vue-router";
import { showToast } from "vant";
import QRcode from "qrcode";
import dayjs from "dayjs";
import { wxSdkInit } from "@/utils/wxSDKUtils";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import {
  StoreUserTypeEnum,
  StoreDataRangeEnum,
  StoreOrderTypeEnum,
  StoreCouponRouteTypeEnum,
  StoreScanTypeEnum,
  StoreOrderDetailRouteTypeEnum,
  StorePendingOrderRouteTypeEnum,
  StoreIntegralRouteTypeEnum,
  OrgIdentityTypeEnum,
  StoreBusinessCodeEnum,
  StoreFunctionEnum,
  KeepAliveRouteNameEnum
} from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { useUserStore } from "@/stores/modules/user";
import { getJSSDKConfig } from "@/services/storeApi";
import { isIOSEnv, isObject } from "@/utils/isUtils";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { CacheConfig } from "@/utils/cache/config";
import { createCacheStorage } from "@/utils/cache/storageCache";
import useGetStoreUserInfo from "./useGetStoreUserInfo";
import {
  getStoreCsLink,
  getOrderDataOverview,
  isBusinessAudit,
  getPendingReviewAfterSaleCountApi,
} from "@/services/storeApi";
/** 相关静态资源 */
import iconCouponStats from "@/assets/storeImage/storeMine/icon-coupon-stats.png";
import iconFeedback from "@/assets/storeImage/storeMine/icon-feedback.png";
import iconInviteCode from "@/assets/storeImage/storeMine/icon-invite-code.png";
import iconMemberManagement from "@/assets/storeImage/storeMine/icon-member-management.png";
import iconOrderStats from "@/assets/storeImage/storeMine/icon-order-stats.png";
import iconPendingVerification from "@/assets/storeImage/storeMine/icon-pending-verification.png";
import iconRanking from "@/assets/storeImage/storeMine/icon-ranking.png";
import iconSalesAnalytics from "@/assets/storeImage/storeMine/icon-sales-analytics.png";
import iconScanVerify from "@/assets/storeImage/storeMine/icon-scan-verify.png";
import iconShopOrders from "@/assets/storeImage/storeMine/icon-shop-orders.png";
import iconStaffStats from "@/assets/storeImage/storeMine/icon-staff-stats.png";
import iconVerificationStats from "@/assets/storeImage/storeMine/icon-verification-stats.png";
import iconWatchTime from "@/assets/storeImage/storeMine/icon-watch-time.png";
import iconstoreManagement from "@/assets/storeImage/storeMine/store-management.png";
import iconDealer from "@/assets/storeImage/storeMine/dealer.png";
import iconrefundReview from "@/assets/storeImage/storeMine/refund-review.png";
import iconreturnOrder from "@/assets/storeImage/storeMine/returnOrder.png";
import storeDataReport from "@/assets/storeImage/storeMine/storeDataReport.png";
import storeLogistics from "@/assets/storeImage/storeMine/storeLogistics.png";

export default function useStoreManagement() {
  const { customerIdRef } = useGetStoreUserInfo();
  const scope = effectScope();
  const route = useRoute();
  const userStore = useUserStore();
  const { pushKeepAliveRoute } = useKeepAliveRoute();
  const systemStore = useSystemStoreWithoutSetup();
  const { createMessageError, createMessageWarning } = useMessages();
  const { routerPushByRouteName } = useRouterUtils();
  const isPageLoadingRef = ref(false);
  const isShowStoreMemberCodeRef = ref(false);
  const qrCodeUrlRef = ref("");
  /** 订单概览时间选择 */
  const storeSelectTimeRef = ref(StoreDataRangeEnum.TODAY);
  /** 订单类型 */
  const storeOrderTypeRef = ref(StoreOrderTypeEnum.NORMAL);

  /** 是否为店长 */
  const isStoreOwner = computed(() => userStore.storeUserInfo?.type === StoreUserTypeEnum.OWNER);

  /** 是否为店员 */
  const isStoreStaff = computed(() => userStore.storeUserInfo?.type === StoreUserTypeEnum.STAFF);

  /** 是否为会员 */
  const isStoreMember = computed(() => userStore.storeUserInfo?.type === StoreUserTypeEnum.CUSTOMER);

  /** 组织架构身份是否区域经理 */
  const isAreaManager = computed(
    () => userStore.storeUserInfo?.structIdentityType == OrgIdentityTypeEnum.REGIONAL_MANAGER,
  );

  /** 组织架构身份是否经销商 */
  const isDistributor = computed(() => userStore.storeUserInfo?.structIdentityType == OrgIdentityTypeEnum.DISTRIBUTOR);
  
  /** 组织架构身份是否门店 */
  const isStore = computed(() => userStore.storeUserInfo?.structIdentityType == OrgIdentityTypeEnum.STORE);
  
  /** 归属店铺ID */
  const storeId = computed(() => userStore.storeUserInfo?.storeId); 

  /** 店铺管理选项 */
  const storeManagementOptions: Array<{
    label: string;
    key: string;
    icon: string;
    value?: number;
    isShow?: boolean;
    onClick: () => void;
  }> = reactive([
    {
      label: "会员管理",
      key: "memberManagement",
      icon: iconMemberManagement,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.MemberManagement, { storeId: storeId.value, customerId: customerIdRef.value });
      },
    },
    {
      label: "排行榜",
      key: "ranking",
      icon: iconRanking,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreRanking);
      },
    },
    {
      label: "店铺订单",
      key: "shopOrders",
      icon: iconShopOrders,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreShopOrders);
      },
    },
    {
      label: "待核销订单",
      key: "pendingVerification",
      icon: iconPendingVerification,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StorePendingVerificationOrders, {
          type: StorePendingOrderRouteTypeEnum.MY_WAIT_VERIFY,
        });
      },
    },
    {
      label: "订单统计",
      key: "orderStats",
      icon: iconOrderStats,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreOrderStatistics);
      },
    },
    {
      label: "福利券统计",
      key: "couponStats",
      icon: iconCouponStats,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreWelfareVoucherStatistics);
      },
    },
    {
      label: "销售统计",
      key: "salesAnalytics",
      icon: iconSalesAnalytics,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreSalesStatistics);
      },
    },
    {
      label: "店员统计",
      key: "staffStats",
      icon: iconStaffStats,
      isShow: isStoreOwner.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreShopAssistantStatistics);
      },
    },
    {
      label: "核销统计",
      key: "verificationStats",
      icon: iconVerificationStats,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreVerificationStatistics);
      },
    },
    {
      label: "退款审核",
      key: StoreFunctionEnum.REFUND_AUDIT,
      icon: iconrefundReview,
      isShow: isStoreOwner.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreRefundAudit);
      },
    },
    {
      label: "观看时长",
      key: "watchTime",
      icon: iconWatchTime,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.ViewDuration);
      },
    },
    {
      label: "会员反馈",
      key: "feedback",
      icon: iconFeedback,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        showToast("敬请期待");
      },
    },
    {
      label: "会员邀请码",
      key: "inviteCode",
      icon: iconInviteCode,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: async () => {
        await generateStoreMemberCode();
      },
    },
    {
      label: "退货订单",
      key: "returnOrder",
      icon: iconreturnOrder,
      isShow: isStoreOwner.value,
      onClick: async () => {
        try {
          const resp = await isBusinessAudit({
            bizCode: StoreBusinessCodeEnum.STORE_RETURN_APPROVAL,
          });
          if (!resp) {
            showToast("此功能未启用");
            return;
          }
          pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
          routerPushByRouteName(RoutesName.StoreReturnOrder);
        } catch (error) {
          console.log("error", error);
        }
      },
    },
    {
      label: "扫码核销",
      key: "scanVerify",
      icon: iconScanVerify,
      isShow: isStoreOwner.value || isStoreStaff.value,
      onClick: async () => {
        const initJSSDKFlag = await initJSSDK();
        if (initJSSDKFlag) {
          window.wx.scanQRCode({
            needResult: 1,
            scanType: ["qrCode", "barCode"],
            success: function (res) {
              if (res.resultStr) {
                pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
                let _params: Record<string, string | number> = {};
                _params = getUrlParams(res.resultStr);
                if (isObject(_params) && _params?.scanType) {
                  // 扫首页提货码
                  if (_params?.scanType == StoreScanTypeEnum.PICKUP) {
                    routerPushByRouteName(RoutesName.StorePendingVerificationOrders, {
                      userId: _params.userId,
                      type: StorePendingOrderRouteTypeEnum.SCAN_WAIT_VERIFY,
                    });
                  }

                  // 扫首页时长码
                  if (_params?.scanType == StoreScanTypeEnum.TIME) {
                    routerPushByRouteName(RoutesName.StoreWatchTime, {
                      userId: _params.userId,
                    });
                  }

                  // 扫首页福利券码
                  if (_params?.scanType == StoreScanTypeEnum.COUPON) {
                    routerPushByRouteName(RoutesName.StoreCoupon, {
                      userId: _params.userId,
                      type: StoreCouponRouteTypeEnum.SCAN_COUPON,
                    });
                  }

                  // 扫首页积分码
                  if (_params?.scanType == StoreScanTypeEnum.INTEGRAL) {
                    routerPushByRouteName(RoutesName.StoreIntegral, {
                      userId: _params.userId,
                      unionId: _params.unionId,
                      type: StoreIntegralRouteTypeEnum.SCAN_INTEGRAL,
                    });
                  }

                  // 扫订单码
                  if (_params?.scanType == StoreScanTypeEnum.ORDER) {
                    routerPushByRouteName(RoutesName.StoreOrderDetail, {
                      routeType: _params?.routeType ?? StoreOrderDetailRouteTypeEnum.MY_ORDER,
                      orderCode: _params?.orderCode ?? "",
                    });
                  }
                }
              }
            },
          });
        }else {
          createMessageWarning("wxJSSDK初始化失败，请联系管理员");
        }
      },
    },
    {
      label: "门店物流",
      key: "logistics",
      icon: storeLogistics,
      isShow: true,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreLogistics);
      },
    },
    {
      label: "数据导出",
      key: "data-export",
      icon: storeDataReport,
      isShow: isStoreOwner.value,
      onClick: async () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreDataExport);
      },
    },
  ]);

  /** 区域管理 */
  const mareaManagementOptions: Array<{
    label: string;
    key: string;
    icon: string;
    isShow?: boolean;
    onClick: () => void;
  }> = [
    {
      label: "我的经销商",
      key: "dealer",
      icon: iconstoreManagement,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.MyDealer);
      },
    },
  ];

  /** 经销商管理 */
  const dealerManagementOptions: Array<{
    label: string;
    key: string;
    icon: string;
    isShow?: boolean;
    onClick: () => void;
  }> = [
    {
      label: "店铺管理",
      key: "store",
      icon: iconDealer,
      onClick: () => {
        pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
        routerPushByRouteName(RoutesName.StoreManagement);
      },
    },
  ];

  /** 订单概览 */
  const orderOverview = ref<
    Array<{
      label: string;
      key: string;
      value: number | string;
    }>
  >([
    {
      label: "销售订单数",
      key: "orderNum",
      value: 0,
    },
    {
      label: "销售商品数",
      key: "productNum",
      value: 0,
    },
    {
      label: "销售订单额",
      key: "orderAmount",
      value: 0,
    },
    {
      label: "会员人数",
      key: "csNum",
      value: 0,
    },
    {
      label: "退款订单数",
      key: "refundOrderNum",
      value: 0,
    },
    {
      label: "退款商品数",
      key: "refundProductNum",
      value: 0,
    },
    {
      label: "退款订单额",
      key: "refundOrderAmount",
      value: 0,
    },
    {
      label: "新增会员数",
      key: "incrCsNum",
      value: 0,
    },
  ]);

  /**
   * @description 生成门店客户邀请链接
   */
  async function generateStoreMemberCode() {
    try {
      const resp = await getStoreCsLink();
      if (resp) {
        const qrCodeLink = await generateQRCode(resp);
        if (qrCodeLink) {
          qrCodeUrlRef.value = qrCodeLink;
          isShowStoreMemberCodeRef.value = true;
        }
      }
    } catch (error) {
      createMessageError("生成门店客户邀请链接失败：" + error);
    }
  }

  /** 生成二维码 */
  async function generateQRCode(url: string): Promise<string | null> {
    try {
      const qrCodeDataUrl = await QRcode.toDataURL(url, {
        width: 156,
        height: 156,
        margin: 2,
      });
      return qrCodeDataUrl;
    } catch (err) {
      createMessageError("生成二维码失败：" + err);
      return null;
    }
  }

  /** 获取门店订单概览参数 */
  function getOrderOverviewParams() {
    let dateStart = null;
    let dateEnd = null;
    const now = dayjs();

    switch (storeSelectTimeRef.value) {
      case StoreDataRangeEnum.TODAY:
        dateStart = now.startOf("day").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.endOf("day").format("YYYY-MM-DD 23:59:59");
        break;
      case StoreDataRangeEnum.YESTERDAY:
        dateStart = now.subtract(1, "day").startOf("day").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.subtract(1, "day").endOf("day").format("YYYY-MM-DD 23:59:59");
        break;
      case StoreDataRangeEnum.THIS_MONTH:
        dateStart = now.startOf("month").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.endOf("month").format("YYYY-MM-DD 23:59:59");
        break;
      case StoreDataRangeEnum.ALL:
        dateStart = null;
        dateEnd = null;
        break;
      default:
        // 默认情况下使用当天
        dateStart = now.startOf("day").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.endOf("day").format("YYYY-MM-DD 23:59:59");
    }

    return {
      orderType: storeOrderTypeRef.value,
      dateStart,
      dateEnd,
    };
  }

  /** 获取订单概览 */
  async function getOrderOverview() {
    try {
      const _params = getOrderOverviewParams();
      const resp = await getOrderDataOverview(_params);

      if (resp) {
        // 遍历本地订单概览配置
        orderOverview.value = orderOverview.value.map(item => {
          const respValue = resp[item.key];

          // 处理金额字段
          if (item.key.includes("Amount")) {
            return {
              ...item,
              value: respValue ? formatCurrency(respValue) : 0,
            };
          } else {
            return {
              ...item,
              value: respValue ? formatNumber(respValue) : 0,
            };
          }
        });
      }

    } catch (error) {
      console.error("获取订单概览失败:", error);
      createMessageError("获取订单概览失败：" + error.message);
    }
  }

  /**
   * 分转元并格式化金额（保留两位小数）
   * @param {number|string} value - 分的金额
   * @return {string} 格式化后的元金额，保留两位小数
   */
  function formatCurrency(value) {
    const num = Number(value);
    return isNaN(num) ? 0 : (num / 100).toFixed(2);
  }

  /** 格式化普通数字（添加千位分隔符） */
  function formatNumber(value) {
    const num = Number(value);
    return isNaN(num) ? 0 : num.toLocaleString("en-US");
  }

  /** 初始化SDK */
  async function initJSSDK() {
    try {
      const _url = isIOSEnv() ? systemStore.entryUrl : `${window.location.origin}${route.fullPath}`;

      // 1. 获取JSSDK配置
      const { signature, nonceStr, timestamp } = await getJSSDKConfig(_url);

      // 2. 获取缓存数据
      const stateCache = createCacheStorage(CacheConfig.StoreToken);
      const _stateInfo = stateCache.get();
      if (!_stateInfo["wxappId"]) {
        throw new Error("Missing wxappId in cache");
      }

      // 3. 初始化SDK
      const JSSDK_Config_params = {
        debug: false,
        appId: _stateInfo["wxappId"],
        timestamp,
        nonceStr,
        signature,
        jsApiList: ["scanQRCode"],
      };

      await wxSdkInit(JSSDK_Config_params);
      return true;
    } catch (err) {
      console.error("InitJSSDK failed:", err);
      return false;
    }
  }

  /** 获取URL参数 */
  function getUrlParams(url: string) {
    const regex = /[?&]([^=#]+)=([^&#]*)/g;
    const params = {};
    let match;

    while ((match = regex.exec(url))) {
      params[match[1]] = match[2];
    }

    return params;
  }

  /**
   * @description 获取店铺待审核售后单数
   */
  async function getStoreAfterSaleOrderCount() {
    try {
      const resp = await getPendingReviewAfterSaleCountApi();
      if (resp) {
        storeManagementOptions.find(item => item.key == StoreFunctionEnum.REFUND_AUDIT).value = resp;
      }
    } catch (error) {
      console.log(`getStoreAfterSaleOrderCount error:` + error);
    }
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    watch(
      () => [storeSelectTimeRef.value, storeOrderTypeRef.value],
      val => {
        if (val) {
          getOrderOverview();
        }
      },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    storeManagementOptions,
    mareaManagementOptions,
    dealerManagementOptions,
    orderOverview,
    isShowStoreMemberCodeRef,
    storeSelectTimeRef,
    storeOrderTypeRef,
    qrCodeUrlRef,
    isStoreOwner,
    isStoreStaff,
    isStoreMember,
    isPageLoadingRef,
    isAreaManager,
    isDistributor,
    isStore,
    getOrderOverview,
    getStoreAfterSaleOrderCount,
  };
}
