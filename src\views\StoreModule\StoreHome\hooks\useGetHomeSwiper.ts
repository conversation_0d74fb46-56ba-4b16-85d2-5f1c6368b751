import { ref } from "vue";
import { RoutesName } from "@/enums/routes";
import { BannerTypeEnum } from "@/views/StoreModule/enums";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { getStoreCarouselImage } from "@/services/storeApi";

export default function useGetHomeSwiper() {
    /** 门店轮播图数据 */
    const swiperList = ref([]);
    const { routerPushByRouteName } = useRouterUtils();

    /** 获取轮播图数据 */
    async function getHomeSwiper() {
        try {
            const _params = {
                CurrentPosition: BannerTypeEnum.STORE_HOME 
            };
            const resp = await getStoreCarouselImage(_params);
            if (resp) {
                swiperList.value = resp;
            }
        } catch (error) {
            console.log(error);
        }
    }

    /** 点击轮播图 */
    function handleSwiperClick(item) {
        if (!item.redirect) return;
        routerPushByRouteName(RoutesName.StoreDetail, { id: item.productId, type: StoreGoodsEnum.Goods });
    }

    return {
        swiperList,
        getHomeSwiper,
        handleSwiperClick
    }
}
