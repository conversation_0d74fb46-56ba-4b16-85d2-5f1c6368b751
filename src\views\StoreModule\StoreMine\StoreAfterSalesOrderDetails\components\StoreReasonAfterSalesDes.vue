<template>
  <div class="store_reason_after_sales">
    <div class="store_reason_after_sales__title">{{ reasonDescription?.title }}</div>
    <p class="content">{{ reasonDescription?.content }}</p>
    <span class="update_time">{{ afterSalesInfoRef?.updateTime ?? `` }}</span>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs } from "vue";
import { AfterSaleStatusEnum } from "@/views/StoreModule/enums";

defineOptions({ name: 'StoreReasonAfterSalesDes' });

/** props */
const props = defineProps<{
    afterSalesInfo: {
        state?: AfterSaleStatusEnum;
        rejectionReasonDescription?: string;
        updateTime?: string;
    };
}>();

const { afterSalesInfo: afterSalesInfoRef } = toRefs(props);

/** 描述 */
const reasonDescriptionMap = computed(() => {
    const { state, rejectionReasonDescription } = afterSalesInfoRef?.value;
    return {
        [AfterSaleStatusEnum.MERCHANT_REFUSED]: {
            title: '拒绝原因',
            content: rejectionReasonDescription ? rejectionReasonDescription : '经过沟通，客户选择不退款，继续购买。'
        },
        [AfterSaleStatusEnum.REFUNDING]: {
            title: '退货退款状态',
            content: rejectionReasonDescription ? rejectionReasonDescription : '商家已收到客户退回的货物，正在办理退款中。'
        },
        [AfterSaleStatusEnum.REFUND_COMPLETED]: {
            title: '退款方式',
            content: rejectionReasonDescription ? rejectionReasonDescription : '线上退款，退款金额原路返回支付账户。'
        },
        [AfterSaleStatusEnum.RETURN_CLOSED]: {
            title: '关闭原因',
            content: rejectionReasonDescription ? rejectionReasonDescription : '因客户超时未退货，售后申请已被系统取消。'
        },
        [AfterSaleStatusEnum.CUSTOMER_WITHDRAWN]: {
            title: '取消原因',
            content: rejectionReasonDescription ? rejectionReasonDescription : '客户主动撤回售后申请，交易将正常进行。'
        },
        [AfterSaleStatusEnum.PENDING_PAYMENT]: {
            title: '退货退款状态',
            content: rejectionReasonDescription ? rejectionReasonDescription : '商家已收到客户退回的货物，正在办理退款中。'
        },
        [AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT]: {
            title: '退货确认',
            content: rejectionReasonDescription ? rejectionReasonDescription : '请确认商品是否有问题。'
        },
    };
});

const reasonDescription = computed(() => {
    return reasonDescriptionMap.value[afterSalesInfoRef.value?.state];
});
</script>

<style lang="less" scoped>
.store_reason_after_sales {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    .store_reason_after_sales__title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 8px;
    }
    .content {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 4px;
    }
    .update_time {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>
