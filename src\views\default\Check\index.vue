<template>
  <div class="main-bg checkWrapper">
    <van-loading />
  </div>
</template>

<script lang="ts" setup>
import { RoutesName } from "@/enums/routes";
import { routesMap } from "@/router/maps";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/stores/modules/user";
import { getFirstChildrenRoute, getRoutesMapByConfig } from "@/utils/routerUtils";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import {afterLogin, clearLoginStatus} from "@/utils/accountUtils";
import { RoleTypeEnum } from "@/enums/role";
import { parseUrlParams } from "@/utils/http/urlUtils";
import { isStoreMode } from "@/utils/envUtils";
const userStore = useUserStore();
const router = useRouter();
afterLogin({router})
hrefRedirect()
function hrefRedirect(){
  const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
  const _routeConfigCache = routeConfigStorage.get(isStoreMode()?'st':'sg');
  const routes = getRoutesMapByConfig(_routeConfigCache, routesMap);
  const tenantRootRoute = routes.find(route=>route.name == RoutesName.Root && route.children && route.children.length)
  if(userStore.redirectUrl){
    
    // if(userStore.redirectUrl.indexOf('scrm') !=-1 ){
    //   if(userStore.userInfo.type !== RoleTypeEnum.Admin){
    //     router.replace(routesMap[RoutesName.Exception9009])
    //     clearLoginStatus()
    //     return 
    //   }
    //   else{
    //     location.href = userStore.redirectUrl;
    //   }
    // }
    // else{
    //   location.href = userStore.redirectUrl;
    // }
    const _targetUrl = new URL(userStore.redirectUrl)
    if(_targetUrl.pathname === '/'){
      router.replace(getFirstChildrenRoute(tenantRootRoute.children));
    }
    else{
      const params = parseUrlParams(_targetUrl.search)
      router.replace({
        path:_targetUrl.pathname,
        query:params
      })
    }
    // location.href = userStore.redirectUrl;
  }
  else{
    // if(userStore.userInfo.type == RoleTypeEnum.Dealer || userStore.userInfo.type == RoleTypeEnum.Admin){
    //   router.replace(getFirstChildrenRoute(tenantRootRoute.children));
    // }
    // else router.replace(routesMap[RoutesName.Exception403])
    router.replace(getFirstChildrenRoute(tenantRootRoute.children));
  }
}

</script>

<style scoped lang="less">
@import "@/styles/default.less";
.checkWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: @blank-background-color;
}
</style>
