<template>
  <!-- 物流信息 -->
  <div v-if="latestShipTrace" class="store_logistics_info_wrapper">
    <div class="store_logistics_info_title text_style">
      <span>物流信息</span>
      <span class="sub_title" @click="showLogisticsDetail">
        详细信息
        <VanIcon name="arrow" />
      </span>
    </div>
    <!-- 最新物流信息 -->
    <div class="store_logistics_info_newest">
      <!-- 点 -->
      <div class="dot"></div>
      <div class="store_logistics_info_newest_content">
        <div class="store_logistics_info_newest_content_title">
          <!-- 状态 -->
          <span class="state">{{ logisticsStatusMap[latestShipTrace?.state ?? 0] }}</span>
          <!-- 时间 -->
          <span class="time">{{ latestShipTrace?.acceptTime ?? '-' }}</span>
        </div>
        <p class="store_logistics_info_newest_content_time">
          {{ latestShipTrace?.acceptStation ?? '-' }}
        </p>
      </div>
    </div>
  </div>
  <!-- 发货信息 -->
  <div v-else class="store_ship_order_wrapper">
    <div class="store_ship_order_title text_style">发货信息</div>
    <!-- 快递公司 -->
    <div class="store_ship_order_item">
      <span class="store_ship_order_item_label text_style">快递公司</span>
      <span class="store_ship_order_item_value text_style">{{ orderInfoRef?.shipCompanyName ?? `-` }}</span>
    </div>
    <!-- 快递单号 -->
    <div class="store_ship_order_item">
      <span class="store_ship_order_item_label text_style">快递单号</span>
      <span class="store_ship_order_item_value text_style">
        {{ orderInfoRef?.trackingNo ?? `-` }}
        <img
          v-if="orderInfoRef?.trackingNo"
          class="store_ship_order_item_copy"
          :src="CopySrc"
          alt=""
          @click.stop="handleCopyID(orderInfoRef?.trackingNo)"
        />
      </span>
    </div>
  </div>
  <!-- 物流信息 -->
  <JLogistics
    v-model:show="showLogistics"
    :shipTracesList="shipTracesList"
    :shipCompanyName="orderInfoRef?.shipCompanyName"
    :trackingNo="orderInfoRef?.trackingNo"
  />
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from "vue";
import { showToast } from "vant";
import { copyText } from "@/utils/clipboardUtils";
import useGetShipTraces from "../hooks/useGetShipTraces";
/** 静态资源 */
import CopySrc from "@/assets/image/member/copy.png";
/** 相关组件 */
import JLogistics from "@/views/StoreModule/components/JLogistics.vue";

defineOptions({ name: 'StoreLogisticsInfo' });

/** props */
const props = defineProps<{
  orderInfo: {
    code?: string;
    shipCompanyName?: string;
    shipCompanyCode?: string;
    trackingNo?: string;
  };
}>();
const { orderInfo: orderInfoRef } = toRefs(props);
const showLogistics = ref(false);
const { shipTracesList, latestShipTrace, logisticsStatusMap, getShipTraces } = useGetShipTraces();

function showLogisticsDetail() {
  showLogistics.value = true;
}

/** 复制快递单号 */
function handleCopyID(data) {
  try {
    copyText(data);
    showToast('快递单号成功');
  }
  catch (e) {
    showToast('快递单号失败');
  }
}

/** 监听 */
watch(() => props.orderInfo, async (newVal) => {
  if (newVal?.trackingNo) {
    let _params = {
      orderCode: newVal?.code,
      shipCompanyCode: newVal?.shipCompanyCode,
      trackingNo: newVal?.trackingNo
    };
    await getShipTraces(_params);
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.text_style {
  font-family: Source Han Sans CN, Source Han Sans CN;
  color: #333333;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.store_logistics_info_wrapper {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 10px 12px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;
  .store_logistics_info_title {
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .sub_title {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
    }
  }
  .store_logistics_info_newest {
    display: flex;
    gap: 8px;
    .dot {
      width: 8px;
      height: 8px;
      margin-left: 6px;
      background: #4DA4FF;
      border-radius: 50%;
      margin-top: 3px;
      transform: translateZ(0);
    }
    .store_logistics_info_newest_content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .store_logistics_info_newest_content_title {
        display: flex;
        align-items: center;
        gap: 4px;
        .state {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 15px;
          color: #4DA4FF;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .time {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .store_logistics_info_newest_content_time {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        text-align: justified;
        font-style: normal;
        text-transform: none;
        line-height: 18px;
      }
    }
  }
}
.store_ship_order_wrapper {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 10px 12px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;
  .store_ship_order_title {
    font-weight: 500;
    font-size: 16px;
  }
  .store_ship_order_item {
    height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .store_ship_order_item_label {
      font-weight: 400;
      font-size: 14px;
    }
    .store_ship_order_item_value {
      font-weight: 400;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 4px;
      .store_ship_order_item_copy {
        width: 16px;
        height: 16px;
      }
    }
  }
}
</style>
