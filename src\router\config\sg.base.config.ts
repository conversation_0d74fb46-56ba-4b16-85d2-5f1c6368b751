import { RoutesName } from "@/enums/routes";

export const sgBaseRoutesConfig = [
  RoutesName.Check,
  RoutesName.Exception403,
  RoutesName.Exception404,
  RoutesName.Exception9000,
  RoutesName.Exception9001,
  RoutesName.Exception9002,
  RoutesName.Exception9003,
  RoutesName.Exception9004,
  RoutesName.Exception9005,
  RoutesName.Exception9006,
  RoutesName.Exception9008,
  RoutesName.Exception9007,
  RoutesName.Exception9009,
  RoutesName.Root,
  RoutesName.SignUp,
  RoutesName.GetCode,
  RoutesName.SignUpOnboarding,
  RoutesName.ExamOnboarding,
  RoutesName.ExamPromoteResult,
  RoutesName.Examination,
  RoutesName.Watch,
  RoutesName.Dummy,
  // RoutesName.UnResiter,
  // RoutesName.Result,
  // {
  //     name:RoutesName.Root,
  // }
];
