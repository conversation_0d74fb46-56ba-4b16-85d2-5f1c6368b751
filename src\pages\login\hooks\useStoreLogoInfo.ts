import { ref } from 'vue'
import {} from '@/services/api'
/**
 * @description 获取logo信息
 */
export default function useStoreLogoInfo() {
  /** 商城logo信息 */
  const logoInfo = ref({
    imgPath: '',
    name: '',
  })

  /** 获取商城logo信息 */
  async function getStoreLogoInfo() {
    try {
      const res = {}
      if (res) {
        Object.assign(logoInfo.value, res)
      }
    } catch (error) {
      uni.showToast({
        title: `获取首页Logo失败: ${error}`,
        icon: 'none',
      })
    }
  }

  return {
    logoInfo,
    getStoreLogoInfo,
  }
}
