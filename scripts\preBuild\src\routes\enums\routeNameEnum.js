"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoutesName = void 0;
/** 商城模块 */
var RoutesName;
(function (RoutesName) {
    /** 商城直播 */
    RoutesName["StoreLive"] = "jw-root-store-live";
    RoutesName["StoreIndex"] = "jw-root-store-index";
    /** 缺省页 */
    RoutesName["StoreDefault"] = "jw-root-store-default";
    /** 商城首页 */
    RoutesName["StoreHome"] = "jw-root-store-home";
    /** 福利券 */
    RoutesName["StoreCoupon"] = "jw-root-store-coupon";
    /** 福利券详情 */
    RoutesName["StoreCouponDetail"] = "jw-root-store-coupon-detail";
    /** 观看时长 */
    RoutesName["StoreWatchTime"] = "jw-root-store-watch-time";
    /** 积分 */
    RoutesName["StoreIntegral"] = "jw-root-store-integral";
    /** 待核销订单 */
    RoutesName["StorePendingVerificationOrders"] = "jw-root-store-pending-verification-orders";
    /** 商城我的 */
    RoutesName["StoreMine"] = "jw-root-store-mine";
    /** 我的订单 */
    RoutesName["StoreMyOrders"] = "jw-root-store-my-orders";
    /** 店铺订单 */
    RoutesName["StoreShopOrders"] = "jw-root-store-shop-orders";
    /** 订单统计 */
    RoutesName["StoreOrderStatistics"] = "jw-root-store-order-statistics";
    /** 商品分类 */
    RoutesName["StoreCategory"] = "jw-root-store-category";
    /** 商品搜索 */
    RoutesName["StoreSearch"] = "jw-root-store-search";
    /** 商品详情 */
    RoutesName["StoreDetail"] = "jw-root-store-detail";
    /** 订单详情 */
    RoutesName["StoreOrderDetail"] = "jw-root-store-order-detail";
    /** 申请退款 */
    RoutesName["StoreApplyRefund"] = "jw-root-store-apply-refund";
    /** 售后详情 */
    RoutesName["StoreAfterSaleDetail"] = "jw-root-store-after-sale-detail";
    /** 排行榜 */
    RoutesName["StoreRanking"] = "jw-root-store-ranking";
    /** 积分商城 */
    RoutesName["StoreIntegralMall"] = "jw-root-store-integral-mall";
    /** 福利券统计 */
    RoutesName["StoreWelfareVoucherStatistics"] = "jw-root-welfare-voucher-statistics";
    /** 福利券统计详情 */
    RoutesName["StoreWelfareVoucherStatisticsDetail"] = "jw-root-welfare-voucher-statistics-detail";
    /**核销统计 */
    RoutesName["StoreVerificationStatistics"] = "jw-root-verification-statistics";
    /**销售统计 */
    RoutesName["StoreSalesStatistics"] = "jw-root-sale-statistics";
    /**店员统计 */
    RoutesName["StoreShopAssistantStatistics"] = "jw-root-shop-assistant-statistics";
    /**积分商城-我能兑 */
    RoutesName["StoreIntegralMallCanExchange"] = "jw-root-store-integral-mall-can-exchange";
    /**福利卷商城 */
    RoutesName["StoreWelfareVoucherMall"] = "jw-root-store-welfare-voucher-mall";
    /**确认订单 */
    RoutesName["StoreConfirmOrder"] = "jw-root-store-confirm-order";
    /**收银台 */
    RoutesName["StoreCashier"] = "jw-root-store-cashier";
    /**账号设置 */
    RoutesName["StoreAccountSetting"] = "jw-root-store-account-setting";
    /**会员管理  */
    RoutesName["MemberManagement"] = "jw-root-member-management";
    /**观看时长  */
    RoutesName["ViewDuration"] = "jw-root-view-duration";
    /**登录 */
    RoutesName["StoreLogin"] = "jw-root-store-login";
    /**绑定门店 */
    RoutesName["StoreInviteMember"] = "jw-root-store-invite-member";
    /** 我的经销商 */
    RoutesName["MyDealer"] = "jw-root-store-my-dealer";
    /** 店铺管理 */
    RoutesName["StoreManagement"] = "jw-root-store-management";
    /** 注册 */
    RoutesName["StoreSignup"] = "jw-root-store-signup";
    /** 地址新增 */
    RoutesName["StoreAddressAdd"] = "jw-root-store-address-add";
    /** 收货地址 */
    RoutesName["StoreAddress"] = "jw-root-store-address";
    /** 现金券 */
    RoutesName["StoreCashCoupon"] = "jw-root-store-cash-coupon";
    /** 退货订单 */
    RoutesName["StoreReturnOrder"] = "jw-root-store-return-order";
    /** 退款审核 */
    RoutesName["StoreRefundAudit"] = "jw-root-store-refund-audit";
    /** 填写物流信息 */
    RoutesName["StoreFillLogistics"] = "jw-root-store-fill-logistics";
    /** 填写物流单号 */
    RoutesName["StoreFillLogisticsNo"] = "jw-root-store-fill-logistics-no";
    /** 数据导出 */
    RoutesName["StoreDataExport"] = "jw-root-store-data-export";
    /** 导出记录 */
    RoutesName["StoreExportRecord"] = "jw-root-store-export-record";
    /** 文件下载 */
    RoutesName["StoreFileDownload"] = "jw-root-store-file-download";
    /** 钱包 */
    RoutesName["StoreWallet"] = "jw-root-store-wallet";
    /** 提现记录 */
    RoutesName["StoreWithdrawRecord"] = "jw-root-store-withdraw-record";
    /** 提现 */
    RoutesName["StoreWithdraw"] = "jw-root-store-withdraw";
    /** 银行卡信息 */
    RoutesName["StoreBankCard"] = "jw-root-store-bank-card";
    /** 门店物流 */
    RoutesName["StoreLogistics"] = "jw-root-store-logistics";
})(RoutesName = exports.RoutesName || (exports.RoutesName = {}));
