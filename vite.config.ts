import { defineConfig, loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
// @see https://unocss.dev/
import UnoCSS from 'unocss/vite'

// https://vitejs.dev/config/
export default ({ mode }) => {
  const { UNI_PLATFORM } = process.env
  const env = loadEnv(mode, process.cwd())
  console.log('UNI_PLATFORM -> ', UNI_PLATFORM) // 得到 mp-weixin, h5, app 等

  const plugins = [uni(), UnoCSS()]

  return defineConfig({
    plugins,
    server: {
      port: Number(env.VITE_DEV_PORT),
      host: '127.0.0.1',
    },
    build: {
      target: UNI_PLATFORM === 'h5' ? 'es2015' : 'esnext',
      cssTarget: 'chrome61',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: false,
          drop_debugger: true,
        },
        format: {
          comments: false,
        },
      },
    },
    esbuild: {
      target: UNI_PLATFORM === 'h5' ? 'es2015' : 'esnext',
      supported: {
        'object-rest-spread': false,
        'optional-chaining': false,
        'nullish-coalescing': false,
      },
    },
  })
}
