<template>
    <div class="store-address-wrapper">
        <img :src="storeAvatar" alt="门店头像" />
        <!-- 门店信息 -->
        <div class="store-info">
            <div class="store-name">{{ storeInfoRef.storeName }}</div>
            <div class="store-address">
                <img :src="addressSrc" alt="" />
                <span>{{ formattedAddress }}</span>
            </div>
            <!-- 电话 -->
            <div class="store-phone">
                <img :src="phoneSrc" alt="" />
                <a class="member-phone" @click.stop :href="storeInfoRef.contactPhone ? `tel:${storeInfoRef.contactPhone}` : 'javascript:void(0)'">
                    <span>{{ storeInfoRef.contactPhone ? storeInfoRef.contactPhone : '暂无电话'}}</span>
                </a>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { toRefs, computed } from "vue";
import { StoreStatusEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import addressSrc from "@/assets/storeImage/storeHome/address.png";
import phoneSrc from "@/assets/storeImage/storeHome/phone.png";
import storeSrc from "@/assets/storeImage/storeMine/store.png";

defineOptions({ name: 'StoreAddress' });

/** props */
const props = defineProps<{
    storeInfo: {
        storeName: string;
        storeAvatar: string;
        storeStatus: StoreStatusEnum;
        province: string;
        city: string;
        area: string;
        addressDetail: string;
        contactPhone: string;
    }
}>();

const { storeInfo: storeInfoRef } = toRefs(props);

/** 门店地址 */
const formattedAddress = computed(() => {
  const { province = '', city = '', area = '', addressDetail = '' } = storeInfoRef.value || {};
  const address = `${province}${city}${area}${addressDetail}`.trim();
  return address || '暂无地址';
});

/** 门店头像 */
const storeAvatar = computed(() => {
  const { storeAvatar } = storeInfoRef.value || {};
  return storeAvatar || storeSrc;
});
</script>


<style lang="less" scoped>
.store-address-wrapper {
    width: 100%;
    border-radius: 8px;
    padding: 8px 12px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    img {
        width: 56px;
        height: 56px;
        border-radius: 4px;
    }
    .store-info {
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        gap: 4px;
        .store-name {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            line-height: 20px;
        }
        .store-address {
            display: flex;
            align-items: center;
            img {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }
            span {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 22px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
        .store-phone {
            display: flex;
            align-items: center;
            img {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }
            span {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #1677FF;
                line-height: 22px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>