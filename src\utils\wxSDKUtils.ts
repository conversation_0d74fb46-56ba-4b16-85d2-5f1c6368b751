import { loadScriptBySrc } from "./domUtils"
import { isLoginQWEnv } from "./envUtils";

interface WxInitParams{
    debug: boolean, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    appId: string, // 必填，公众号的唯一标识
    timestamp: string , // 必填，生成签名的时间戳
    nonceStr: string, // 必填，生成签名的随机串
    signature: string,// 必填，签名
    jsApiList: Array<string> // 必填，需要使用的JS接口列表
}

 function loadWxSDKScripts(){
    function loadWxScript(src:string, isBackup = false) {
        return new Promise((resolve, reject) => {
            loadScriptBySrc(src).then(dom => {
                if (window.wx.config) {
                    resolve(true);
                } else {
                    if (!isBackup) {
                        dom.remove();
                    }
                    reject(new Error('Script loaded but window.wx not found.'));
                }
            }).catch(err => {
                reject(err);
            });
        });
    }
    const _src = isLoginQWEnv()?`https://res.wx.qq.com/open/js/jweixin-1.2.0.js`:`https://res.wx.qq.com/open/js/jweixin-1.6.0.js`
    const _srcBackup = isLoginQWEnv()?`https://res2.wx.qq.com/open/js/jweixin-1.2.0.js`:`https://res2.wx.qq.com/open/js/jweixin-1.6.0.js`

    return new Promise((resolve, reject) => {
        if (window.wx && window.wx.config) {
            resolve(true);
        } else {
            loadWxScript(_src)
            .catch(() => {
                return loadWxScript(_srcBackup, true); 
            })
            .then(resolve)
            .catch(reject);
        }
    });
}

function loadQWSDKScripts(){
    function loadWxScript(src:string, isBackup = false) {
        return new Promise((resolve, reject) => {
            loadScriptBySrc(src).then(dom => {
                if (window.wx.agentConfig) {
                    resolve(true);
                } else {
                    if (!isBackup) {
                        dom.remove();
                    }
                    reject(new Error('Script loaded but window.wx not found.'));
                }
            }).catch(err => {
                reject(err);
            });
        });
    }

    const _src = `https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js`

    return new Promise((resolve, reject) => {
        if (window.wx && window.wx.agentConfig) {
            resolve(true);
        } else {
            loadWxScript(_src)
            .then(resolve)
            .catch(reject);
        }
    });
}

export function wxSdkInit(params:WxInitParams) {
    return new Promise((resolve, reject) => {
        loadWxSDKScripts().then(()=>{
            window.wx.config(params);
            window.wx.ready(function () {
               resolve(true)
            })
            window.wx.error(function (err) {
                reject(err)
            })
        })
        .catch(err=>{
            reject(err)
        })
    })
}

export function checkWxJSApiValid(jsApiList:Array<string>) {
    return new Promise((resolve, reject) => {
        window.wx.checkJsApi({
            jsApiList,
            success: function(res) {
                const _result = {}
                for(const key in res.checkResult){
                    _result[key] = res.checkResult[key] === "true" ? true:false
                }
                resolve(_result)
            // 以键值对的形式返回，可用的api值true，不可用为false
            // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
            }
        });
    })
}



interface QWInitParams{
    agentid: string, 
    corpid: string, // 必填，公众号的唯一标识
    timestamp: string , // 必填，生成签名的时间戳
    nonceStr: string, // 必填，生成签名的随机串
    signature: string,// 必填，签名
    jsApiList: Array<string> // 必填，需要使用的JS接口列表
}

export function qwSDKInit(params:QWInitParams) {
    return new Promise((resolve, reject) => {
        loadQWSDKScripts().then(()=>{
            window.wx.agentConfig({
                ...params,
                success:()=>{
                    resolve(true)
                },
                fail:(err)=>{
                    reject(err)
                }
            });
        })
    })
}