<template>
  <view class="store_order_detail_status">
    <image
      :src="orderStatusIconMap[orderInfoRef?.status]"
      alt=""
      class="store_order_detail_status_icon"
    />
    <view class="store_order_detail_status_text">
      <view class="store_order_detail_status_text_title">
        {{ orderStatusMap[orderInfoRef?.status] }}
      </view>
      <view class="store_order_detail_status_text_subTitle">
        {{ orderStatusTipMap[orderInfoRef?.status] }}
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { toRefs, computed } from 'vue'
import {
  StoreOrderTypeEnum,
  StoreOrderStatusEnum,
  ProductPickupModeEnum,
  OrderVerificationTypeEnum,
} from '@/enum'
/** 相关静态资源 */
import pickUpSrc from '@/static/images/storeHome/orderDetails/pick-up.png'
import completeTransactionSrc from '@/static/images/storeHome/orderDetails/complete_transaction.png'
import cancelledSrc from '@/static/images/storeHome/orderDetails/cancelled.png'

defineOptions({ name: 'StoreOrderStatus' })

/** props */
const props = withDefaults(
  defineProps<{
    orderInfo: {
      type?: StoreOrderTypeEnum
      status?: StoreOrderStatusEnum
      pickupType?: ProductPickupModeEnum
      verificationType?: OrderVerificationTypeEnum
    }
  }>(),
  {
    orderInfo: () => ({
      status: StoreOrderStatusEnum.WAIT_RECEIVE,
    }),
  },
)

const { orderInfo: orderInfoRef } = toRefs(props)

/**
 * @description 计算属性
 */

/** 商品是否快递到家 */
const isHomeDelivery = computed(() => {
  return orderInfoRef.value?.pickupType == ProductPickupModeEnum.HOME_DELIVERY
})

/** 商品是否自提 */
const isSelfPickUp = computed(() => {
  return orderInfoRef.value?.pickupType == ProductPickupModeEnum.STORE_PICKUP
})

/** 商品是否下单门店到货后核销 */
const isStoreVerification = computed(() => {
  return orderInfoRef.value?.verificationType == OrderVerificationTypeEnum.STORE_VERIFICATION
})

/** 待发货提示语 */
function getWaitSendStatus() {
  if (isHomeDelivery.value) return '待发货'
  if (isStoreVerification.value) return '待发货至门店后提货'
  return '待提货'
}

/** map */
const orderStatusMap = computed(() => ({
  [StoreOrderStatusEnum.WAIT_RECEIVE]: '待收货',
  [StoreOrderStatusEnum.CANCELLED]: '交易取消',
  [StoreOrderStatusEnum.WAIT_PAY]: '待支付',
  [StoreOrderStatusEnum.WAIT_SEND]: getWaitSendStatus(),
  [StoreOrderStatusEnum.FINISHED]: '交易成功',
}))
/** tip */
const orderStatusTipMap = computed(() => ({
  [StoreOrderStatusEnum.WAIT_RECEIVE]: '你的商品正在快马加鞭',
  [StoreOrderStatusEnum.CANCELLED]: '你的交易已取消',
  [StoreOrderStatusEnum.WAIT_PAY]: '你的交易待支付',
  [StoreOrderStatusEnum.WAIT_SEND]: isHomeDelivery.value
    ? '你的订单正在安排发货'
    : '你的订单正在安排提货',
  [StoreOrderStatusEnum.FINISHED]: '你的交易已完成',
}))

/** 图标 */
const orderStatusIconMap = {
  [StoreOrderStatusEnum.WAIT_RECEIVE]: pickUpSrc,
  [StoreOrderStatusEnum.CANCELLED]: cancelledSrc,
  [StoreOrderStatusEnum.WAIT_PAY]: pickUpSrc,
  [StoreOrderStatusEnum.WAIT_SEND]: pickUpSrc,
  [StoreOrderStatusEnum.FINISHED]: completeTransactionSrc,
}
</script>

<style lang="scss" scoped>
.store_order_detail_status {
  display: flex;
  align-items: center;
  gap: 16rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  .store_order_detail_status_icon {
    width: 76rpx;
    height: 76rpx;
  }
  .store_order_detail_status_text {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    .store_order_detail_status_text_title {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 500;
      font-size: 36rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .store_order_detail_status_text_subTitle {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
