import { StoreUserInfoTypeEnum } from "@/enums/role";
import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

export const enum StoreAccountApi{
  storeLogin = "/h5/storeQRCode/login",
  getStoreLoginWxappID = '/h5/globalConfigs/getGlobalConfigs',
  refreshUserInfo = '/h5/storeQRCode/refresh',
  getJSSDKConfig = '/h5/signature',
}

interface StoreLoginParams{
   code:string,
   state?:string 
}

export const enum StoreUserInfoStatusEnum{
  ENABLE = 0,
  DISABLE = 1,
}

export interface StoreUserInfo {
  /**
   * 客户编号
   */
  code?: string;
  /**
   * 性别
   */
  gender?: string;
  /**
   * 身份证
   */
  idNo?: string;
  /**
   * 用户头像
   */
  img?: string;
  /**
   * 手机号
   */
  mobile?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 用户昵称
   */
  nickname: string;
  /**
   * 是否启用：0=启用、1=不启用
   */
  status: StoreUserInfoStatusEnum;
  /**
   * 商城TOKEN
   */
  token: string;
  /**
   * 商城百货角色
   */
  type: StoreUserInfoTypeEnum;
}
export function storeLogin(params:StoreLoginParams){
  return defHttp.post<StoreUserInfo>({
    url: getStoreApiUrl(StoreAccountApi.storeLogin),
    params,
    requestConfig: {
      withToken: false,
    },
    options:{
      timeout:5000
    }
  });
}


export function getStoreLoginWxappID(){
  return defHttp.get<{sgAppId:string}>({
    url: getStoreApiUrl(StoreAccountApi.getStoreLoginWxappID),
    params:{},
    requestConfig: {
      withToken: false,
    },
    options:{
      timeout:5000
    }
  });
}

export function storeUSerInfoRefresh(){
  return defHttp.post<StoreUserInfo>({
    url: getStoreApiUrl(StoreAccountApi.refreshUserInfo),
    params:{},
    options:{
      timeout:5000
    }
  });
}
export interface GetJSSDKConfigResponse{
  "signature": "string",
  "nonceStr": "string",
  "url": "string",
  "timestamp": "string"
}

export function getJSSDKConfig(url:string){
  return defHttp.get<GetJSSDKConfigResponse>({
    url: getStoreApiUrl(StoreAccountApi.getJSSDKConfig),
    params:{
        url
    },
    requestConfig: {
      isQueryParams: true,
    },
  });
}