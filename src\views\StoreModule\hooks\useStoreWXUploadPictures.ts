import { useRoute } from "vue-router";
import useBoolean from "./useBoolean";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { CacheConfig } from "@/utils/cache/config";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { isDevEnv } from "@/utils/envUtils";
import { isArray, isIOSEnv } from "@/utils/isUtils";
import { wxSdkInit } from "@/utils/wxSDKUtils";
import { getJSSDKConfig } from "@/services/storeApi";

/** 是否初始化WXSdk */
const { bool: isInitRef, setTrue: setInitTrue, setFalse: setInitFalse } = useBoolean(false);

/**
 * 将微信返回的本地图片ID转换为Base64格式（兼容iOS/Android）
 * @param localId - 微信返回的本地图片ID
 * @returns Promise<string> - Base64格式的图片数据
 */
function getLocalImgDataToBase64(localId: string): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!isInitRef.value || !window.wx) {
      reject("微信 JSSDK 未初始化");
      return;
    }

    window.wx.getLocalImgData({
      localId,
      success: res => {
        const base64Data = res.localData;

        // iOS直接返回Base64，Android返回本地路径需转换
        if (base64Data.startsWith("data:image")) {
          resolve(base64Data);
        } else {
          convertAndroidLocalPathToBase64(base64Data).then(resolve).catch(reject);
        }
      },
      fail: err => reject(`微信获取本地图片失败: ${JSON.stringify(err)}`),
    });
  });
}

/**
 * Android本地路径转Base64（通过Canvas）
 */
function convertAndroidLocalPathToBase64(localPath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    let canvas: HTMLCanvasElement | null = null;

    // 清理资源
    const cleanup = () => {
      img.onload = null;
      img.onerror = null;
      img.src = "";
      if (canvas) {
        document.body.removeChild(canvas);
        canvas = null;
      }
    };

    img.onload = () => {
      try {
        canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext("2d");
        if (!ctx) throw new Error("无法获取Canvas上下文");

        ctx.drawImage(img, 0, 0);
        const quality = 0.8; // 压缩质量
        const base64 = canvas.toDataURL("image/jpeg", quality);
        cleanup();
        resolve(base64);
      } catch (error) {
        cleanup();
        reject(`图片转换失败: ${error}`);
      }
    };

    img.onerror = () => {
      cleanup();
      reject("图片加载失败");
    };

    img.src = localPath;
  });
}

export default function useStoreWXUploadPictures() {
  /**
   * 获取微信图片（支持多图/Base64转换）
   * @param needBase64 - 是否需要Base64格式（默认返回微信localIds）
   * @param count - 最多选择图片数量（默认9张）
   * @returns Promise<string | string[]> - 单图返回string，多图返回数组
   */
  function getWxPictures(count = 9): Promise<string | string[]> {
    return new Promise(async (resolve, reject) => {
      if (!window.wx || !isInitRef.value) {
        reject("微信 JSSDK 未初始化");
        return;
      }

      window.wx.chooseImage({
        count: Math.min(count, 9), // 微信限制最多9张
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: async (res) => {
          if (!res.localIds?.length) {
            reject("未选择任何图片");
            return;
          }

          try {
            const results = await Promise.all(res.localIds.map(getLocalImgDataToBase64));

            // 单图直接返回，多图返回数组
            resolve(results.length === 1 ? results[0] : results);
          } catch (error) {
            reject(`图片处理失败: ${error}`);
          }
        },
        fail: (err) => reject(`微信选图接口错误: ${JSON.stringify(err)}`),
      });
    });
  }

  /**
   * 初始化微信图片上传SDK
   * 添加了getLocalImgData接口用于Base64转换
   */
  function initUploadPictureWXSdk() {
    setInitFalse();
    const route = useRoute();
    const systemStore = useSystemStoreWithoutSetup();
    const _url = isIOSEnv() ? systemStore.entryUrl : `${window.location.origin}${route.fullPath}`;

    getJSSDKConfig(_url)
      .then(res => {
        const { signature, nonceStr, timestamp } = res;
        const stateCache = createCacheStorage(CacheConfig.StoreToken);
        let _stateInfo = stateCache.get();

        if (!_stateInfo["wxappId"]) {
          throw new Error("Missing wxappId in cache");
        }

        // 初始化配置
        wxSdkInit({
          debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
          appId: _stateInfo["wxappId"] as string, // 必填，公众号的唯一标识
          timestamp, // 必填，生成签名的时间戳
          nonceStr, // 必填，生成签名的随机串
          signature, // 必填，签名
          jsApiList: ["chooseImage", "previewImage", "uploadImage", "getLocalImgData"], // 必填，需要使用的JS接口列表
        })
          .then(() => {
            setInitTrue();
          })
          .catch(err => {
            console.log("Init WeChat JSSDK error:", err);
          });
      })
      .catch(err => {
        console.log("getJSSDKConfig:", err);
      });
  }

  return {
    getWxPictures,
    initUploadPictureWXSdk,
    // 导出Base64转换方法供外部使用
    getLocalImgDataToBase64,
  };
}
