<template>
    <div class="goods-search">
        <view class="search-box" @click="handleSearch">
            <van-search shape="round" readonly placeholder="请输入商品名称搜索" />
        </view>
        <van-pull-refresh class="content-warpper" v-model="isPullLoadingRef" @refresh="onRefresh">
            <van-list v-model:loading="exchangeLoadingRef" class="content-list" :finished="listFinishedRef" :offset="50"
                @load="loadPageData" finished-text="没有更多了">
                <GoodsCard v-for="item in exchangeList" @click="handleClickGoods(item.productId)" :type="StoreGoodsEnum.IntegralGoods"
                    @choose-sku="handleChooseSku" :border-all="false" :key="item.productId" :card-info="item">
                </GoodsCard>
            </van-list>
        </van-pull-refresh>
    </div>
    <IntegralModal v-model:show="isShowModal" :state="skuState" />
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import GoodsCard from "../../components/GoodsCard.vue";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import IntegralModal from "../../components/IntegralModal/index.vue";
import { useRouter, useRoute } from "vue-router"
import { RoutesName } from "@/enums/routes";
import { useExchangeData } from "../hooks/useExchangeData";
const route = useRoute()
const router = useRouter()
const initParams = {
    keyword: '',
}
const modal = ref({ ...initParams })
const { exchangeLoadingRef, exchangeList, listFinishedRef, isPullLoadingRef, loadPageData, onRefresh,getPoints, reloadData } = useExchangeData({
    modal,
})
const isShowModal = ref<boolean>(false)
const skuState = ref<any>({})

const handleSearch = () => {
    router.push({
        name: RoutesName.StoreSearch,
        query: {
            ...route.query,
            type: StoreGoodsEnum.IntegralGoods
        }
    })
}
const handleClickGoods = (id: string) => {
    router.push({
        name: RoutesName.StoreDetail,
        query: {
            ...route.query,
            id,
            type: StoreGoodsEnum.IntegralGoods,
        }
    })
}
const handleChooseSku = (info) => {
    skuState.value = JSON.parse(JSON.stringify(info))
    isShowModal.value = true
}
onMounted(() => {
    getPoints()
})
</script>

<style scoped lang="less">
:deep(.van-cell){
	background: none !important;
	padding: 0 !important;
}
.goods-search {
    height: calc(100vh - env(safe-area-inset-bottom));
    display: flex;
    flex-direction: column;

    .search-box {}

    .content-warpper {
        flex: 1;
        overflow-y: auto;
        padding: 12px;

        .content-list {
            padding: 12px;
            background-color: #fff;
            border-radius: 8px;
        }
    }
}
</style>