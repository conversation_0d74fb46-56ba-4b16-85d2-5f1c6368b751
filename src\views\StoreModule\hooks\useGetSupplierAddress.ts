import { ref } from "vue";
import { getSupplierAddress } from "@/services/storeApi";

export default function useGetSupplierAddress() {
  interface Supplier {
    addressDetail?: string;
    city?: string;
    cityId?: string;
    companyName?: string;
    contactName?: string;
    contactPhone?: string;
    createTime?: string;
    district?: string;
    districtId?: string;
    extraInfo?: string;
    id?: string;
    province?: string;
    provinceId?: string;
    remark?: string;
    status?: number;
    supplierName?: string;
  }

  /** 经销商地址信息 */
  const supplierAddress = ref<Supplier>({});

  /** 获取经销商地址信息 */
  async function getSupplierAddressList(id: string) {
    // TODO: 获取门店基础信息
    try {
      const _params = { id: id };
      const resp = await getSupplierAddress(_params);
      if (resp) {
        Object.assign(supplierAddress.value, resp);
      }
    } catch (error) {
      console.log(error);
    }
  }

  return {
    supplierAddress,
    getSupplierAddressList,
  };
}
