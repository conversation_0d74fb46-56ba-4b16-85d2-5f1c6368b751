import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
const enum SystemApiEnum {
  getAgreementByKey = "/applet/globalConfigs/getAgreement",
  getStoreLogo = '/h5/homeLogo/getLogo',
  acceptStoreBelong = '/applet/store/personal/acceptStoreBelong'
}
// 查询协议
export async function getAgreementByKey(params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(SystemApiEnum.getAgreementByKey),
    params,
    requestConfig: {
      withToken: true,
    },
  });
}


interface StoreInfoResponse{
  createTime: string,
  homeImgPath: string,
  id: string,
  imgPath: string,
  isEnableImg: number,
  isEnableName: number,
  name: string,
  slogan: string,
  updateTime: string,
}
export async function getStoreLogo() {
  return defHttp.get<StoreInfoResponse>({
    url: getStoreApiUrl(SystemApiEnum.getStoreLogo),
    requestConfig: {
      withToken: true,
    },
  });
}
export async function acceptStoreBelong(state:string) {
  return defHttp.get({
    url: getStoreApiUrl(SystemApiEnum.acceptStoreBelong),
    params:{
      state
    }
  });
}