import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";
import MainLayout from "@/layout/MainLayout/index.vue";

export const StoreBasic = {
    [RoutesName.StoreIndex]: {
        path: "/st",
        component: MainLayout,
        redirect: "main",
        meta: {
          isMenu: false,
          isShow: false,
        },
    },
    [RoutesName.StoreLogin]: {
        path: "login",
        component: ()=> import("@/views/StoreModule/StoreLogin/index.vue"),
        meta: {
            title: "会员登录",
            isMenu: false,
            isShow: false,
        },
    },
    [RoutesName.StoreDefault]: {
        path: "exception",
        component: ()=> import("@/views/StoreModule/StoreException/index.vue"),
        meta: {
            title: "",
            isMenu: false,
            isShow: false,
        },
        props: (route: RouteLocation) => ({
          code: route.query.code ?? 403,
        }),
    }
}