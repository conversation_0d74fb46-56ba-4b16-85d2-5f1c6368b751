import { isIOSEnv } from './isUtils'

/**
 * 初始化剪贴板复制默认事件
 * 禁止复制时自动添加的制表符
 */
export function initClipboardCopyDefaultEvent(): void {
  document.addEventListener('copy', (e: ClipboardEvent) => {
    const clipboardData = e.clipboardData
    if (!clipboardData) return

    const selectionText = window.getSelection()?.toString()
    if (selectionText) {
      e.preventDefault()
      // 移除制表符和多余空格
      const cleanText = selectionText.trim().replace(/\t/g, '')
      clipboardData.setData('text/plain', cleanText)
    }
  })
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 复制是否成功
 */
export async function copyText(text: string): Promise<boolean> {
  const cleanText = text.trim()
  if (!cleanText) return false

  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && isIOSEnv()) {
      await navigator.clipboard.writeText(cleanText)
      return true
    }
  } catch (error) {
    console.warn('Clipboard API failed, falling back to execCommand:', error)
  }

  // 降级到 execCommand 方法
  return fallbackCopyText(cleanText)
}

/**
 * 降级复制方法 - 使用 execCommand
 * @param text 要复制的文本
 * @returns boolean 复制是否成功
 */
function fallbackCopyText(text: string): boolean {
  try {
    const input = document.createElement('input')
    input.style.cssText = `
      position: absolute;
      left: -99999px;
      bottom: -99999px;
      opacity: 0;
      pointer-events: none;
    `
    input.setAttribute('readonly', '')
    input.value = text

    document.body.appendChild(input)
    input.focus()
    input.select()
    input.setSelectionRange(0, text.length)

    const success = document.execCommand('copy')
    document.body.removeChild(input)

    return success
  } catch (error) {
    console.error('Fallback copy failed:', error)
    return false
  }
}

export function copyToClipboard(value) {
  // 调用 API 设置剪切板内容
  uni.setClipboardData({
    data: value, // 要复制的内容
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
        duration: 1500,
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 1500,
      })
    },
  })
}
