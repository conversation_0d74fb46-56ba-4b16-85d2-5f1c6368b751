import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 文件下载 */
export const enum DownloadFileEnum {
  // 导出
  EXPORT = "/h5/exportRecord/export",
  // 分页查询列表
  PAGE_LIST = "/h5/exportRecord/page",
  // 下载
  DOWNLOAD = "/h5/exportRecord/download",
}

/**
 * @description 导出
 */
export function exportRecordApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(DownloadFileEnum.EXPORT),
    params: {
        data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 分页查询导出列表
 */
export function pageExportRecord(_params) {
  return defHttp.post({
    url: getStoreApiUrl(DownloadFileEnum.PAGE_LIST),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 下载
 */
export function downloadRecord(_params: { id: string }) {
  return defHttp.get({
    url: getStoreApiUrl(DownloadFileEnum.DOWNLOAD),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}
