<template>
    <div class="store_data_export_card_wrapper">
        <div class="left">
            <span>{{ dataReportRef.title }}</span>
            <p>{{ dataReportRef.description }}</p>
        </div>
        <div class="right">
            <VanIcon name="arrow" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { toRefs } from "vue";

defineOptions({ name: "StoreDataExportCard" });

/** props */
const props = defineProps<{
    dataReport: {
        id: string;
        title: string;
        description: string;
    }
}>();

const { dataReport: dataReportRef } = toRefs(props);
</script>

<style lang="less" scoped>
.store_data_export_card_wrapper {
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .left {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
        span {
            font-family: Source <PERSON>, Source <PERSON>s <PERSON>N;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        p {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
    .right {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>