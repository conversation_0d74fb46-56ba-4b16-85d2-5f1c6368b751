/**
 * @description 使用微信小程序的 wx.openDocument API 打开下载的文档文件
 */
export function previewDocumentByUrl(url: string) {
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url,
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          filePath: filePath,
          success: function (res) {
            resolve(true)
          },
          fail: function (res) {
            reject(res)
          },
        })
      },
      fail: function (res) {
        reject(res)
      },
    })
  })
}
