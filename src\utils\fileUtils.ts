import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { isString } from "./isUtils";
import dayjs from 'dayjs';

export function downloadFileFromStream({ stream, type = "application/octet-stream", filename = "文件下载" }) {
  let blob = new Blob([stream], { type: type });
  let downloadElement = document.createElement("a");
  let href = window.URL.createObjectURL(blob);
  downloadElement.href = href;
  downloadElement.download = filename;
  document.body.appendChild(downloadElement);
  downloadElement.click();
  document.body.removeChild(downloadElement);
  window.URL.revokeObjectURL(href);
}

export function transformMinioSrc(fileSrc: string): string {
  if(isString(fileSrc) && fileSrc.includes('-oss.')){
    return fileSrc
  }
  else{
    const systemStore = useSystemStoreWithoutSetup();
    if (isString(fileSrc)) {
      return `${systemStore.imgPrefix}/upload/downloadFile?filePath=${fileSrc}`;
    } 
    else {
      return "";
    }
  }
}

export function transformMinioStoreSrc(fileSrc: string): string {
  const systemStore = useSystemStoreWithoutSetup();
  if (isString(fileSrc)) {
    return `${systemStore.imgPrefix}/upload/downloadStoreFile?filePath=${fileSrc}`;
  } else return "";
}

export function revertToMinioSrc(fileSrc: string): string {
  const systemStore = useSystemStoreWithoutSetup();
  return `${fileSrc.replace(`${systemStore.imgPrefix}/upload/downloadFile?filePath=`, "")}`;
}

export function asyncFileToBlob(file: File) {
  return new Promise((resolve, reject) => {
    const { type } = file;
    const reader = new FileReader();
    reader.onload = function (evt) {
      const blob = new Blob([evt.target.result], { type });
      resolve(blob);
    };
    reader.onerror = function (err) {
      reject(err);
    };
    reader.readAsDataURL(file);
  });
}
export function asyncFileToBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = function (evt) {
      resolve(evt.target.result);
    };
    reader.onerror = function (err) {
      reject(err);
    };
    reader.readAsDataURL(file);
  });
}

export function blobToFile(blob: Blob) {
  return new File([blob], "avatar.jpg", { type: blob.type });
}



type MergeQRCodeToBgPicParams = {
  bgSrc: string;
  qrCodeConfig: {
    src: string;
    x: number;
    y: number;
    width: number;
    height: number;
    avatarSrc?:string,
    avatarWidth?:number,
    avatarHeight?:number,
    avatarX?:number,
    avatarY?:number,
  };
}

export function mergeQRCodeToBgPic({  
  bgSrc,
  qrCodeConfig: {  
    src,
    x,
    y,
    width,
    height,
    avatarSrc,
    avatarWidth,
    avatarHeight,
    avatarX,
    avatarY
  }  
}:MergeQRCodeToBgPicParams):Promise<string> {  
  return new Promise((resolve, reject) => { 
    const canvas = document.createElement('canvas');  
    const ctx = canvas.getContext('2d'); 
    const bgImage = new Image();
    bgImage.crossOrigin = 'anonymous';  
    bgImage.src = bgSrc; 
    bgImage.onload = function () {
      canvas.width = bgImage.width;  
      canvas.height = bgImage.height;  
      ctx.drawImage(bgImage, 0, 0, bgImage.width, bgImage.height);  
      const qrCodeImage = new Image(); 
      qrCodeImage.crossOrigin = 'anonymous';  
      qrCodeImage.src = src;  
      qrCodeImage.onload = function () {  
        ctx.drawImage(qrCodeImage, x, y, width, height);  
        if(avatarSrc){
          const avatarImage = new Image(); 
          avatarImage.crossOrigin = 'anonymous';  
          avatarImage.src = avatarSrc;  
          avatarImage.onload = function () {  
            ctx.drawImage(avatarImage, avatarX, avatarY, avatarWidth, avatarHeight);  
            const base64 = canvas.toDataURL();  
            resolve(base64);  
          };
          avatarImage.onerror = function (err) {
            reject("读取头像异常");
          }  
        } 
        else{
          const base64 = canvas.toDataURL();  
          resolve(base64);  
        }
       
      };
      qrCodeImage.onerror = function (err) {
        reject("读取二维码异常");
      } 
      
    }
    bgImage.onerror = function (err) {
      reject(`读取海报背景图异常`);
    }
      
  });  
}

export function durationFormat(duration:number):string{
  if(isNaN(Number(duration))){
    return "-"
  }
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = duration % 60;
  return `${hours?`${hours}:`:''}${minutes<10?'0':''}${minutes}:${seconds<10?'0':''}${seconds}`;
}

export function durationFormatC(duration:number):string{
  if(isNaN(Number(duration))){
    return "-"
  }
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = duration % 60;
  const minute = `${minutes<10?'0':''}${minutes}`
  return `${hours?`${hours}时`:''}${minute!='00'?`${minute}分`:''}${seconds<10?'0':''}${seconds}秒`;
}

export function createTimeFormat(start:string,end:string){
  let starts = dayjs(start).format("YYYY-MM-DD")
  let ends = dayjs(end).format("YYYY-MM-DD")
  let Timestr = ''
  if(starts == ends){
      let startH = dayjs(start).format("HH:mm")
      let endH = dayjs(end).format("HH:mm")
      Timestr = dayjs(start).format("MM月DD日")+' '+startH+'-'+endH
  }else{
      let startY = dayjs(start).format("YYYY")
      let endY = dayjs(end).format("YYYY")
      if(startY == endY){
          startY = dayjs(start).format("MM月DD日")
          endY = dayjs(end).format("MM月DD日")
          Timestr = startY+'-'+endY
      }else{
        Timestr = start+'-'+end
      }
     
      
  }
  return Timestr
}

export function downloadBase64Image(base64:string,fileName:string='download'){
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = base64;
  link.download = `${fileName}.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export function base64ToFile(base64String:string,fileName='image.png'){
   // 提取 MIME 类型
   const mimeType = base64String.match(/^data:(.+);base64,/)[1];
    
   // 将 Base64 字符串中的前缀去掉
   const byteString = atob(base64String.split(',')[1]);
   
   // 创建一个 ArrayBuffer 并将 Base64 解码的二进制数据写入
   const ab = new ArrayBuffer(byteString.length);
   const ia = new Uint8Array(ab);
   for (let i = 0; i < byteString.length; i++) {
       ia[i] = byteString.charCodeAt(i);
   }
   
   // 创建一个 Blob 对象
   const blob = new Blob([ab], { type: mimeType });
   
   // 将 Blob 对象转换为 File 对象
   const file = new File([blob], fileName, { type: mimeType });

   return file;
}

//字节转换单位
export const transformByteSize = (byte: number) => {
  const k = 1024
  if (byte < k) return byte + 'B'
  if (byte < k * k) return Math.round(byte / k) + 'KB'
  if (byte < k * k * k) return Math.round(byte / (k * k)) + 'MB'
  if (byte < k * k * k * k) return Math.round(byte / (k * k * k)) + 'GB'
  return Math.round(byte / (k * k * k * k)) + 'TB'
}

//获取文件后缀
export function getFileExtension(filename: string) {
  return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2);
}