import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import { showToast } from "vant";
import { ReturnOrderStatusEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { getReturnOrderList, confirmReturn } from "@/services/storeApi";

export default function useGetReturnOrder() {
  const scope = effectScope();
  const isPageLoadingRef = ref(false);
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 退货订单数据 */
  const storeReturnOrderList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });
  /** 选中退货订单 */
  const checkedReturnOrderListId = ref([]);

  /** 退货订单状态 */
  const returnOrderStatusList = [
    {
      label: "待退货",
      value: ReturnOrderStatusEnum.PENDING_RETURN,
    },
    {
      label: "已锁定",
      value: ReturnOrderStatusEnum.LOCKED,
    },
    {
      label: "已完成",
      value: ReturnOrderStatusEnum.COMPLETED,
    },
  ];
  /** tab */
  const activeTabRef = ref(ReturnOrderStatusEnum.PENDING_RETURN);

  /** 初始化参数 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    const returnToPlatformStatus = activeTabRef.value;
    return {
      data: {
        returnToPlatformStatus,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取退货订单列表数据 */
  async function _getReturnOrderList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getReturnOrderList(_params);

      // 更新订单列表
      if (current === 1) {
        storeReturnOrderList.value = records;
      } else if (records.length) {
        storeReturnOrderList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 数据初始化 */
  async function initStoreReturnOrderList() {
    isPageLoadingRef.value = true;
    ininParams();
    await _getReturnOrderList();
    isPageLoadingRef.value = false;
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      _getReturnOrderList();
    }
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    initStoreReturnOrderList();
  }

  /** 确认退货 */
  async function confirmReturnOrder() {
    try {
      let _params = {
        idList: checkedReturnOrderListId.value
      };
      await confirmReturn(_params);
      showToast("确认退货成功");
      initStoreReturnOrderList();
    } catch (error) {
      createMessageError(`确认退货失败：${error}，请稍后重试`);
    }
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    /** 监听 */
    watch(
      () => activeTabRef.value,
      newVal => {
        initStoreReturnOrderList();
        checkedReturnOrderListId.value = [];
      },
      {
        immediate: true,
      },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    isPageLoadingRef,
    activeTabRef,
    returnOrderStatusList,
    storeReturnOrderList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    checkedReturnOrderListId,
    onRefresh,
    onLoad,
    initStoreReturnOrderList,
    confirmReturnOrder
  };
}
