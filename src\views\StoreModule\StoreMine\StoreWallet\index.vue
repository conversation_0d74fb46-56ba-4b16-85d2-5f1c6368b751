<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh">
    <div class="store_wallet_wrapper">
      <div class="store_wallet_bg" :style="backgroundStyle"></div>
      <div class="store_wallet_content">
        <div class="store_wallet_balance">
          <div class="left">
            <!-- 可提现余额 -->
            <div class="store_wallet_balance_title">可提现余额</div>
            <div class="store_wallet_balance_num">
              <span class="pre">￥</span>
              <span class="money">
                {{ accountBalance }}
              </span>
            </div>
          </div>
          <div class="right">
            <!-- 提现 -->
            <div class="store_wallet_balance_btn" @click="handleToWithdraw">提现</div>
            <!-- 提现记录 -->
            <div class="store_wallet_balance_record" @click="handleToWithdrawRecord">提现记录</div>
          </div>
        </div>
        <!-- 收支流水 -->
        <div class="store_wallet_record">收支流水</div>
        <!-- 内容 -->
        <div style="flex: 1; background: #FFFFFF; border-radius: 12px;">
          <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="van_pull_refresh_content">
            <template v-if="storeWalletFlowList.length">
              <VanList
                v-model:loading="isLoadingRef"
                :finished="isFinishedRef"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <StoreWalletCard v-for="item in storeWalletFlowList" :key="item?.id" :walletFlow="item" />
              </VanList>
            </template>
            <template v-else>
              <EmptyData style="min-height: 400px;" />
            </template>
          </VanPullRefresh>
        </div>
      </div>
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { showToast } from "vant";
import useStoreWallet from "./hooks/useStoreWallet";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import useAccountBalance from "@/views/StoreModule/StoreMine/hooks/useAccountBalance";
/**  静态资源 */
import storeWalletBg from "@/assets/storeImage/storeMine/walletBg.png";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreWalletCard from "./components/StoreWalletCard.vue";

defineOptions({ name: "StoreWallet" });

const { routerPushByRouteName } = useRouterUtils();
const {
    isPageLoadingRef,
    storeWalletFlowList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onLoad,
    onRefresh,
    initStoreWalletFlowList
} = useStoreWallet();
const { accountBalance, getAccountBalance } = useAccountBalance();

/** 点击提现 */
function handleToWithdraw() {
  if (accountBalance.value <= 0) {
    showToast("暂无提现余额");
    return;
  }
  routerPushByRouteName(RoutesName.StoreWithdraw);
}

/** 点击提现记录 */
function handleToWithdrawRecord() {
  routerPushByRouteName(RoutesName.StoreWithdrawRecord);
}

/** 背景图 */
const backgroundStyle = computed(() => {
  return {
    background: `url(${storeWalletBg}) no-repeat`,
    backgroundSize: '100% 100%'
  }
});

onMounted(async () => {
  await getAccountBalance();
  await initStoreWalletFlowList();
});
</script>

<style lang="less" scoped>
.store_wallet_wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .store_wallet_bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    z-index: 1;
  }
  .store_wallet_content {
    width: 100%;
    height: 100%;
    z-index: 2;
    padding: 12px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 12px);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .store_wallet_balance {
        width: 100%;
        height: 100px;
        background-image: url("@/assets/storeImage/storeMine/wallet.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-color: transparent;
        margin-top: 26px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 24px;
        box-sizing: border-box;
        .left {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            .store_wallet_balance_title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 14px;
                color: #FFFFFF;
                line-height: 22px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .store_wallet_balance_num {
                font-family: Bebas Neue, Bebas Neue;
                font-weight: 400;
                font-size: 24px;
                color: #FFFFFF;
                line-height: 32px;
                text-shadow: 0px 1px 0px #FE8E90;
                text-align: left;
                font-style: normal;
                text-transform: none;
                .pre {
                    font-size: 18px;
                }
            }
        }
        .right {
            display: flex;
            flex-direction: column;
            gap: 8px;
            .store_wallet_balance_btn {
                width: 72px;
                height: 32px;
                background: linear-gradient( 94deg, #FFFFFF 0%, #F4F9FF 100%);
                box-shadow: 0px 4px 6px 0px rgba(248,208,208,0.52);
                border-radius: 99px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #EF1115;
                line-height: 22px;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
            .store_wallet_balance_record {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #FFFFFF;
                line-height: 22px;
                text-align: left;
                font-style: normal;
                text-transform: none;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
    .store_wallet_record {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin: 28px 0px 8px 4px;
    }
    .van_pull_refresh_content {
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
        padding: 12px 16px;
    }
  }
}
</style>
