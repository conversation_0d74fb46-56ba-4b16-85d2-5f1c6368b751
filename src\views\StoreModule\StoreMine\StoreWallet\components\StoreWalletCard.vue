<template>
  <div class="store_wallet_card_wrapper">
    <div class="store_wallet_header">
      <div class="store_wallet_header_title">{{ recordType }}</div>
      <div class="store_wallet_header_right">
        {{`${isIncome ? '' : '-'}￥${((walletFlowRef?.amount ?? 0) / 100).toFixed(2)}`}}
      </div>
    </div>
    <div class="store_wallet_reason_wrapper">
      <div class="store_wallet_header_reason">{{walletFlowRef?.recordDetail ?? '-'}}</div>
      <div class="store_wallet_header_time">{{`时间：${walletFlowRef?.createTime ?? '-'}`}}</div>
    </div>
    <!-- 线 -->
    <div class="store_wallet_line"></div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, computed } from "vue";
import { FlowTypeEnum, FlowDirectionEnum } from "@/views/StoreModule/enums";

defineOptions({ name: "StoreWalletCard" });

/** props */
const props = defineProps<{
    walletFlow: {
        recordType?: FlowTypeEnum;
        recordDetail?: string;
        amount?: number;
        ioType?: FlowDirectionEnum;
        createTime?: string;
    }
}>();

const { walletFlow: walletFlowRef } = toRefs(props);

/** 收支流水记录类型 */
const recordTypeMap = {
    [FlowTypeEnum.COMMISSION_SETTLEMENT]: "分佣结算",
    [FlowTypeEnum.WITHDRAW]: "提现",
    [FlowTypeEnum.WITHDRAW_REJECTED]: "提现驳回",
};
const recordType = computed(() => recordTypeMap[walletFlowRef.value?.recordType]);

/** 收入与支出判断 */
const isIncome = computed(() => walletFlowRef.value?.ioType === FlowDirectionEnum.INCOME);
</script>

<style lang="less" scoped>
.store_wallet_card_wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
    .store_wallet_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .store_wallet_header_title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_wallet_header_right {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 26px;
            text-align: right;
            font-style: normal;
            text-transform: none;
        }
    }
    .store_wallet_reason_wrapper {
        display: flex;
        flex-direction: column;
        gap: 2px;
        .store_wallet_header_reason,
        .store_wallet_header_time {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
    .store_wallet_line {
        width: 100%;
        height: 1px;
        background: #f0f0f0;
        margin-top: 12px;
    }
}
</style>
