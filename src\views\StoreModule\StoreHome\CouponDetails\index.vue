<template>
  <div class="wrapper">
    <!-- header -->
    <div class="header-container">
      <div class="title">{{`${categoryNameRef ?? '福利券'}·`}}</div>
      <p class="sub-title">{{`${exchangeNoteRef ?? '-'}`}}</p>
    </div>
    <!-- tabs -->
    <VanTabs
      :style="{ '--van-tabs-line-height': '40px', height: '40px' }"
      v-model:active="activeTabRef"
      color="#EF1115"
      title-active-color="#EF1115"
      title-inactive-color="#333333"
      line-width="36px"
      line-height="2px"
      swipeable
    >
      <VanTab v-for="item in couponStatusOptions" :key="item.value" :name="item.value">
        <template #title>
          <span class="tab-title" :class="{ 'tab-active': activeTabRef === item.value }">{{ item.label }}</span>
        </template>
      </VanTab>
    </VanTabs>
    <div class="coupon-details-container">
      <JLoadingWrapper :show="isPageLoadingRef">
        <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="coupon-content">
          <template v-if="couponDetailsList.length">
            <VanList v-model:loading="isLoadingRef" :finished="isFinishedRef" finished-text="没有更多了">
              <CouponDetailsCard
                v-for="item in couponDetailsList"
                :key="item.id"
                :couponInfo="item"
                :couponStatus="activeTabRef"
              />
            </VanList>
          </template>
          <template v-else>
            <EmptyData style="width: 100%; height: 100%;" />
          </template>
        </VanPullRefresh>
      </JLoadingWrapper>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, toRefs, computed } from "vue";
import useGetCoupDetails from "./hooks/useGetCoupDetails";
import { CouponStatusEnum, StoreCouponRouteTypeEnum } from "@/views/StoreModule/enums";
/** 相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import CouponDetailsCard from "./components/CouponDetailsCard.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

defineOptions({ name: 'CouponDetails' });

/** props */
const props = defineProps<{
  type: StoreCouponRouteTypeEnum;
  userId: string;
  categoryId: string;
  useStatus: CouponStatusEnum;
  categoryName: string;
  exchangeNote: string;
}>();

const { categoryName: categoryNameRef, exchangeNote: exchangeNoteRef } = toRefs(props);

/** 福利券状态 */
const couponStatusOptions = [
  {
    label: '待使用',
    value: CouponStatusEnum.NOT_USED,
  },
  {
    label: '已使用',
    value: CouponStatusEnum.USED,
  },
  {
    label: '已失效',
    value: CouponStatusEnum.EXPIRED,
  },
];

const {
  isPageLoadingRef,
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  couponDetailsList,
  activeTabRef,
  onRefresh,
  onLoad,
  getCouponDetails
} = useGetCoupDetails({
  type: props.type,
  userId: props.userId,
  categoryId: props.categoryId,
  useStatus: props.useStatus,
});

/** 监听 */
watch(
  () => activeTabRef.value,
  (newVal) => {
    couponDetailsList.value = [];
    getCouponDetails();
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
.wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  :deep(.van-tabs__line) {
    bottom: 18px;
  }
  .header-container {
    width: 100%;
    height: 150px;
    background: url(@/assets/storeImage/storeHome/couponBg.png) no-repeat;
    background-size: 100% 100%;
    padding: 32px;
    box-sizing: border-box;
    .title {
      height: 50px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 36px;
      color: #FFFFFF;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
    }
    .sub-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255,255,255,0.85);
      text-align: left;
      font-style: normal;
      text-transform: none;
      line-height: 22px;
    }
  }
  .tab-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .tab-active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #EF1115;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .coupon-details-container {
    height: calc(100% - 150px - 40px);
    .coupon-content {
      height: 100%;
      padding: 12px;
      box-sizing: border-box;
      overflow-y: auto;
    }
  }
}
</style>
