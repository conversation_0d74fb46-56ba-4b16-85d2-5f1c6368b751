<template>
  <div class="store_export_record_card_wrapper">
    <div class="header_wrapper">
      <img :src="dataReportIcon" alt="" />
      <span>{{ exportRecordRef?.fileName ?? '-' }}</span>
    </div>
    <!-- 信息 -->
    <div class="store_export_record_info">
      <div class="store_export_record_info_item">
        导出时间：
        <span>{{ exportRecordRef?.createTime ?? '-' }}</span>
      </div>
      <div class="store_export_record_info_item">
        总数据数：
        <span>{{ exportRecordRef?.recordCount ?? 0 }}</span>
      </div>
      <div class="store_export_record_info_item">
        文件大小：
        <span>{{`${formatBytesToKB(exportRecordRef?.fileSize ?? 0)}KB`}}</span>
      </div>
    </div>
    <!-- footer -->
    <div v-if="isShowDownloadBtn" class="footer">
      <!-- 点击下载 -->
      <VanButton type="danger" size="small" @click="emit('clickDownload')">
        <template #icon>
          <SvgIcon name="download" style="font-size: 18px;" />
        </template>
        点击下载
      </VanButton>
    </div>
    <!-- 状态 -->
    <!-- <div class="store_export_record_status" :style="{backgroundColor: currentStatusInfo?.backgroundColor ?? '#F8F8F8' }">
      <span>{{ currentStatusInfo?.label ?? '未知' }}</span>
    </div> -->
  </div>
</template>

<script lang="ts" setup>
import { computed, toRefs } from "vue";
import { StoreDataExportEnum, ExportStatusEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import dataReportIcon from "@/assets/storeImage/storeMine/dataReportIcon.png";

defineOptions({ name: "StoreExportRecordCard" });

/** props */
const props = defineProps<{
  exportRecord: {
    type?: StoreDataExportEnum;
    id?: string;
    fileName?: string;
    fileSize?: number;
    createTime?: string;
    status?: ExportStatusEnum;
    recordCount?: number;
  };
}>();

/** emit */
const emit = defineEmits<{
  /** 点击下载 */
  (e: "clickDownload"): void;
}>();

const { exportRecord: exportRecordRef } = toRefs(props);

/** 导出状态 */
const ExportStatusEnumMap = {
  [ExportStatusEnum.PENDING_EXPORT]: {
    backgroundColor: "#4D8AFF",
    label: "未导出",
  },
  [ExportStatusEnum.EXPORTING]: {
    backgroundColor: "#4DA4FF",
    label: "执行中",
  },
  [ExportStatusEnum.EXPORT_SUCCESS]: {
    backgroundColor: "#4BE092",
    label: "执行完成",
  },
  [ExportStatusEnum.EXPORT_FAILED]: {
    backgroundColor: "#EF1115",
    label: "执行失败",
  }
}

/** 当前状态 */
const currentStatusInfo = computed(() => {
  return ExportStatusEnumMap[exportRecordRef.value.status];
});

/** 是否显示点击下载按钮 */
const isShowDownloadBtn = computed(() => {
  return exportRecordRef.value.status === ExportStatusEnum.EXPORT_SUCCESS;
});

/** 字节转换 */
function formatBytesToKB(bytes, standard = 'binary') {
  const divisor = standard === 'binary' ? 1024 : 1000;
  const kb = bytes / divisor;
  
  return kb.toFixed(2);
}
</script>

<style lang="less" scoped>
.store_export_record_card_wrapper {
    width: 100%;
    background: #FFFFFF;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;
    .header_wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        img {
            width: 24px;
            height: 24px;
        }
        span {
            flex: 1;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
    .store_export_record_info {
        width: 100%;
        background: #F8F8F8;
        border-radius: 8px;
        padding: 8px 12px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 12px;
        .store_export_record_info_item {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            span {
                color: #333333;
            }
        }
    }
    .footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;
        margin-top: 12px;
        :deep(.van-button__text) {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 14px;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
    .store_export_record_status {
        position: absolute;
        top: 0;
        right: 0;
        padding: 4px 12px;
        border-top-right-radius: 12px;
        border-bottom-left-radius: 12px;
        span {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #FFFFFF;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
}
</style>
