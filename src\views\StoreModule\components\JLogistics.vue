<template>
    <van-popup
      v-model:show="showRef"
      round
      closeable
      close-icon="close"
      position="bottom"
      @close="closeFn"
      style="height: 80%;"
    >
      <div class="store_logistics_wrapper">
        <!-- 标题 -->
        <div class="store_logistics_title">物流信息</div>
        <!-- 快递信息 -->
        <div class="store_logistics_info">
          <div class="store_logistics_info_left">{{ shipCompanyNameRef }}</div>
          <div class="store_logistics_info_right">
            <span>{{ trackingNoRef }}</span>
            <img
              v-if="trackingNoRef"
              class="store_logistics_info_copy"
              :src="CopySrc"
              alt=""
              @click.stop="handleCopyID(trackingNoRef)"
            />
          </div>
        </div>
        <!-- 内容 -->
        <div class="store_logistics_content">
          <div v-for="item,index in shipTracesListRef" :key="item.acceptTime" class="store_logistics_content_item">
            <!-- 点 -->
            <div class="dot" :class="{'dot_active':index == 0}"></div>
            <!-- 信息 -->
            <div class="store_logistics_content_item_info">
              <div class="store_logistics_info_newest_content_title">
                <!-- 状态 -->
                <span v-if="logisticsStatusMap[item?.state] && index <= 2" class="state" :class="{'state_active':index == 0}">{{ logisticsStatusMap[item?.state] }}</span>
                <!-- 时间 -->
                <span class="time">{{ item?.acceptTime }}</span>
              </div>
              <p class="store_logistics_info_newest_content_time">
                {{ item?.acceptStation }}
              </p>
            </div>
          </div>
        </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, toRefs } from 'vue';
import { showToast } from "vant";
import { copyText } from "@/utils/clipboardUtils";
import { LogisticsStatusEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import CopySrc from "@/assets/image/member/copy.png";

defineOptions({ name: 'JLogistics' });

interface ShipTrace {
  orderCode?: string;
  trackingNo?: string;
  shipCompanyCode?: string;
  state?: LogisticsStatusEnum;
  acceptStation?: string;
  acceptTime?: string;
  location?: string;
  station?: string;
  stationTel?: string;
  stationAdd?: string;
  deliveryMan?: string;
  deliveryManTel?: string;
}

/** Props */
const props = defineProps<{
  show: boolean;
  shipTracesList: ShipTrace[];
  trackingNo?: string;
  shipCompanyName?: string;
}>();

/** Emits */
const emits = defineEmits<{
  (e: 'update:show', show: boolean): void;
}>();

const { shipTracesList: shipTracesListRef, shipCompanyName: shipCompanyNameRef, trackingNo: trackingNoRef } = toRefs(props);

const showRef = computed({
  get() {
    return props.show;
  },
  set(show: boolean) {
    emits('update:show', show);
  }
});

/** 关闭弹窗 */
const closeFn = () => {
  emits('update:show', false);
};

/**
 * @description 物流状态映射表
 */
 const logisticsStatusMap = {
  [LogisticsStatusEnum.NO_TRACKING]: "暂无轨迹信息",
  [LogisticsStatusEnum.COLLECTED]: "已揽收",
  [LogisticsStatusEnum.IN_TRANSIT]: "运输中",
  [LogisticsStatusEnum.DELIVERED]: "已签收",
  [LogisticsStatusEnum.PROBLEM]: "问题件",
  [LogisticsStatusEnum.FORWARDED]: "转寄中",
  [LogisticsStatusEnum.CUSTOMS]: "清关中",
};

/** 复制快递单号 */
function handleCopyID(data) {
  try {
    copyText(data);
    showToast('快递单号成功');
  }
  catch (e) {
    showToast('快递单号失败');
  }
}
</script>

<style lang="less" scoped>
.store_logistics_wrapper {
  width: 100%;
  height: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .store_logistics_title {
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    padding: 12px 12px 0px 12px;
  }
  .store_logistics_info {
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    box-sizing: border-box;
    .store_logistics_info_left {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .store_logistics_info_right {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      gap: 4px;
      .store_logistics_info_copy {
        width: 16px;
        height: 16px;
      }
    }
  }
  .store_logistics_content {
    flex: 1;
    padding: 12px 16px;
    overflow-y: auto;
    .store_logistics_content_item {
      border-left: 1px solid rgba(0,0,0,0.08);
      position: relative;
      .dot {
        position: absolute;
        top: -2px;
        left: -5px;
        width: 8px;
        height: 8px;
        background: #cccccc;
        border-radius: 50%;
        transform: translateZ(0);
      }
      .dot_active {
        background-color: #4DA4FF;
      }
      .store_logistics_content_item_info {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 18px 12px 26px 10px;
        .store_logistics_info_newest_content_title {
          display: flex;
          align-items: center;
          gap: 4px;
          position: absolute;
          top: -6px;
          .state {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 15px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .state_active {
            color: #4DA4FF;
          }
          .time {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
        .store_logistics_info_newest_content_time {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          text-align: justified;
          font-style: normal;
          text-transform: none;
          line-height: 18px;
        }
      }
    }
  }
}
</style>