/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BannerContainer: typeof import('./src/components/BannerContainer/index.vue')['default']
    JBackTop: typeof import('./src/components/JBackTop/index.vue')['default']
    JCrossPlatformTabs: typeof import('./src/components/JCrossPlatformTabs/index.vue')['default']
    JEmptyData: typeof import('./src/components/JEmptyData/index.vue')['default']
    JLoadingWrapper: typeof import('./src/components/JLoadingWrapper/index.vue')['default']
    JTag: typeof import('./src/components/JTag/index.vue')['default']
    LoadLoading: typeof import('./src/components/LoadLoading/index.vue')['default']
    OutOfStockOverlay: typeof import('./src/components/Overlay/OutOfStockOverlay.vue')['default']
    Overlay: typeof import('./src/components/Overlay/index.vue')['default']
    PresOverlay: typeof import('./src/components/Overlay/PresOverlay.vue')['default']
    Tabbar: typeof import('./src/components/Tabbar/index.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanBadge: typeof import('vant/es')['Badge']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanLoading: typeof import('vant/es')['Loading']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
  }
}
