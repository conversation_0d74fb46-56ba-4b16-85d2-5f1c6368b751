import { TAxios } from "@/utils/http/Axios/index.js";
import { getApiUrlPrefix, getRedirectUrl } from "@/utils/http/urlUtils";
import type { RequestOptions } from "@/utils/http/Axios/type";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import axios from "axios";
import { isObject } from "@/utils/isUtils";

const requestOptions: RequestOptions = {
  withToken: true,
  isRetry: false,
  isQueryParams: false,
  isReturnRawResponse: false,
  errorMsgMode: "message",
  requestContentType: "json",
  responeseType: "json",
};

export const defHttp: TAxios = {} as TAxios;
export async function initDefHttp() {
  const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix);
  try {
    const resp = await axios.get(`${getRedirectUrl()}/apiDomainV2`);
    const _obj = resp.data.data;
    if (isObject(_obj)) {
      apiPrefixStorage.set(_obj);
    } else {
      apiPrefixStorage.remove();
    }
  } catch (e) {
    apiPrefixStorage.remove();
  }
  const _TAxios = new TAxios({
    withCredentials: false,
    timeout: 60000,
    baseURL: getApiUrlPrefix(),
    requestOptions,
  });
  Object.setPrototypeOf(defHttp, Object.getPrototypeOf(_TAxios));
  Object.assign(defHttp, _TAxios);
}

export function getDefHttp() {
  return defHttp;
}
