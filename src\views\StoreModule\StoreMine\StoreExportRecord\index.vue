<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="store_data_export-content">
      <template v-if="storeExportRecordList.length">
        <VanList v-model:loading="isLoadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
          <StoreExportRecordCard
            v-for="item in storeExportRecordList"
            :key="item.id"
            :exportRecord="item"
            @clickDownload="onDownload(item)"
          />
        </VanList>
      </template>
      <template v-else>
        <EmptyData style="min-height: 400px;" />
      </template>
    </VanPullRefresh>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import useGetStoreExportRecord from "./hooks/useGetStoreExportRecord";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { useMessages } from "@/hooks/useMessage";
import { downloadRecord } from "@/services/storeApi";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreExportRecordCard from "./components/StoreExportRecordCard.vue";

defineOptions({ name: "StoreExportRecord" });

const { routerPushByRouteName } = useRouterUtils();
const { createMessageError } = useMessages();
const {
  isPageLoadingRef,
  refreshingRef,
  onRefresh,
  storeExportRecordList,
  isLoadingRef,
  isFinishedRef,
  onLoad,
  initStoreExportRecordList
} = useGetStoreExportRecord();

/** 下载文件 */
 async function onDownload(fileInfo) {
  // 跳转前查询文件下载地址
  try {
    isPageLoadingRef.value = true;
    const resp = await downloadRecord({ id: fileInfo?.id });
    if (resp) {
      routerPushByRouteName(RoutesName.StoreFileDownload, { fileName: fileInfo?.fileName, fileUrl: resp });
    }
  } catch (error) {
    createMessageError("查询文件下载地址失败：" + error);
  } finally {
    isPageLoadingRef.value = false;
  }
}

onMounted(() => {
  initStoreExportRecordList();
});
</script>

<style lang="less" scoped>
.store_data_export-content {
  height: 100%;
  padding: 12px 10px;
  box-sizing: border-box;
  overflow-y: auto;
}
img {
  width: 28px;
  height: 28px;
}
</style>
