import { CacheConfig } from "@/utils/cache/config";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { createDummyId, timeoutPromise } from "@/utils/commonUtils";
import { isLoginQWEnv } from "@/utils/envUtils";




export const enum MessageEventEnum{
    /**登录 */
    Login = 0,
    /**分享卡片消息到当前会话 */
    ShareCard,
    /** 获取本地图片接口 */
    ChooseImage,
    /** 上传图片接口 */
    UploadImage,
    /** 获取当前外部联系人userid */
    GetCurExternalContact,
    /** 获取当前客户群的群ID */
    GetCurExternalChat,
    /** 获取当前打开应用的环境 */
    GetContext,
    /**分享消息到当前会话 */
    ShareMessage,
    getDpDomain = 'originUrl',
    sendUrlToWindow = 4002
}

const EventMap:Map<MessageEventEnum,Array<Function>> = new Map()


interface EventParams{
    type:MessageEventEnum,
    params?:{},
    success?:(args?:any)=>void,
    fail?:(args?:any)=>void,
    complete?:()=>void,
}


export function useWindowMessage(){
    let FatherOrigin = 'https://corp.jiutiansoft.com';

    function getFatherOrigin(){
        if(isLoginQWEnv()){
            FatherOrigin = 'https://corp.jiutiansoft.com'
        }
        else{
            const FatherOriginStoreage = createCacheStorage(CacheConfig.FatherOrigin)
            const _cahce = FatherOriginStoreage.get()
            if(_cahce){
                FatherOrigin = _cahce as string
            }
            else{
                FatherOrigin = 'https://corp.jiutiansoft.com'
            }
        }
       
    }
    function initWindowMessage(){

    }
    function destoryWindowMessage(){
        EventMap.clear()
    }
    function messageHandler(){
        getFatherOrigin()
        window.addEventListener('message',(event)=>{
            if (event.origin !== FatherOrigin) return;
            const resp = JSON.parse(event.data)
            if(resp.type && EventMap.has(resp.type)){
                const callBacks = EventMap.get(resp.type) || []
                callBacks.forEach((callBack)=>{
                    if(typeof callBack === 'function'){
                        callBack(resp.value)
                    }
                })
            }

        })
    }
    function pushEventMap(key:MessageEventEnum,callBack:Function){
        if(!EventMap.has(key)){
            EventMap.set(key,[])
        }
        const callBacks = EventMap.get(key) || []
        if(!callBacks.includes(callBack)){
            callBacks.push(callBack)
            EventMap.set(key,callBacks)
        }
    }
    function sendMessageToWindows(key:MessageEventEnum,params:any){
        getFatherOrigin()
        const props={
            type:key,
            value:params
        }
        window.parent.postMessage(JSON.stringify(props), FatherOrigin);
    }
    function waitQWSdkResponse(eventParams:EventParams){
        getFatherOrigin()
        const uuid = createDummyId()
        const props={
            type:eventParams.type,
            params:eventParams.params,
            uid:uuid
        }
        return new Promise((resolve,reject)=>{
            window.parent.postMessage(JSON.stringify(props), FatherOrigin);
            window.addEventListener('message',(event)=>{
                if (event.origin !== FatherOrigin) return;
                else{
                    const resp = JSON.parse(event.data)
                    if(resp.uid == uuid){
                        console.log(resp,'resp');
                        if(resp.result == 'success'){
                            eventParams.success(resp.resp)
                            resolve(true)
                        }
                        else if(resp.result == 'error'){
                            console.log(resp,'error');
                            eventParams.fail(resp.resp)
                            resolve(true)
                        }
                        
                    }
                }
            })
        })
    }

    function listenQWSdkEvent(eventParams:EventParams,timeout=1000){
        return Promise.race([waitQWSdkResponse(eventParams),timeoutPromise(timeout)])
    }
    return {
        listenQWSdkEvent,
        initWindowMessage,
        destoryWindowMessage,
        pushEventMap,
        sendMessageToWindows
    }











}