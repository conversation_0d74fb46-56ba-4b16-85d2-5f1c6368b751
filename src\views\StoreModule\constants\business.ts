import { transformObjectToOption } from "./_shared";

/** 基础设置 */
declare namespace BasicSetting {
  /**
   *0:非售后状态,
   *1:待商家受理,
   *2:客户撤回申请,
   *3:商家拒绝退款,
   *4:待客户退货,
   *5:待商家收货,
   *6:退货退款关闭,
   *7:退款中,
   *8:待打款,
   *9:退款完成,
   *10:商家同意取消订单,
   *11:商家拒绝取消订单,
   */
  type afterSalesStatusTypeKey = NonNullable<0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11>;
}

/** 售后状态 */
export const afterSalesStatusLabels: Record<BasicSetting.afterSalesStatusTypeKey, string> = {
  0: "非售后状态",
  1: "待商家受理",
  2: "客户撤回申请",
  3: "商家拒绝退款",
  4: "待客户退货",
  5: "待商家收货",
  6: "退货退款关闭",
  7: "退款中",
  8: "待打款",
  9: "退款完成",
  10: "商家同意取消订单",
  11: "商家拒绝取消订单",
};
export const afterSalesStatusOptions = transformObjectToOption(afterSalesStatusLabels, true);
