<template>
  <van-config-provider :theme-vars="theme">
    <view class="default-layout" :style="{ ...props.customStyle }">
      <slot></slot>
    </view>
  </van-config-provider>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import LoginBackground from '@/static/images/storeLogin/loginBg.png'
/** props */
const props = withDefaults(
  defineProps<{
    themeVars?: object
    customStyle?: object
  }>(),
  {
    themeVars: () => ({}),
    customStyle: () => ({}),
  },
)

/** 自定义页面主题 */
const theme = computed(() => {
  return {
    // rateIconFullColor: '#07c160',
    // sliderBarHeight: '4px',
    // sliderButtonWidth: '20px',
    // sliderButtonHeight: '20px',
    // sliderActiveBackgroundColor: '#07c160',
    // buttonPrimaryBorderColor: '#07c160',
    overlayBackground: 'rgba(0, 0, 0, 0.1)',
    ...props.themeVars,
  }
})
</script>

<style lang="scss" scoped>
.default-layout {
  width: 100vw;
  min-height: 100vh;
}
</style>
