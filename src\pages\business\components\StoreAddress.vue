<template>
  <view class="store-address-wrapper">
    <image :src="storeAvatar" alt="门店头像" class="store-profile-picture" />
    <!-- 门店信息 -->
    <view class="store-info">
      <view class="store-name">{{ storeInfoRef?.storeName ?? '-' }}</view>
      <view class="store-address">
        <image :src="addressSrc" alt="" class="store-address-icon" />
        <text class="store-address-text">{{ formattedAddress }}</text>
      </view>
      <!-- 电话 -->
      <view class="store-phone">
        <image :src="phoneSrc" alt="" class="store-phone-icon" />
        <text class="store-phone-text" @click.stop>
          {{ storeInfoRef?.contactPhone ? storeInfoRef.contactPhone : '暂无电话' }}
        </text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { toRefs, computed } from 'vue'
import { StoreStatusEnum } from '@/enum'
/** 静态资源 */
import addressSrc from '@/static/images/storeHome/address.png'
import phoneSrc from '@/static/images/storeHome/phone.png'
import storeSrc from '@/static/images/storeUser/store.png'

defineOptions({ name: 'StoreAddress' })

/** props */
const props = defineProps<{
  storeInfo: {
    storeName: string
    storeAvatar: string
    storeStatus: StoreStatusEnum
    province: string
    city: string
    area: string
    addressDetail: string
    contactPhone?: string
  }
}>()

const { storeInfo: storeInfoRef } = toRefs(props)

/** 门店地址 */
const formattedAddress = computed(() => {
  const { province = '', city = '', area = '', addressDetail = '' } = storeInfoRef.value || {}
  const address = `${province}${city}${area}${addressDetail}`.trim()
  return address || '暂无地址'
})

/** 门店头像 */
const storeAvatar = computed(() => {
  const { storeAvatar } = storeInfoRef.value || {}
  return storeAvatar || storeSrc
})
</script>

<style lang="scss" scoped>
.store-address-wrapper {
  width: 100%;
  padding: 16rpx 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  border-radius: 16rpx;
  background: linear-gradient(180deg, #fff3ea 0%, #ffffff 100%);
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  .store-profile-picture {
    width: 112rpx;
    height: 112rpx;
    border-radius: 8rpx;
  }
  .store-info {
    flex: 1;
    margin-left: 8rpx;
    display: flex;
    flex-direction: column;
    gap: 4rpx;
    .store-name {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 500;
      font-size: 36rpx;
      color: #333333;
      line-height: 52rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .store-address {
      display: flex;
      align-items: center;
      .store-address-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      .store-address-text {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 44rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .store-phone {
      display: flex;
      align-items: center;
      .store-phone-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      .store-phone-text {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 28rpx;
        color: #1677ff;
        line-height: 44rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
