<template>
    <div class="card-box" :style="{ border: borderAll ? '1px solid ##EEEEEE' : 'none' }">
        <div class="image">
            <MaskBanner :is-use-type-mask="true" :custom-style="{ borderRadius: '4px' }"
                :type="totalStock == 0 ? 'stock' : null"
                v-if="totalStock == 0">
            </MaskBanner>
            <img :lazy-load="true" class="image" :src="cardInfo.firstImg" />
        </div>
        <div class="card-content" :style="{ border: !borderAll ? '1px solid ##EEEEEE' : 'none' }">
            <GoodsTitle :state="cardInfo" :style="{ fontSize: '13px' }" />
            <div class="content-opt">
                <slot name="price">
                    <IntegralContent :numFontSize="18" :skuInfo="minSkuInfo"
                        v-if="props.type == StoreGoodsEnum.IntegralGoods">
                    </IntegralContent>
                    <PriceContent v-if="props.type == StoreGoodsEnum.Goods" :sku-info="minSkuInfo" price-key="minPrice" :price-font-size="18" />
                    <WelfareContent v-if="props.type == StoreGoodsEnum.WelfareTicket" :sku-info="minSkuInfo"></WelfareContent>
                </slot>
                <slot name="btn">
                    <van-button @click.stop="handleClick" class="btn" style="padding: 4px 14px;white-space: nowrap;" type="danger"
                        size="small" round>{{ btnText }}</van-button>
                </slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch, ref } from "vue";
import GoodsTitle from "./GoodsTitle.vue";
import IntegralContent from "./IntegralContent.vue"
import PriceContent from "./PriceContent.vue"
import WelfareContent from "./WelfareContent.vue";
import MaskBanner from "../components/MaskBanner/index.vue";
import { isNullOrUnDef } from "@/utils/isUtils";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { contrastMinPriceSku, filterSkuMinIntegral,filterSkuMinWelfare } from "@/utils/storeUtils";
//商品 积分商品 福利卷
const props = withDefaults(defineProps<{
    cardInfo: any,
    type:StoreGoodsEnum,
    borderAll?: boolean
}>(), {
    cardInfo: () => ({}),
    type:StoreGoodsEnum.Goods,
    borderAll: false
})
const emits = defineEmits<{
    chooseSku: [string]
}>()
const btnText = computed(() => {
    return props.type == StoreGoodsEnum.Goods ? '购买' : '兑换'
})
//最小规格数据
const minSkuInfo = computed(() => {
    let info: any = {}
    if (props.type == StoreGoodsEnum.IntegralGoods) {
        info = filterSkuMinIntegral(skuList.value)
    }
    if (props.type == StoreGoodsEnum.WelfareTicket) {
        info = filterSkuMinWelfare(skuList.value)
    }
    if(props.type == StoreGoodsEnum.Goods) {
        info = contrastMinPriceSku(skuList.value)
    }
    return {
        ...info,
        price: info?.price || 0,
        minPrice: info?.minPrice || 0,
        upper: info.upper || 0,
        availStocks: info.availStocks || info.availStock || 0
    }
})
const skuList = computed<any[]>(() => {
    let list = []
    if (props.type == StoreGoodsEnum.IntegralGoods) {
        list = props.cardInfo?.appletPointSpecDTOS || []
    }
    if (props.type == StoreGoodsEnum.WelfareTicket) {
        list = props.cardInfo?.couponProductSpecList || []
    }
    if (props.type == StoreGoodsEnum.Goods) {
        list = props.cardInfo?.appletProductSpecDTOList || []
    }
    return list
});
//获取总库存数量
const totalStock = computed(() => {
    return skuList.value.reduce((pre, cur) => {
        return pre + (cur.availStocks || cur.availStock || 0);
    }, 0);
});
const handleClick = () => {
    emits('chooseSku', props.cardInfo)
}
</script>

<style scoped lang="less">
@import url("@/styles/storeVar.less");

.card-box {
    width: 100%;
    display: flex;
    margin-bottom: 12px;
    box-sizing: border-box;

    .image {
        position: relative;
        width: 75px;
        height: 75px;
        margin-right: 8px;
        border-radius: 4px;
    }

    .card-content {
        flex: 1;
        padding-bottom: 10px;
        box-sizing: border-box;
        font-size: 14px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .content-opt {
            margin-top: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .btn {
                background-color: @error-color;
            }
        }
    }
}
</style>