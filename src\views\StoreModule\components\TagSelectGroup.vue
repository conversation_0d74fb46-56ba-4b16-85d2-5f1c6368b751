<template>
  <div class="tag_select_group_wrapper">
    <div
      v-for="range in dataRef"
      :key="range.value"
      class="tag_select_group_item"
      :class="{ 'tag_select_group_item_active': valueRef === range.value }"
      @click="changeTag(range.value)"
    >
      {{ range.label }}
    </div>
  </div>
</template>

<script setup lang="tsx">
import { toRefs, toRef } from "vue";

defineOptions({name: "TagSelectGroup"});

type valType = string | number;
type labelType = string | number;
interface TagSelectDataItem {
  label: labelType;
  value: valType;
}

/** props */
const props = withDefaults(
  defineProps<{
    data: TagSelectDataItem[];
    value?: valType;
  }>(),
  {
    data: () => [],
  }
);

/** emits */
const emits = defineEmits<{
  (e: "update:value", value: valType): void;
}>();

const { data: dataRef } = toRefs(props);
const valueRef = toRef(props, "value");

/** 切换 */
function changeTag(value: valType) {
  emits("update:value", value);
}
</script>

<style lang="less" scoped>
.tag_select_group_wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  box-sizing: border-box;
  .tag_select_group_item {
    height: 24px;
    background: #F8F8F8;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 8px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    text-align: left;
    font-style: normal;
    text-transform: none;
    box-sizing: border-box;
  }
  .tag_select_group_item_active {
    background: #FFF4F4 !important;
    color: #EF1115 !important;
  }
}
</style>
