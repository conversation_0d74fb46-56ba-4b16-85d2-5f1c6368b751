import type { PageStyle } from '@/routes/types'

export interface Page {
  /** 路由路径 */
  path: string
  /** 页面样式配置 */
  style?: {}
  /** 是否需要登录 */
  needLogin?: boolean
}

export interface easycom {
  /** 是否自动扫描组件 */
  autoscan: boolean
  /** 自定义配置 */
  custom?: {}
}

export interface tabBar {
  /** 是否自定义 tabBar */
  custom?: boolean
  /** tabBar 文字颜色 */
  color?: string
  /** tabBar 选中文字颜色 */
  selectedColor?: string
  /** tabBar 背景颜色 */
  backgroundColor?: string
  /** tabBar 边框样式 */
  borderStyle?: string
  /** tabBar 模糊效果 */
  blurEffect?: string
  /** tabBar 项列表 */
  list: tabBarList[]
  /** tabBar 位置 */
  position?: string
  /** tabBar 字体大小 */
  fontSize?: string
  /** tabBar 图标宽度 */
  iconWidth?: string
  /** tabBar 间距 */
  spacing?: string
  /** tabBar 高度 */
  height?: string
  /** 中间按钮配置 */
  midButton?: tabBarMidButton
  /** 图标字体源 */
  iconfontSrc?: string
  /** 背景图片 */
  backgroundImage?: string
  /** 背景重复方式 */
  backgroundRepeat?: string
  /** 红点颜色 */
  redDotColor?: string
}

export interface tabBarList {
  /** 页面路径 */
  pagePath: string
  /** 文字 */
  text: string
  /** 图标路径 */
  iconPath?: string
  /** 选中图标路径 */
  selectedIconPath?: string
  /** 是否可见 */
  visible?: boolean
  /** 图标字体配置 */
  iconfont?: tabBarIconfont
}

export interface tabBarMidButton {
  /** 按钮宽度 */
  width: string
  /** 按钮高度 */
  height: string
  /** 按钮文字 */
  text?: string
  /** 图标路径 */
  iconPath?: string
  /** 图标宽度 */
  iconWidth?: string
  /** 背景图片 */
  backgroundImage?: string
  /** 图标字体配置 */
  iconfont?: tabBarIconfont
}

export interface tabBarIconfont {
  /** 文字 */
  text: string
  /** 选中文字 */
  selectedText: string
  /** 字体大小 */
  fontSize?: string
  /** 颜色 */
  color?: string
  /** 选中颜色 */
  selectedColor?: string
}

export interface PagesConfig {
  /** 页面配置列表 */
  pages: Page[]
  /** 全局样式配置 */
  globalStyle?: PageStyle
  /** easycom 配置 */
  easycom?: easycom
  /** tabBar 配置 */
  tabBar?: tabBar
}
