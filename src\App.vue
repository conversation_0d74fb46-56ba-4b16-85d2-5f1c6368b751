<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useSystemStoreWithoutSetup } from './stores/modules/system'
import { useUserInfoStoreWithoutSetup } from './stores/modules/user'
import { isObject } from './utils/isUtils'
import { getLoginCode } from './utils/wxSdkUtils/account'
import { navigateTo } from './routes/utils/navigateUtils'
import { routesMap } from '@/routes/maps'
import { StorePayMode } from '@/enum'
</script>

<style lang="scss">
/*  #ifdef MP-WEIXIN  */
@import '/src/wxcomponents/vant/common/index.wxss';
@import '/src/style/vantVar.wxss';
/*  #endif  */

/* #ifdef H5 */
/* stylelint-disable-next-line selector-type-no-unknown */
uni-page-head {
  display: none;
}
/* #endif */
</style>
