<template>
  <van-config-provider :theme-vars="themeOverrides">
    <n-config-provider :theme-overrides="naiveUIThemeOverrides" :locale="zhCN" :date-locale="dateZhCN">
      <BlankLayout />
    </n-config-provider>
  </van-config-provider>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { themeOverrides,naiveUIThemeOverrides } from "@/styles/themeOverride";
import { bootstrap } from "./bootstrap";
import { stores } from "@/stores/index";
import BlankLayout from "./layout/BlankLayout.vue";
import 'vant/es/toast/style';
import 'vant/es/dialog/style';
import 'vant/es/notify/style';
import 'vant/es/image-preview/style';
import { useWindowMessage } from "./hooks/useWindowMessage";
import { NConfigProvider, zhCN, dateZhCN } from "naive-ui";
import { onBeforeMount, onMounted } from "vue";
import { isLoginQWEnv } from "./utils/envUtils";
import { useSystem } from "./hooks/useSystem";
const {init} = useSystem()
const {destoryWindowMessage,initWindowMessage} = useWindowMessage()
bootstrap({
  router: useRouter(),
  store: stores,
});
init()
onBeforeMount(()=>{
  destoryWindowMessage()
})
</script>

<style lang="less">
@import "@/styles/default.less";
body{
  touch-action: manipulation;
}
</style>
