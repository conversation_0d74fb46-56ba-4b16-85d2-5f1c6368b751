import { isString, isNullOrUnDef, isArray } from "@/utils/isUtils";
/** 防抖 */
export function _debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  } as T;
}
/**销量显示 */
export const saleComputedSum = (sale: number): string => {
  if (isNullOrUnDef(sale) || isNaN(sale)) {
    throw new Error("It can't be empty");
  }
  if (isString(sale)) {
    sale = Number(sale);
  }
  const UNIT = "+";
  const maxSize = 10000;
  const minSize = 10;
  const spaceNum = 3;
  const countAmass = [10, 100, 1000];
  const upperArr = [];
  if (sale <= minSize) {
    return minSize + UNIT;
  }
  if (sale >= maxSize) {
    return maxSize + UNIT;
  }
  if (sale > minSize && sale < maxSize) {
    for (let i = 0; i < spaceNum; i++) {
      for (let index = 1; index < minSize; index++) {
        upperArr.push(index * countAmass[i]);
      }
    }
    const diffNums = upperArr
      .map((num) => sale - num)
      .filter((num) => num >= 0);
    const minDiff = Math.min(...diffNums);
    const upper = upperArr.find((num) => sale - num === minDiff) || sale;
    return upper + UNIT;
  }
};
export function duplicateNewCopy(data: object | []) {
  if (typeof data !== "object" || data === null) {
    return data;
  }
  const result = isArray(data) ? [] : {};
  for (let key in data) {
    if (data.hasOwnProperty(key)) {
      result[key] = duplicateNewCopy(data[key]);
    }
  }
  return result;
}

/**过滤最低价格数据 */
export const filterSkuMin = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  if (list.length == 1) {
    return list[0];
  }
  const minPrice = Math.min(...list.map((item) => item.price));
  return list.find((item) => item.price === minPrice) || {};
};

/**过滤最低价格数据(对比活动价格) */
export const contrastMinPriceSku = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  let _list = JSON.parse(JSON.stringify(list));
  const isAllNotStock = _list.every((item) => !isNullOrUnDef(item.availStocks) && !(item.availStocks > 0));
  if(!isAllNotStock){
    _list = _list.filter(item=>!isNullOrUnDef(item.availStocks)&&item.availStocks>0)
  }
  _list.forEach((item) => {
    if (isNullOrUnDef(item.minPrice)) {
      item.minPrice = item.price;
      if (
        !isNullOrUnDef(item.activityPrice) &&
        item.activityPrice < item.price
      ) {
        item.minPrice = item.activityPrice;
      }
    }
  });
  const minPrice = Math.min(..._list.map((item) => item.minPrice));
  return _list.find((item) => item.minPrice === minPrice) || {};
};

/**过滤最低积分数据 */
export const filterSkuMinIntegral = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  if (list.length == 1) {
    return list[0];
  }
  const minPoints = Math.min(...list.map((item) => item.exchangePoints));
  return list.find((item) => item.exchangePoints === minPoints) || {};
};

/**过滤最低福利卷数据 */
export const filterSkuMinWelfare = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  if (list.length == 1) {
    return list[0];
  }
  const exchangeCount = Math.min(...list.map((item) => item.exchangeCount));
  return list.find((item) => item.exchangeCount === exchangeCount) || {};
};

/**已售或者已兑换数量 */
export const genSaleCount = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error("It must be an array");
  }
  if (!list.length) {
    return 0;
  }
  const sumCount = list.reduce((pre, item) => {
    const initSaled = (item.initSaled || item.soldCount) || 0;
    const soldQty = item.soldQty || 0;
    const salePrice = initSaled + soldQty;
    pre += salePrice;
    return pre;
  }, 0);
  return sumCount;
};

export interface SpecValue {
  attributeValue: string
  id?: string
  level?:number
}

export interface SpecList {
  attributeName: string
  specValue: SpecValue[]
}

/**
 * 将扁平的规格属性列表转换为按level分组的结构
 * @param flatList 扁平的规格属性列表
 * @returns 按level分组的规格列表
 */
export function transformSpecData(flatList: Array<{
  attributeName: string;
  attributeValue: string;
  id: number;
  level: number;
}>): SpecList[] {
  // 使用Map按level分组
  const groupedMap = new Map<number, {
    attributeName: string;
    specValue: Array<{
      attributeValue: string;
      id: string;
    }>;
  }>();
  
  // 遍历扁平列表进行分组
  flatList.forEach(item => {
    if (!groupedMap.has(item.level)) {
      groupedMap.set(item.level, {
        attributeName: item.attributeName,
        specValue: []
      });
    }
    
    groupedMap.get(item.level)?.specValue.push({
      attributeValue: item.attributeValue,
      id: item.id.toString()
    });
  });
  
  // 转换为目标格式，按level排序
  const result = Array.from(groupedMap.entries())
    .sort(([a], [b]) => a - b)
    .map(([level, group]) => group);
  
  return result;
}