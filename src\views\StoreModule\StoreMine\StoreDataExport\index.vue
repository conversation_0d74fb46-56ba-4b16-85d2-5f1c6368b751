<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <template v-if="storeExportDataList.length">
      <div class="store_data_export-content">
        <StoreDataExportCard
          v-for="item in storeExportDataList"
          :key="item.id"
          :dataReport="item"
          @click="handleExportClick(item)"
        />
      </div>
    </template>
    <template v-else>
      <EmptyData style="min-height: 400px;" />
    </template>
    <!-- 跳转导出记录 -->
    <VanFloatingBubble
      :offset="inviteBubbleOffestRef"
      axis="xy"
      @click="toExportRecord"
      style="background-color: transparent;border-radius: 0%;width: 52px;height: 58px;"
    >
      <img :src="DataReportSrc" alt="" class="invite-logo" />
    </VanFloatingBubble>
    <!-- 导出选择 -->
    <StoreDataExportForm v-model:show="isExportFormShowRef" :exportType="exportTypeRef" />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import useGetStoreExportData from "./hooks/useGetStoreExportData";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { useBoolean } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { StoreDataExportEnum } from "@/views/StoreModule/enums";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreDataExportCard from "./components/StoreDataExportCard.vue";
import StoreDataExportForm from "./components/StoreDataExportForm.vue";
/** 静态资源 */
import DataReportSrc from "@/assets/storeImage/storeMine/data_report.png";

defineOptions({ name: "StoreDataExport" });

const { bool: isExportFormShowRef, setTrue: openExportForm, setFalse: closeExportForm } = useBoolean();
const { isPageLoadingRef, storeExportDataList } = useGetStoreExportData();
const { routerPushByRouteName } = useRouterUtils();
const inviteBubbleOffestRef = ref({
  x: window.innerWidth - 72,
  y: window.innerHeight - 120
});

/** 跳转导出记录 */
function toExportRecord() {
  routerPushByRouteName(RoutesName.StoreExportRecord);
}

const exportTypeRef = ref<StoreDataExportEnum>(StoreDataExportEnum.A01_MEMBER_DATA);
/** 点击 */
function handleExportClick(item: {
  id: string;
  type: StoreDataExportEnum;
  title: string;
  description: string;
}) {
  console.log("点击", item);
  exportTypeRef.value = item.type;
  openExportForm();
}
</script>

<style lang="less" scoped>
.store_data_export-content {
  height: 100%;
  padding: 12px 10px;
  box-sizing: border-box;
  overflow-y: auto;
}
.invite-logo {
  height: 100%;
  width: 100%;
}
</style>
