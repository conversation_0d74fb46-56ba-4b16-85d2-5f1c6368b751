<template>
    <van-popup 
        :show="props.show" 
        teleport="body"
        position="bottom"
        round 
        closeable
        close-icon="close"
        @click-close-icon="handleClose"
        @click-overlay="handleClose"
        style="height: auto;"
    >
       <div class="search-tag">
            <div class="searchItem">
                <van-field
                    v-if="isStoreManager == StatisticsEnum.IsStoreManager"
                    v-model="staffIdValue"
                    label="归属店员"
                    placeholder="请输入店员编号"
                    label-align="top"
                    type="digit"
                    :border="false"
                    class="input"
                    style="padding: 0px;margin-bottom: 24px;margin-top: 24px;"
                />
                <van-field
                    v-model="csId"
                    label="归属会员"
                    placeholder="请输入会员编号"
                    label-align="top"
                    type="digit"
                    :border="false"
                    class="input"
                    style="padding: 0px;margin-bottom: 24px;"
                />
                <van-field
                    v-model="productName"
                    label="商品名称"
                    placeholder="请输入商品名称"
                    label-align="top"
                    :border="false"
                    class="input"
                    style="padding: 0px;margin-bottom: 24px;"
                />
            </div>
            <div class="footer">
                <van-button  style="color:black;width:45%;" @click="handleReset"  round type="default" color="#F8F8F8">重置</van-button>
                <van-button  style="width:45%;"  @click="handleConfirm" round type="danger" >查询</van-button>
            </div>
       </div> 
    </van-popup>
</template>

<script setup lang="ts">
import { ref,} from "vue";
import {StatisticsEnum} from '@/views/StoreManagement/WelfareVoucherStatistics/type'
import { useUserStore } from '@/stores/modules/user';
const userStore = useUserStore()
const props = withDefaults(defineProps<{show:boolean,}>(),{
    show:false,
})
const emits = defineEmits<{
    (e:'update:show',val:boolean):void,
    (e:"close"):void;
    (e:'update:search',val:any):void,
}>()
const isStoreManager = ref(userStore.storeUserInfo.type)
const staffIdValue = ref('')
const csId = ref('')
const productName = ref('')
function handleConfirm(){
    let staffObj = {
        staffShortId:staffIdValue.value
    }
    const staffShortId = isStoreManager.value == StatisticsEnum.IsStoreManager ? staffObj : {}
    let param = {
        csShortId:csId.value,
        ...staffShortId,
        productName:productName.value
    }
    console.log(param)
    emits('update:search',param)
    handleClose()
}
function handleReset(){
    staffIdValue.value = ''
    csId.value = ''
    productName.value = ''
}
function handleClose(){
    handleReset()
    emits('update:show',false)
}

</script>

<style scoped lang="less">
.search-tag{
  .searchItem{
    padding: 18px 12px;
    .input.van-field {
      :deep(.van-field__body) {
        input {
          height: 40px;
          background: #f8f8f8;
          border-radius: 4px;
          padding-left: 8px;
        }
      }
    }
  }
  .footer{
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    margin-bottom: env(safe-area-inset-bottom);
    padding: 12px;
    :deep(.van-button__text) {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
:deep(.van-field__label) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #333333;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8px;
}
:deep(.van-field__control) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>