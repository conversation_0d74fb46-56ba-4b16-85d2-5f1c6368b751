import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 地址 */
export const enum storeAddressApiEnum {
  address = "/addressEntity/listByCode",
  // 添加收货地址
  addAddress = "/h5/customerAddress/add",
  // 删除收货地址
  deleteAddress = "/h5/customerAddress/remove",
  // 修改收货地址
  updateAddress = "/h5/customerAddress/update",
  // 修改默认地址
  updateDefaultAddress = "/h5/customerAddress/updateIsDefault",
  // 获取收货地址
  getAddress = "/h5/customerAddress/page",
  // 根据ID获取收货地址
  getAddressById = "/h5/customerAddress/getAddressById",
  // 供应商商品地址
  supplierAddress = "/customerAddress/addressConfig/supplier",
}

/**
 * @description 获取供应商收货地址
 */
export async function getSupplierAddress(_params: { id: string }) {
  return defHttp.get({
    url: `${getStoreApiUrl(storeAddressApiEnum.supplierAddress)}?id=${_params.id}`,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 根据ID获取收货地址
 */
export async function getAddressById(_params: { id: string }) {
  return defHttp.post({
    url: `${getStoreApiUrl(storeAddressApiEnum.getAddressById)}?id=${_params.id}`,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/** 获取地址数据 */
interface AddressDataParams {
  parentCode: string;
  cateType: number;
}
export async function getAddress(_params: AddressDataParams) {
  return defHttp.post({
    url: `${getStoreApiUrl(storeAddressApiEnum.address)}`,
    params: { data: _params },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/** 添加收货地址 */
export async function addAddress(_params) {
  return defHttp.post({
    url: `${getStoreApiUrl(storeAddressApiEnum.addAddress)}`,
    params: { data: _params },
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/** 删除收货地址 */
export async function deleteAddress(_params) {
  return defHttp.post({
    url: `${getStoreApiUrl(storeAddressApiEnum.deleteAddress)}`,
    params: { data: _params },
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/** 修改收货地址 */
export async function updateAddress(_params) {
  return defHttp.put({
    url: `${getStoreApiUrl(storeAddressApiEnum.updateAddress)}`,
    params: { data: _params },
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/** 修改默认地址 */
export async function updateDefaultAddress(_params) {
  return defHttp.put({
    url: `${getStoreApiUrl(storeAddressApiEnum.updateDefaultAddress)}`,
    params: { data: _params },
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/** 获取收货地址列表 */
export async function getCustomerAddress(_params) {
  return defHttp.post({
    url: `${getStoreApiUrl(storeAddressApiEnum.getAddress)}`,
    params: _params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}