{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:app-harmony": "uni -p app-harmony", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "cross-env NODE_ENV=development node ./scripts/dev.wx.js", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:app-harmony": "uni build -p app-harmony", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "git init && husky install", "type-check": "vue-tsc --noEmit"}, "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --fix"], "**/*.{vue,css,scss,html}": ["stylelint --fix"]}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020420240722002", "@dcloudio/uni-app-harmony": "3.0.0-4020420240722002", "@dcloudio/uni-app-plus": "3.0.0-4020420240722002", "@dcloudio/uni-components": "3.0.0-4020420240722002", "@dcloudio/uni-h5": "3.0.0-4020420240722002", "@dcloudio/uni-mp-alipay": "3.0.0-4020420240722002", "@dcloudio/uni-mp-baidu": "3.0.0-4020420240722002", "@dcloudio/uni-mp-jd": "3.0.0-4020420240722002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020420240722002", "@dcloudio/uni-mp-lark": "3.0.0-4020420240722002", "@dcloudio/uni-mp-qq": "3.0.0-4020420240722002", "@dcloudio/uni-mp-toutiao": "3.0.0-4020420240722002", "@dcloudio/uni-mp-weixin": "3.0.0-4020420240722002", "@dcloudio/uni-mp-xhs": "3.0.0-4020420240722002", "@dcloudio/uni-quickapp-webview": "3.0.0-4020420240722002", "@dcloudio/uni-ui": "^1.5.6", "@vant/popperjs": "^1.3.0", "@vant/touch-emulator": "^1.4.0", "@vant/use": "^1.6.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "module-alias": "^2.2.3", "pinia": "^2.0.33", "uniapp-qrcode": "^1.0.2", "vant": "^4.9.21", "vue": "^3.4.21"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4020420240722002", "@dcloudio/uni-cli-shared": "3.0.0-4020420240722002", "@dcloudio/uni-stacktracey": "3.0.0-4020420240722002", "@dcloudio/vite-plugin-uni": "3.0.0-4020420240722002", "@eslint/js": "^9.9.1", "@unocss/preset-legacy-compat": "^0.59.4", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.1.3", "commitlint": "^18.6.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "husky": "^8.0.3", "lint-staged": "^15.2.7", "miniprogram-api-typings": "^3.12.3", "sass": "^1.71.1", "stylelint": "^16.6.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^14.0.0", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-prettier": "^5.0.0", "typescript": "^4.9.4", "typescript-eslint": "^8.3.0", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-vue-components": "^29.0.0", "vite": "5.2.8", "vue-tsc": "^1.0.24"}, "_moduleAliases": {"@": "./scripts/preBuild/src"}}