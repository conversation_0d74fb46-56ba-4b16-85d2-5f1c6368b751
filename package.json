{"name": "social-groups-mobile", "version": "0.0.0", "scripts": {"dev": "vite", "bootstrap": "pnpm install", "clean:lib": "rimraf node_modules", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "build-only": "vite build", "build-only:prod": "cross-env UAT=$npm_config_uat vite build --mode prod", "build-only:test": "vite build --mode test", "type-check": "vue-tsc --noEmit", "prettier:all": "npx prettier --write ./src/", "lint:all": "npx eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "lint:src": "npx eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\"", "lint:lint-staged": "npx lint-staged -c ./.husky/lintstagedrc.js"}, "dependencies": {"@antv/f-vue": "^1.1.1", "@antv/f2": "^5.0.39", "@csstools/normalize.css": "^12.0.0", "@vant/touch-emulator": "^1.4.0", "axios": "^1.1.2", "clipboard": "^2.0.11", "crypto-js": "^4.1.1", "dayjs": "^1.11.6", "deepmerge-ts": "^4.2.2", "html2canvas": "^1.4.1", "lottie-web": "^5.12.2", "moment": "^2.29.4", "nprogress": "^0.2.0", "pinia": "^2.0.21", "qrcode": "^1.5.3", "qs": "^6.11.0", "vant": "^4.9.1", "vue": "^3.2.38", "vue-router": "^4.1.5"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.23.4", "@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@rollup/plugin-babel": "^6.0.4", "@types/node": "^16.11.56", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-legacy": "^4.0.4", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/tsconfig": "^0.1.3", "cross-env": "^7.0.3", "eslint": "^8.37.0", "eslint-plugin-vue": "^9.10.0", "flv.js": "^1.6.2", "hls.js": "^1.5.13", "less": "^4.1.3", "lint-staged": "^13.2.0", "naive-ui": "^2.38.2", "prettier": "^2.8.7", "rimraf": "^3.0.2", "terser": "^5.16.1", "typescript": "~4.7.4", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.22.11", "vfonts": "^0.0.3", "video.js": "^8.16.1", "vite": "^4.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^0.40.7"}}