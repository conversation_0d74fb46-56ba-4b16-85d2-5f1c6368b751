<template>
  <!-- 积分收入 -->
  <template v-if="props.pointType == IntegralEnum.INCOME">
    <div class="point-wrapper">
      <img :src="pointIcon" alt="" />
      <div class="point-desc-container">
        <div class="point-title">{{ integralDetailTypeMap[pointInfoRef.source] }}</div>
        <div class="point-desc">
          <span class="channel">{{ `渠道：${pointChannelMap[pointInfoRef.channel]}` }}</span>
          <div class="line"></div>
          <span class="point-time">{{ pointInfoRef?.createTime }}</span>
        </div>
      </div>
      <!-- 积分 -->
      <div class="point">{{`+${pointInfoRef?.points}积分`}}</div>
    </div>
  </template>
  <!-- 积分支出 -->
  <template v-else>
    <div class="expenditure-points-wrapper">
        <div class="expenditure-points-left">
            <p class="expenditure-points-title">{{`${pointInfoRef?.sourceDetail}`}}</p>
            <span class="expenditure-points-time">{{ pointInfoRef?.createTime }}</span>
        </div>
        <div class="expenditure-points-right">
            <span class="expenditure-points">{{`-${pointInfoRef?.points}积分`}}</span>
        </div>
    </div>
  </template>
</template>

<script lang="ts" setup>
import { ref, toRefs } from "vue";
import { IntegralEnum, StoreIntegralDetailTypeEnum, StoreIntegralChannelEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import pointIcon from "@/assets/storeImage/storeHome/point/pointIcon.png";

defineOptions({ name: 'PointCard' });

/** props */
const props = defineProps<{
    pointType: IntegralEnum;
    pointInfo: {
        id?: string;
        code?: string;
        io?: IntegralEnum;
        points?: number;
        createTime?: string;
        channel?: StoreIntegralChannelEnum;
        source?: StoreIntegralDetailTypeEnum;
        sourceDetail?: string;
    }
}>();

const { pointType, pointInfo: pointInfoRef } = toRefs(props);

/** 积分明细类型 */
const integralDetailTypeMap: Record<StoreIntegralDetailTypeEnum, string> = {
  [StoreIntegralDetailTypeEnum.EXPIRED]: '过期未使用',
  [StoreIntegralDetailTypeEnum.SHOPPING_EXPENSE]: '积分购物支出',
  [StoreIntegralDetailTypeEnum.SHOPPING_REBATE]: '购物返还',
  [StoreIntegralDetailTypeEnum.CHECK_IN]: '签到',
  [StoreIntegralDetailTypeEnum.DAILY_VISIT]: '每日来访',
  [StoreIntegralDetailTypeEnum.VIEW_PRODUCT]: '查看商品',
  [StoreIntegralDetailTypeEnum.COMMUNITY_TASK]: '社群任务',
  [StoreIntegralDetailTypeEnum.COMMUNITY_MANUAL_ADD]: '社群手动增加',
  [StoreIntegralDetailTypeEnum.COMMUNITY_MANUAL_SUB]: '社群手动减少',
  [StoreIntegralDetailTypeEnum.COMMUNITY_POINT_REPLACE_REDPACKET]: '社群积分替代红包',
  [StoreIntegralDetailTypeEnum.MALL_MANUAL_ADD]: '商城手动增加',
  [StoreIntegralDetailTypeEnum.MALL_MANUAL_SUB]: '商城手动减少',
  [StoreIntegralDetailTypeEnum.STORE_MANUAL_ADD]: '门店手动增加',
  [StoreIntegralDetailTypeEnum.STORE_MANUAL_SUB]: '门店手动减少'
};

/** 积分渠道 */
const pointChannelMap: Record<StoreIntegralChannelEnum, string> = {
  [StoreIntegralChannelEnum.MALL]: '商城',
  [StoreIntegralChannelEnum.COMMUNITY]: '社群',
  [StoreIntegralChannelEnum.STORE]: '门店',
};
</script>

<style lang="less" scoped>
.point-wrapper {
    width: 100%;
    height: 76px;
    border-bottom: 1px solid #EEEEEE;
    padding: 16px 0px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    img {
        width: 38px;
        height: 38px;
    }
    .point-desc-container {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-left: 2px;
        .point-title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .point-desc {
            display: flex;
            align-items: center;
            gap: 6px;
            .channel {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .line {
                width: 0px;
                height: 10px;
                border: 1px solid rgba(0,0,0,0.05);
            }
            .point-time {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }
    .point {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #4BE092;
        line-height: 24px;
        text-align: right;
        font-style: normal;
        text-transform: none;
        margin-left: auto;
    }
}

.expenditure-points-wrapper {
    width: 100%;
    border-bottom: 1px solid #EEEEEE;
    padding: 16px 0px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .expenditure-points-left  {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 6px;
        .expenditure-points-title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .expenditure-points-time {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
    .expenditure-points-right {
        min-width: 80px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .expenditure-points {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #EF1115;
            line-height: 24px;
            text-align: right;
            font-style: normal;
            text-transform: none;
        }
    }
}
</style>
