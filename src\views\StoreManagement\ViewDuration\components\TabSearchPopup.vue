<template>
  <van-popup
    :show="props.show"
    teleport="body"
    position="bottom"
    round
    closeable
    close-icon="close"
    @click-close-icon="handleClose"
    @click-overlay="handleClose"
    style="height: auto"
    safe-area-inset-bottom
  >
    <div class="search-popup-content">
      <div class="search-item">
        <van-field
          class="input"
          v-model="csId"
          label="会员ID"
          placeholder="请输入会员ID"
          label-align="top"
          :border="false"
          style="padding: 0px;margin-bottom: 24px;margin-top: 24px;"
        />
        <van-field
          class="input"
          v-model="staffId"
          label="归属店员编号"
          placeholder="请输入店员编号"
          label-align="top"
          :border="false"
          style="padding: 0px;margin-bottom: 24px;margin-top: 24px;"
        />
      </div>
      <div class="footer">
        <van-button style="color: black; width: 45%" @click="handleReset" round type="default" color="#F8F8F8">
          重置
        </van-button>
        <van-button style="width: 45%" @click="handleConfirm" round type="danger">查询</van-button>
      </div>
    </div>
  </van-popup>
</template>
<script setup lang="ts">
import { ref } from "vue";

const props = withDefaults(defineProps<{ show: boolean }>(), {
  show: false,
});

const emits = defineEmits<{
  (e: "update:show", val: boolean): void;
  (e: "close"): void;
  (e: "update:value", val: any): void;
}>();

const staffId = ref("");
const csId = ref("");
const handleConfirm = () => {
  emits("update:value", { staffShortId: staffId.value, csShortId: csId.value });
  handleClose();
};

const handleReset = () => {
  staffId.value = "";
  csId.value = "";
  handleConfirm();
};

const handleClose = () => {
  emits("update:show", false);
};
</script>

<style scoped lang="less">
.search-popup-content {
  .search-item {
    padding: 18px 12px;
    .input.van-field {
      :deep(.van-field__body) {
        input {
          height: 40px;
          background: #f8f8f8;
          border-radius: 4px;
          padding-left: 8px;
        }
      }
    }
  }

  .footer {
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    padding: 12px;
    :deep(.van-button__text) {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
:deep(.van-field__label) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #333333;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8px;
}
:deep(.van-field__control) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
}
</style>
