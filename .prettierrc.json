{"arrowParens": "avoid", "bracketSameLine": false, "bracketSpacing": true, "embeddedLanguageFormatting": "off", "htmlWhitespaceSensitivity": "ignore", "insertPragma": false, "jsxSingleQuote": false, "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": false, "tabWidth": 2, "trailingComma": "all", "useTabs": false, "vueIndentScriptAndStyle": false, "jsxBracketSameLine": true, "endOfLine": "lf"}