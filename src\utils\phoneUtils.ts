//不做判断 直接开启
const OlderVersionDeter = {
    Android:180,
    iOS:200
}


export function getSystemName(){
    var userAgent = navigator.userAgent || navigator.vendor;
    // 判断是否是 iOS
    if (/iPad|iPhone|iPod/.test(userAgent)) {
        return 'iOS';
    }

    // 判断是否是 Android
    if (/android/i.test(userAgent)) {
        return 'Android';
    }

    return 'unknown';
}

export function getIOSVersion(){
    const userAgent = navigator.userAgent || navigator.vendor ;
    var match = userAgent.match(/OS (\d+_\d+(_\d+)?)/);
    if (match) {
        const _version = Number(match[1].replace(/_/g, '.'));
        return isNaN(_version)?null:_version
    }
    else{
        return null
    }
}
export function getAndroidVersion(){
    const userAgent = navigator.userAgent || navigator.vendor;
    var match = userAgent.match(/Android (\d+(\.\d+){0,2})/);
    if (match) {
        const _version = Number(match[1]);
        return isNaN(_version)?null:_version
    }
    else return null
}

export function isOlderSystemVersion():boolean{
    const systemName = getSystemName()
    if(systemName == 'iOS'){
        const version = getIOSVersion()
        if(version){
            return version<=OlderVersionDeter['iOS']
        }
        else{
            return true
        }
    }
    else if(systemName == 'Android'){
        const version = getAndroidVersion()
        if(version){
            return version<=OlderVersionDeter['Android']
        }
        else{
            return true
        }
    }
    else {
        return false
    }
}