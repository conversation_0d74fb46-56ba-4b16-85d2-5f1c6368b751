import { nextTick, onDeactivated, ref } from "vue";

/** 存储需要被keep-alive缓存的组件路由名称列表（响应式引用）*/
const keepAliveRouteListRef = ref([])

/** 存储各个路由对应的滚动位置（普通对象）*/
const scrollBarContainMap = {}

export function useKeepAliveRoute(){
    /** 创建可滚动容器的ref引用 */
    const scrollBarContainRef = ref(null)
    
    /** 
     * @description 查找路由在缓存列表中的索引 
     */
    function _findIndex(name){
        return keepAliveRouteListRef.value.findIndex(item=>item == name)
    }

    /** 
     * @description 添加路由到keep-alive缓存列表 
     */
    function pushKeepAliveRoute(name:string){
        const _index = _findIndex(name)
        if(_index == -1){
            keepAliveRouteListRef.value.push(name)
        }
    }

    /** 
     * @description 从缓存列表中移除指定路由 
     */
    function deleteKeepAliveRouteByName(name:string){
        const _index = _findIndex(name)
        if(_index == -1){
           return
        }
        else{
            const _temp = [...keepAliveRouteListRef.value]
            _temp.splice(_index,1)
            keepAliveRouteListRef.value = _temp
            // scrollBarContainMap[name] = 0
        }
    }

    /** 
     * @description 滚动事件处理函数：记录当前滚动位置 
     */
    function scrollEventHandler(e:Event,name:string){
        // 将滚动位置保存到对应路由名的映射中
        scrollBarContainMap[name] = e.target['scrollTop'] || 0;
    }

    /** 
     * @description 通过DOM元素直接恢复滚动位置 
     */
    function restoreScrollPositionByDom(dom:Element,name:string){
        // 从映射中获取保存的位置并设置到DOM元素
        dom.scrollTop = scrollBarContainMap[name]
        // 重置该路由的滚动位置记录
        scrollBarContainMap[name] = 0
    }

    /** 
     * @description 保存当前路由的滚动位置 
     */
    function saveScrollPosition(name:string) {
        // 如果有滚动容器ref，则保存其滚动位置
        scrollBarContainMap[name] = scrollBarContainRef.value ? scrollBarContainRef.value.scrollTop : 0;
    }

    /** 
     * @description 恢复指定路由的滚动位置 
     */
    function restoreScrollPosition(name:string) {
        // 在下一次DOM更新后执行
        nextTick(()=>{
            if(scrollBarContainRef.value){
                // 从映射中获取保存的位置并设置到滚动容器
                scrollBarContainRef.value.scrollTop = scrollBarContainMap[name]
            }
        })
    }

    // 返回所有方法和状态
    return {
        scrollEventHandler,
        restoreScrollPositionByDom,
        scrollBarContainRef,
        keepAliveRouteListRef,
        pushKeepAliveRoute,
        deleteKeepAliveRouteByName,
        saveScrollPosition,
        restoreScrollPosition,
        _findIndex
    }
}