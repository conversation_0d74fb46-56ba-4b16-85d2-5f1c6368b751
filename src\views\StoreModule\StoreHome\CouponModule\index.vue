<template>
  <div class="coupon-wrapper">
    <!-- 通知 -->
    <div class="coupon-notice">
      <VanNoticeBar left-icon="volume-o" color="#1677FF" background="#ECF5FF" style="height: 36px;border-radius: 8px;">
        <p class="notice-title">福利券到账可能会有延迟，请直播结束后再查询</p>
      </VanNoticeBar>
    </div>
    <!-- tabs -->
    <VanTabs
      :style="{ '--van-tabs-line-height': '40px', height: '40px' }"
      v-model:active="activeTabRef"
      color="#EF1115"
      title-active-color="#EF1115"
      title-inactive-color="#333333"
      line-width="36px"
      line-height="2px"
      swipeable
    >
      <VanTab v-for="item in couponStatusOptions" :key="item.value" :name="item.value">
        <template #title>
          <span class="tab-title" :class="{ 'tab-active': activeTabRef === item.value }">{{ item.label }}</span>
        </template>
      </VanTab>
    </VanTabs>
    <div class="coupon-content-wrapper">
      <CouponList :userId="props.userId" :type="props.type" :useStatus="activeTabRef" />
    </div>
    <!-- 去福利券商城兑换礼品 -->
    <div v-if="props.type == StoreCouponRouteTypeEnum.MY_COUPON" class="footer">
      <VanButton type="danger" block round @click="handleToCouponStore">去福利券商城兑换礼品</VanButton>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { ref, computed } from "vue";
import { useUserRole, useRouterUtils } from "@/views/StoreModule/hooks";
import { CouponStatusEnum, StoreCouponRouteTypeEnum, KeepAliveRouteNameEnum } from "@/views/StoreModule/enums";
import { RoutesName } from "@/enums/routes";
/** 相关组件 */
import CouponList from "./components/CouponList.vue";

defineOptions({ name: KeepAliveRouteNameEnum.COUPON });

/** props */
const props = defineProps<{
  userId: string;
  type: StoreCouponRouteTypeEnum;
}>();

const { isStoreMember } = useUserRole();
const { routerPushByRouteName } = useRouterUtils();
/** tab */
const activeTabRef = ref(CouponStatusEnum.NOT_USED);

/** 福利券状态 */
const couponStatusOptions = [
  {
    label: '待使用',
    value: CouponStatusEnum.NOT_USED,
  },
  {
    label: '已使用',
    value: CouponStatusEnum.USED,
  },
  {
    label: '已失效',
    value: CouponStatusEnum.EXPIRED,
  },
];

function handleToCouponStore() {
  routerPushByRouteName(RoutesName.StoreWelfareVoucherMall);
}
</script>

<style lang="less" scoped>
.coupon-wrapper {
  width: 100%;
  height: calc(100vh - env(safe-area-inset-bottom));
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
  .coupon-notice {
    height: 36px;
    .notice-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #1677FF;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .tab-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .tab-active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #EF1115;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .coupon-content-wrapper {
    flex: 1;
    height: calc(100% - 136px);
  }
  .footer {
    height: 60px;
    padding: 8px 12px;
    box-sizing: border-box;
  }
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
:deep(.van-tabs__line) {
  bottom: 18px;
}
</style>
