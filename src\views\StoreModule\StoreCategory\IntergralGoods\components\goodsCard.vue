<template>
  <div class="goodsItem">
    <div class="goodsHeader">
      <div class="goodsImage">
        <img :src="props.cardInfo.firstImg" />
        <MaskBanner type="stock" isUseTypeMask v-if="totalStock == 0"></MaskBanner>
      </div>
    </div>
    <div class="goodsInfo">
      <GoodsTitle style="margin: 8px 0;font-size: 16px;" :state="props.cardInfo"></GoodsTitle>
      <IntegralContent :numFontSize="16" :skuInfo="minSkuInfo">
      </IntegralContent>
      <div class="goodsInfo-footer">
        <SoldQty :card-info="props.cardInfo"></SoldQty>
        <van-button  class="goodsBtn" size="small" round @click.stop="toExchange">去兑换</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { RoutesName } from "@/enums/routes";
import IntegralContent from "../../components/IntegralContent.vue";
import GoodsTitle from "../../components/GoodsTitle.vue";
import SoldQty from "../../components/SoldQty.vue";
import { saleComputedSum, genSaleCount } from "@/utils/storeUtils";
import MaskBanner from "../../components/MaskBanner/index.vue";
import { contrastMinPriceSku, filterSkuMinIntegral } from "@/utils/storeUtils";
const router = useRouter()
const props = withDefaults(defineProps<{
  cardInfo: any
}>(), {
  cardInfo: () => ({})
})
const emits = defineEmits<{
  'toExchange':[any]
}>()
//最小规格数据
const minSkuInfo = computed(() => {
  let info: any = {}
  info = filterSkuMinIntegral(skuList.value)
  return {
    ...info,
    price: info?.price || 0,
    minPrice: info?.minPrice || 0,
    upper: info.upper || 0,
    availStocks: info.availStocks || 0
  }
})
const skuList = computed<any[]>(() => {
  let list = props.cardInfo?.appletPointSpecDTOS || []
  return list
});
//获取总库存数量
const totalStock = computed(() => {
  return skuList.value.reduce((pre, cur) => {
    return pre + (cur.availStocks || 0);
  }, 0);
});
const toExchange = () => {
  emits('toExchange', props.cardInfo)
}
</script>
<style scoped lang="less">
@import url('@/styles/storeVar.less');
.goodsItem {
  background: #FFFFFF;
  border-radius: 8px;
  
  .goodsHeader {
    .goodsImage {
      position: relative;
      height: 160px;
      border-radius: 8px;
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%
      }
    }
  }
  
  .goodsInfo {
    padding: 12px;
    .goodsName {
      font-weight: bold;
      color: #333333;
      font-size: 14px;
      word-break: break-all;
      word-wrap: break-word;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .goodsInfo-footer{
      margin-top: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .goodsBtn {
        background: @error-color;
        color: #fff;
      }
    }
  }
}
</style>