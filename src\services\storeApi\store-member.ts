import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
const enum MemberManagementApi {
  memberOverview = "/h5/customerManage/findMemberOverview",
  memberCustomerPage = "/h5/customerManage/pageStoreCustomer",
  setAssistant = "/h5/customerManage/setAssistant",
  addOrDecPoints = "/h5/customerManage/addOrDecPoints",
  welfareVoucherPage = "/h5/customerManage/pageCoupon",
  sendWelfareVoucher = "/h5/customerManage/sendCoupon",
}

// 会员概览
export function getMemberOverview(_params: {
  storeId: string;
  customerId: string;
}) {
  return defHttp.get({
    url: getStoreApiUrl(MemberManagementApi.memberOverview),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}
// 分页查询店铺会员
export function getMemberCustomerPage(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(MemberManagementApi.memberCustomerPage),
    params: params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}
// 设为店员
export function setAssistant(params) {
  return defHttp.put({
    url: getStoreApiUrl(`${MemberManagementApi.setAssistant}?storeId=${params?.storeId}&customerId=${params?.customerId}`),
    // params: params
  });
}
// 加减积分
export function addOrDecPoints(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(MemberManagementApi.addOrDecPoints),
    params: params,
  });
}
// 福利券列表
export function getWelfareVoucherPage(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(MemberManagementApi.welfareVoucherPage),
    params: params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}
// 发放福利券
export function sendWelfareVoucher(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(MemberManagementApi.sendWelfareVoucher),
    params: params
  });
}
