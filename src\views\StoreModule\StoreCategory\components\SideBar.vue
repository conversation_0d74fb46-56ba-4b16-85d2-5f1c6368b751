<template>
	<div class="sidebar-box">
		<div class="side-item" :class="{ active: props.value === item.id }" @click="handleClick(item)"
			v-for="item in list" :key="item.id">
			<div class="side-line" v-if="props.value === item.id"></div>
			<div class="side-value">
				<text>{{ item.name }}</text>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, type StyleValue } from "vue"
interface Props {
	value: string | null;
	customStyle: StyleValue;
	list: any[];
}
const props = withDefaults(defineProps<Props>(), {
	value: '',
	customStyle: () => ({}),
	list: () => ([])
})
const emits = defineEmits<{
	(e: 'update:value', value: string): void;
}>()
const handleClick = (info: any) => {
	emits('update:value', info.id)
}
</script>

<style lang="less" scoped>
@import url('@/styles/default.less');

.active {
	background-color: #fff !important;
}

.sidebar-box {
	height: 100%;
	width: 75px;
	overflow: auto;
	background-color: #f8f8f8;

	.side-item {
		box-sizing: border-box;
		padding: 15px 2.5px;
		display: flex;
		align-items: center;
		border-top-right-radius: 8px;
		border-bottom-right-radius: 8px;

		.side-line {
			width: 3px;
			min-width: 3px;
			height: 20px;
			border-top-right-radius: 8px;
			border-bottom-right-radius: 8px;
			background-color: @error-color;
		}

		.side-value {
			font-size: 13px;
			padding: 5px;
			max-width: 100%;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}
	}
}
</style>