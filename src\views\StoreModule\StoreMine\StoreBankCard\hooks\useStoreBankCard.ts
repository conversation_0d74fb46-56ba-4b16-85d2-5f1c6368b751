import { ref } from "vue";
import { showToast } from "vant";
import { useRouter } from "vue-router";
import { useMessages } from "@/hooks/useMessage";
import { isArray } from "@/utils/isUtils";
import { AccountTypeEnum } from "@/views/StoreModule/enums";
import { storeAddBankCardApi, storeUpdateBankCardApi } from "@/services/storeApi";

export default function useStoreBankCard() {
  const router = useRouter();
  const { createMessageError, createMessageSuccess } = useMessages();
  const isPageLoadingRef = ref(false);

  /** 表单参数 */
  const initparams = {
    /** 提现金额账户类型 */
    accountType: AccountTypeEnum.PRIVATE,
    /** 户名 */
    accountName: null,
    /** 银行卡号 */
    cardNumber: null,
    /** 开户行ID */
    bankId: null,
    /** 开户行名称 */
    bankName: null,
    /** 开户行行号 */
    bankNo: null,
  };
  const formValue = ref({ ...initparams });

  /** 获取参数 */
  function _getParams() {
    const { accountType, accountName, cardNumber, bankId, bankName, bankNo } = formValue.value;

    if (accountType === AccountTypeEnum.PRIVATE) {
      return {
        accountType,
        accountName,
        cardNumber,
      };
    }

    if (accountType === AccountTypeEnum.PUBLIC) {
      return {
        accountType,
        accountName,
        cardNumber,
        bankId,
        bankName,
        bankNo,
      };
    }
  }

  /** 验证表单 */
  function _validateForm() {
    const { accountType, accountName, cardNumber, bankId, bankName, bankNo } = formValue.value;

    // 1. Validate account name (必填，最多60个字)
    if (!accountName || accountName.trim() === "") {
      showToast("户名不能为空");
      return false;
    }
    if (accountName.length > 60) {
      showToast("户名最多60个字");
      return false;
    }

    // 2. Validate card number (必填，只能输入数字，最少13位，最多19位)
    if (!cardNumber || cardNumber.trim() === "") {
      showToast("银行卡号不能为空");
      return false;
    }
    if (!/^\d+$/.test(cardNumber)) {
      showToast("银行卡号只能输入数字");
      return false;
    }
    if (cardNumber.length < 13 || cardNumber.length > 19) {
      showToast("银行卡号长度应为13-19位");
      return false;
    }

    // 3. Validate public account fields (当账户类型选择【对公账户】时才校验)
    if (accountType === AccountTypeEnum.PUBLIC) {
      if (!bankId || !bankName || !bankNo) {
        showToast("请填写完整的开户行信息");
        return false;
      }
    }

    return true;
  }

  /** 保存 -- 银行卡 */
  async function handleSave() {
    try {
      if (!_validateForm()) {
        return;
      }

      isPageLoadingRef.value = true;
      const _params = _getParams();
    
      await storeAddBankCardApi(_params);
      showToast("保存成功");

      router.back();
    } catch (error) {
      createMessageError("保存失败：" + error);
    } finally {
      isPageLoadingRef.value = false;
    }
  }

  /** 修改 -- 银行卡 */
  async function handleUpdate(id: string) {
    try {
      if (!_validateForm()) {
        return;
      }

      isPageLoadingRef.value = true;
      const _params = _getParams();
      _params['id'] = id;

      await storeUpdateBankCardApi(_params);
      showToast("修改成功");

      router.back();
    } catch (error) {
      createMessageError("修改失败：" + error);
    } finally {
      isPageLoadingRef.value = false;
    }
  }

  return {
    formValue,
    isPageLoadingRef,
    handleSave,
    handleUpdate
  };
}
