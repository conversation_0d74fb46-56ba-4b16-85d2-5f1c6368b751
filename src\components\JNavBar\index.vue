<template>
  <view class="j-nav-bar" :style="navBarStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: rectInfo.statusBarHeight + 'px' }"></view>

    <!-- 导航栏内容 -->
    <view class="nav-content" :style="{ height: rectInfo.navbarHeight + 'px' }">
      <!-- 左侧区域 -->
      <view class="nav-left">
        <slot name="left">
          <view v-if="showBack" class="back-btn" @click="handleBack">
            <van-icon name="arrow-left" size="36rpx" />
          </view>
        </slot>
      </view>

      <!-- 中间标题区域 -->
      <view class="nav-center">
        <slot name="center">
          <text v-if="title" class="nav-title">{{ title }}</text>
        </slot>
      </view>

      <!-- 右侧区域 -->
      <view class="nav-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useRectInfo } from '@/hooks/common'

interface NavBarProps {
  /** 标题 */
  title?: string
  /** 是否显示返回按钮 */
  showBack?: boolean
  /** 是否固定在顶部 */
  fixed?: boolean
}

/** props */
const props = withDefaults(defineProps<NavBarProps>(), {
  title: '',
  showBack: true,
  fixed: false,
})

/** emits */
const emit = defineEmits<{
  back: []
}>()

defineOptions({ name: 'JNavBar' })

const { rectInfo } = useRectInfo()

const navBarStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.fixed) {
    style.position = 'fixed'
    style.top = '0'
    style.left = '0'
    style.right = '0'
    style.zIndex = '999'
  } else {
    style.position = 'relative'
    style.top = '0'
    style.left = '0'
    style.right = '0'
    style.zIndex = '999'
  }

  return style
})

const handleBack = () => {
  emit('back')
  // 如果没有监听back事件，则执行默认返回逻辑
  if (!props.showBack) return

  // 判断是否可以返回上一页
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    // 如果没有上一页，则跳转到首页
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
}
</script>

<style lang="scss" scoped>
.j-nav-bar {
  width: 100%;
  display: flex;
  flex-direction: column;

  .status-bar {
    width: 100%;
  }

  .nav-content {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 8px;
    box-sizing: border-box; // 确保padding不会导致溢出

    .nav-left {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex: 0 0 auto;
      min-width: 80px;

      .back-btn {
        display: flex;
        align-items: center;
        justify-content: center;

        .back-icon {
          font-size: 24px;
          font-weight: bold;
          line-height: 1;
        }
      }
    }

    .nav-center {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1 1 auto;
      min-width: 0;
      padding: 0 8px;

      .nav-title {
        font-weight: 400;
        font-size: 32rpx;
        color: #000000;
        line-height: 102rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }

    .nav-right {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 0 0 auto;
      min-width: 80px;
    }
  }
}
</style>
