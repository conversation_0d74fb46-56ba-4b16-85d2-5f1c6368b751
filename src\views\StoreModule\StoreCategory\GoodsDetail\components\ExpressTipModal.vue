<template>
    <van-popup round v-model:show="_show" closeable teleport="body" close-icon="close" position="bottom" @close="onClose"
        :safe-area-inset-bottom='safeBottom' lock-scroll>
        <div class="popup-content">
            <div class="content-tip" v-for="(item, index) in tipList" :key="index">
                <div class="tip-title">{{ `${index + 1}、${item.title}:` }}</div>
                <div class="tip-content">{{ item.content }}</div>
            </div>
        </div>
    </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, type StyleValue } from "vue";
interface Props {
    show: boolean;
    safeBottom: boolean;
}
interface Option {
    title: string;
    content: string;
}
const props = withDefaults(defineProps<Props>(), {
    show: false,
    safeBottom: true,
})
const emits = defineEmits<{
    (e: 'update:show', val: boolean): void;
    (e: 'refresh'): void;
}>()
const _show = computed({
    get: () => props.show,
    set: (val) => emits('update:show', val)
})
const tipList = ref<Option[]>([
    {
        title: "支持物流代收",
        content: "商品的所有费用以及邮寄商品所需的邮费全额在用户签收货物时由物流公司进行收取。"
    },
    {
        title: "支持订单支付",
        content: "先收取定金及邮寄商品所需的邮费，剩余尾款在用户签收货物时由物流公司进行收取。"
    },
])
const onClose = () => {
    _show.value = false
    emits('refresh')
}
</script>

<style lang="less" scoped>
.popup-content {
    font-size: 14px;
    padding: 60px 16px;
    box-sizing: border-box;

    .content-tip {
        margin-bottom: 8px;

        .tip-title {
            margin-bottom: 5px;
        }

        .tip-content {
            color: #666666;
        }
    }
}
</style>