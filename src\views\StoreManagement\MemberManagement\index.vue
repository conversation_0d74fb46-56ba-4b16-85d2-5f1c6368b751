<template>
  <!-- 会员管理主页面 -->
  <div class="member-management-page">
    <!-- 会员概览组件 -->
    <MemberOverview :memberOverview="memberOverview" />
    
    <!-- 加载状态包装器 -->
    <JLoadingWrapper :show="isPageLoadingRef">
      <!-- 搜索栏区域 -->
      <div class="search-bar">
        <div class="search-warpper">
          <!-- 搜索框 -->
          <view class="search-box">
            <VanSearch
              @search="handleSearch"
              @clear="handleSearch"
              @click-left-icon="handleSearch"
              shape="round"
              v-model="modal.keyword"
              placeholder="请输入会员编号或昵称搜索"
            />
          </view>
          
          <!-- 筛选按钮 -->
          <div class="screen" @click="handleFilter">
            <span>筛选</span>
            <SvgIcon
              name="dropDown"
              style="font-size: 18px;"
              :class="{'rotate-180': showFilterPopup, 'icon': true }"
            />
          </div>
        </div>
      </div>
      
      <!-- 下拉刷新区域 -->
      <VanPullRefresh
        class="content-wrapper"
        v-model="refreshingRef"
        @refresh="onRefresh"
      >
        <!-- 列表区域 -->
        <VanList
          v-if="memberList.length"
          v-model:loading="isLoadingRef"
          @load="onLoad"
          :finished="isFinishedRef"
          finished-text="没有更多了"
        >
          <!-- 列表卡片组件 -->
          <ListCard v-for="item in memberList" :data="item" @refresh="handleSearch" />
        </VanList>
        
        <!-- 空状态显示 -->
        <van-empty v-else description="暂无数据" :image="emptyImg" :image-size="[200, 200]" />
      </VanPullRefresh>
    </JLoadingWrapper>
    
    <!-- 筛选弹窗组件 -->
    <TagSearchPopup v-model:show="showFilterPopup" @update:value="updateSearch" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import dayjs from "dayjs";
import useMember from "./hooks/useMember";
// 图片资源
import emptyImg from "@/assets/store0602Image/emptyImg.png";
// 组件
import TagSearchPopup from "./components/TabSearchPopup.vue";
import ListCard from "./components/ListCard.vue";
import MemberOverview from "./components/MemberOverview.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

/** props */
const props = defineProps<{
  storeId?: string; // 店铺id
  customerId?: string; // 客户id
}>();

const route = useRoute();
const { 
  modal,
  isPageLoadingRef,
  memberList,
  isFinishedRef,
  refreshingRef,
  isLoadingRef,
  memberOverview,
  onLoad,
  onRefresh,
  getMemberList,
  initMemberList,
  getMemberCustomer
 } = useMember({
  storeId: props.storeId,
  staffShortId: route?.query?.staffShortId || null,
  customerId: props.customerId
});

const showFilterPopup = ref(false);

/**
 * 更新搜索条件
 * @param {Object} data - 新的搜索条件
 */
const updateSearch = data => {
  modal.value.staffShortId = data.staffShortId;
  
  // 处理注册日期范围
  if (data.registrationDate) {
    modal.value.startTime = data.registrationDate[0]
      ? dayjs(data.registrationDate[0]).format("YYYY-MM-DD 00:00:00")
      : "";
    modal.value.endTime = data.registrationDate[1]
      ? dayjs(data.registrationDate[1]).format("YYYY-MM-DD 23:59:59")
      : "";
  } else {
    modal.value.startTime = "";
    modal.value.endTime = "";
  }
  
  initMemberList();
};

/**
 * 搜索处理
 */
const handleSearch = () => {
  initMemberList();
};

/**
 * 打开筛选弹窗
 */
const handleFilter = () => {
  showFilterPopup.value = true;
};

/** 组件挂载时加载数据 */
onMounted(async () => {
  await getMemberCustomer();
  await initMemberList();
});
</script>

<style lang="less" scoped>
.member-management-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  // 搜索栏样式
  .search-bar {
    width: 100%;
    font-size: 15px;
    background: #fff;
    
    .search-warpper {
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
      
      .search-box {
        width: 295px;
        
        .van-search {
          padding: 10px 0;
          
          :deep(.van-cell) {
            background-color: transparent !important;
            padding: 0 8px 0 0 !important;
          }
        }
      }
      
      // 筛选按钮样式
      .screen {
        display: flex;
        align-items: center;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 20px;
        text-align: right;
        font-style: normal;
        text-transform: none;
        
        .search-icon {
          margin-left: 4px;
          width: 0;
          height: 0;
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
          border-top: 6px solid black;
        }
      }
    }
  }
  
  // 内容区域样式
  .content-wrapper {
    width: calc(100% - 12px * 2);
    height: calc(100% - 52px - 120px - 24px);
    overflow-y: scroll;
    margin-top: 12px;
    padding: 0 12px;
  }
}
.rotate-180 {
  transform: rotate(-180deg);
}
</style>