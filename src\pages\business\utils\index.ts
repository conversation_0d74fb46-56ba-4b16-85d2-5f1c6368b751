import { isString, isNullOrUnDef, isArray } from '@/utils/isUtils'

/**
 * @description 过滤最低价格数据(对比活动价格)
 */
export const contrastMinPriceSku = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error('It must be an array')
  }
  const _list = JSON.parse(JSON.stringify(list))
  _list.forEach((item) => {
    if (isNullOrUnDef(item.minPrice)) {
      item.minPrice = item.price
      if (!isNullOrUnDef(item.activityPrice) && item.activityPrice < item.price) {
        item.minPrice = item.activityPrice
      }
    }
  })
  const minPrice = Math.min(..._list.map((item) => item.minPrice))
  return _list.find((item) => item.minPrice === minPrice) || {}
}

/**
 * @description 过滤最低价格数据
 */
export const filterSkuMin = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error('It must be an array')
  }
  if (list.length == 1) {
    return list[0]
  }
  const minPrice = Math.min(...list.map((item) => item.price))
  return list.find((item) => item.price === minPrice) || {}
}

/**
 * @description 已售或者已兑换数量
 */
export const genSaleCount = (list: any[]) => {
  if (!isArray(list)) {
    throw new Error('It must be an array')
  }
  if (!list.length) {
    return 0
  }
  const sumCount = list.reduce((pre, item) => {
    const salePrice = item.initSaled + item.soldQty
    pre += salePrice
    return pre
  }, 0)
  return sumCount
}

/**
 * @description 销量显示
 */
export const saleComputedSum = (sale: number): string => {
  if (isNullOrUnDef(sale)) {
    throw new Error("It can't be empty")
  }
  if (isString(sale)) {
    sale = Number(sale)
  }
  const UNIT = '+'
  const maxSize = 10000
  const minSize = 10
  const spaceNum = 3
  const countAmass = [10, 100, 1000]
  const upperArr = []
  if (sale <= minSize) {
    return minSize + UNIT
  }
  if (sale >= maxSize) {
    return maxSize + UNIT
  }
  if (sale > minSize && sale < maxSize) {
    for (let i = 0; i < spaceNum; i++) {
      for (let index = 1; index < minSize; index++) {
        upperArr.push(index * countAmass[i])
      }
    }
    const diffNums = upperArr.map((num) => sale - num).filter((num) => num >= 0)
    const minDiff = Math.min(...diffNums)
    const upper = upperArr.find((num) => sale - num === minDiff) || sale
    return upper + UNIT
  }
}
