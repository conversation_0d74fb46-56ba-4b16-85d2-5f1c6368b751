<template>
    <div class="detailWarpper">
        <JLoadingWrapper>
            <div class="header">
                <div class="haeadWaepper">
                    <div class="title">
                        <span>{{ route.query.couponName }}</span>
                        <div class="idStyle">ID: 
                            <span>{{ route.query.couponId }}</span>
                            <div class="copyBtn" @click="(e)=>handleCopy(e,route.query.couponId)">
                                <van-image round width="15" height="15" fit="contain" :src="CopyBtn"/>
                            </div>
                        </div>
                    <!-- <ListCard v-for="item in listData" :data='item' :showDetail="true"></ListCard> -->
                    </div>
                    <div class="content">
                        <div class="item" v-for="item in list">
                            <div style="font-size:20px">{{item.num}}</div>
                            <div style="margin-top:4px">{{item.label}}</div>
                            </div>
                    </div>
                </div>
            </div>
            <van-pull-refresh 
                class="conentDetail" 
                v-model="groupMgrListStatusReactive.isPullLoading" 
                @refresh="onGroupMgrListRefresh"
            >
            <van-list
                :offset="50"
                v-if="listData.length"
                v-model:loading="groupMgrListStatusReactive.isNextPageLoading"
                @load="onGroupMgrListNextPageLoad"
                :finished="groupMgrListStatusReactive.isNextPageFinished"
            >
            <ListCard v-for="item in listData" :data='item'></ListCard>
            </van-list>
            <van-empty 
                v-else
                description="暂无数据" 
                :image="emptyImg" 
                :image-size="[200,200]"
            />
            </van-pull-refresh>
        </JLoadingWrapper>
    </div>
</template>
<script setup lang="ts">
import { ref,reactive,computed,onMounted } from "vue";
import CopyBtn from "@/assets/store0602Image/copyWhite.png"
import { getCouponDetailStatDataPage } from "@/services/storeApi";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import ListCard from '../components/ListCard/index.vue'
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
import emptyImg from "@/assets/store0602Image/emptyImg.png"
import { useRouter,useRoute } from "vue-router"
const route = useRoute()
const {createMessageSuccess,createMessageError,} = useMessages()
const surveyData = ref({
    getNum:0,
    unUseNum:0,
    useNum:0,
    invalidNum:0
})
const list = computed(() => [
    { num: surveyData.value.getNum, label: '领取数量' },
    { num: surveyData.value.unUseNum, label: '待使用' },
    { num: surveyData.value.useNum, label: '已使用' },
    { num: surveyData.value.invalidNum, label: '已失效' }
])
const listData = ref([])
// 复制
function handleCopy(e,con){
    e.stopPropagation()
    try{
        copyText(con)
        createMessageSuccess('复制ID成功')
    }
    catch(e){
        createMessageError('复制ID失败')
    }
}
const groupMgrListStatusReactive = reactive({
        isPullLoading:false,
        isNextPageLoading:false,
        isNextPageFinished:false
}) 
function onGroupMgrListRefresh(){
    listData.value = []
    groupMgrListStatusReactive.isPullLoading = true
    loadData()
}
const welfarePageVO = {
    current:1,
    size:50,
    total:1
}
const isPageLoadingRef = ref(false)
onMounted(async()=>{
    await loadData()
})

const loadData = async() =>{
    isPageLoadingRef.value = true
    groupMgrListStatusReactive.isNextPageLoading = false
    let param = {
        data:{
            staffId:route.query.staffId,
            csId:route.query.staffId,
            couponId:route.query.couponId
        },
        pageVO: {
            current:welfarePageVO.current,
            size: welfarePageVO.size
        },
    }
    try {
        const result = await getCouponDetailStatDataPage(param)
        surveyData.value = result.surveyData
        console.log(result.surveyData,surveyData.value)
        const {current,size,total,records} = result.couponCsDataPage
        welfarePageVO.current = Number(current)
        welfarePageVO.size = Number(size)
        welfarePageVO.total = Number(total)
        listData.value.push(...records)
        if(Number(welfarePageVO.current) * Number(welfarePageVO.size) >=  Number(welfarePageVO.total)){
            groupMgrListStatusReactive.isNextPageFinished = true
        }
    } catch (error) {
        createMessageError("获取福利券统计详情数据异常")
    }finally{
        // groupMgrListStatusReactive.isNextPageFinished = true
        groupMgrListStatusReactive.isNextPageLoading = false
        groupMgrListStatusReactive.isPullLoading = false
        isPageLoadingRef.value = false
    }
}

function onGroupMgrListNextPageLoad(){
    if(Number(welfarePageVO.current) * Number(welfarePageVO.size) <  Number(welfarePageVO.total)){
        welfarePageVO.current++
        groupMgrListStatusReactive.isNextPageLoading = true
        loadData()
    }
}
</script>
<style scoped lang="less">
.detailWarpper{
    width:100%;
    height: 100vh;

.header{
    width:100%;
    height:124px;
    // display: flex;
    background: url("@/assets/store0602Image/detailBg.png") no-repeat;
    background-size: cover;
    color: #FFFFFF;
    .haeadWaepper{
        padding: 20px 15px;
    }
    .title{
        width: 100%;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px dashed #EEEEEE;
        padding-bottom: 15px;
        // padding: 18px;
        .idStyle{
            font-size: 13px;
            display: flex;
            .copyBtn{
                margin-left: 5px;
            }
        }
    }
    .content{
        margin-top:18px;
        display:flex;
        justify-content:space-around;
        font-size: 14px;
        .item{
            text-align:center;
        }
    }
}
.conentDetail{
    width:100%;
    height: calc(100% - 134px);
    margin-top: 10px;
    overflow-y: scroll;
}
}
</style>