import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 钱包 */
export const enum storeWalletApiEnum {
  // 门店端-钱包-提现记录
  storeWithdrawRecord = "/h5/paymentAudit/page",
  // 门店端-钱包-提现
  storeWithdraw = "/h5/paymentAudit/add",
  // 门店端-钱包-获取账户余额
  storeBalance = "/h5/userAccount/get",
  // 门店端-钱包-添加银行卡
  storeAddBankCard = "/h5/csBankAccount/add",
  // 门店端-钱包-修改银行卡
  storeUpdateBankCard = "/h5/csBankAccount/update",
  // 门店端-钱包-获取银行卡
  storeBankCard = "/h5/csBankAccount/getCsBankInfo",
  // 门店端-钱包-分页检索开户行信息
  storeBankList = "/h5/csBankAccount/pageBankInfo",
  // 门店端-钱包-分页检索收支流水记录
  storeFlowList = "/h5/userAccount/pageIncomeExpend",
}

/**
 * @description 门店端-钱包-分页检索收支流水记录
 */
export function storeFlowListApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeWalletApiEnum.storeFlowList),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店端-钱包-分页检索开户行信息
 */
export function storeBankListApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeWalletApiEnum.storeBankList),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店端-钱包-获取银行卡
 */
export function storeBankCardApi(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeWalletApiEnum.storeBankCard),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店端-钱包-修改银行卡
 */
export function storeUpdateBankCardApi(_params) {
  return defHttp.put({
    url: getStoreApiUrl(storeWalletApiEnum.storeUpdateBankCard),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店端-钱包-添加银行卡
 */
export function storeAddBankCardApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeWalletApiEnum.storeAddBankCard),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店端-钱包-提现记录
 */
export function storeWithdrawRecordApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeWalletApiEnum.storeWithdrawRecord),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店端-钱包-提现
 */
export function storeWithdrawApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeWalletApiEnum.storeWithdraw),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店端-钱包-获取账户余额
 */
export function storeBalanceApi(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeWalletApiEnum.storeBalance),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}
