"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreMine = void 0;
const routeNameEnum_1 = require("@/routes/enums/routeNameEnum");
exports.StoreMine = {
    [routeNameEnum_1.RoutesName.StoreMine]: {
        path: 'pages/mine/index',
        style: {
            navigationBarTitleText: '个人中心页',
            enablePullDownRefresh: false,
            navigationBarBackgroundColor: '#fff',
        },
    },
    [routeNameEnum_1.RoutesName.StoreMyOrders]: {
        path: 'pages/order/index',
        style: {
            navigationBarTitleText: '我的订单',
            enablePullDownRefresh: false,
            navigationBarBackgroundColor: '#fff',
        },
    },
    [routeNameEnum_1.RoutesName.StoreDetail]: {
        path: 'pages/order-detail/index',
        style: {
            navigationBarTitleText: '',
            enablePullDownRefresh: false,
            navigationBarBackgroundColor: '#fff',
            navigationStyle: 'custom',
        },
    },
};
