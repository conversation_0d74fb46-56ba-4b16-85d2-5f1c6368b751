<template>
    <div class="storeLogPage">
        <JLoadingWrapper :show="isPageLoadingRef">
            <StoreSearch type="digit" v-model:value="modal.keyword" placeholder="请输入商品ID搜索" @search="handleSearch" />
            <van-pull-refresh 
                class="content-wrapper" 
                v-model="groupMgrListStatusReactive.isPullLoading" 
                @refresh="onGroupMgrListRefresh(modal.keyword)"
            >
                <van-list
                    :offset="50"
                    v-if="listData.length"
                    v-model:loading="groupMgrListStatusReactive.isNextPageLoading"
                    @load="onGroupMgrListNextPageLoad(modal.keyword)"
                    :finished="groupMgrListStatusReactive.isNextPageFinished"
                >
                <div class="conent" v-for="item in listData">
                    <div class="divList">
                        <span class="title">商品名称</span>
                        <span class="name">{{ item.productName }}</span>
                    </div>
                    <div class="divList">
                        <span class="title">商品ID</span>
                        <span>{{ item.productId }}</span>
                    </div>
                    <div class="divList" v-if="item?.sku">
                        <span class="title">SKU名称</span>
                        <span>{{ item.sku }}</span>
                    </div>
                    <div class="divList">
                        <span class="title">发货数量</span>
                        <span>{{ item.deliveryQuantity }}</span>
                    </div>
                    <div class="buttonStyle">
                        <van-button round style="color: #EF1115;background-color: #FFF4F4;" color="#FFF4F4" size="small"  @click="handelSearchLog(item.id)">&nbsp;物流轨迹&nbsp;</van-button>
                    </div>
                </div>
                </van-list>
                <van-empty 
                    v-else
                    description="暂无数据" 
                    :image="emptyImg" 
                    :image-size="[200,200]"
                />
            </van-pull-refresh>
        </JLoadingWrapper>
        <StoreLogisticsPopup v-model:show="storeLogisticsPopupShow" v-if="storeLogisticsPopupShow" :orderId="orderCode"></StoreLogisticsPopup>
    </div>
</template>
<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import emptyImg from "@/assets/store0602Image/emptyImg.png";
import StoreLogisticsPopup from './components/StoreLogisticsPopup.vue'
import {StoreLogistics} from './hooks/index'
import StoreSearch from "@/views/StoreModule/components/StoreSearch.vue";

const {getLogisticsPage,listData,
        onGroupMgrListRefresh,
        groupMgrListStatusReactive,onGroupMgrListNextPageLoad,isPageLoadingRef} = StoreLogistics()
const initParams = {
    keyword: '',
}
const modal = ref({ ...initParams })
const orderCode = ref('')
const storeLogisticsPopupShow = ref(false)
const handleSearch = () => {
    getLogisticsPage(modal.value.keyword,true);
};
onMounted(()=>{
    getLogisticsPage(modal.value.keyword,true)
})
const handelSearchLog =(data)=>{
    orderCode.value = data
    storeLogisticsPopupShow.value = true
}
</script>
<style lang="less" scoped>
:deep(.van-cell) {
    background: none !important;
    padding: 0 !important;
}
:deep(.van-field__clear, .van-field__right-icon) {
    margin-right: 0 !important;
}
.storeLogPage{
    width: 100%;
    height: 100vh;
    overflow: hidden;
    .content-wrapper{
        height: calc(100% - 54px - 10px);
        overflow-y: auto;
        margin-top: 10px;
    }
    .conent{
        width: 90%;
        margin: auto;
        padding: 0px 10px;
        background-color: white;
        margin-bottom: 10px;
        border-radius: 10px;
        font-size: 15px;
        .divList{
            display: flex;
            justify-content: space-between;
            padding: 14px 0px;
            .title{
                width: 20%;
                text-align: end;
            }
            .name{
                width: 70%;
                white-space: nowrap;      /* 强制不换行 */
                overflow: hidden;         /* 隐藏溢出内容 */
                text-overflow: ellipsis; 
                text-align: end;
            }
        }
        .buttonStyle{
            width: 100%;
            display: flex;
            justify-content: flex-end;
            padding-bottom: 10px;
        }
    }
}
</style>
