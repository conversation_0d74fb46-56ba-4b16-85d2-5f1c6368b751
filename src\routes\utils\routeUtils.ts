import { isString } from '@/utils/isUtils'
import type { RoutesName } from '../enums/routeNameEnum'
import { routesMap } from '../maps'

/**
 * @description 根据路由名称获取页面 URL
 * @param {RoutesName} routeName - 路由名称
 * @returns {string} 页面 URL
 * @throws {Error} 如果路由名称未注册或路径为空
 */
export function getPageUrlByRouteName(routeName: RoutesName): string {
  const route = routesMap[routeName]

  // 检查路由是否存在且路径为字符串
  if (route && isString(route.path)) {
    return `/${route.path}` // 返回完整的页面 URL
  }

  // 抛出错误，说明路由未注册或路径为空
  throw new Error(`${routeName} 未在 map 中注册或 path 为空`)
}
