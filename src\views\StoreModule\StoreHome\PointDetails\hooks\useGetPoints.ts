import { ref, reactive } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { IntegralEnum, StoreIntegralRouteTypeEnum } from "@/views/StoreModule/enums";
import { getIntegralDetail } from "@/services/storeApi";

export default function useGetPoints(_params: {
  io: number;
  customerId?: string;
  requestPageSource?: StoreIntegralRouteTypeEnum;
}) {
  const isPageLoadingRef = ref(false);

  const { createMessageSuccess, createMessageError } = useMessages();
  /** 积分数据 */
  const pointsList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  const model = ref({
    io: _params.io ?? IntegralEnum.INCOME,
    customerId: _params.customerId ?? null,
    requestPageSource: _params.requestPageSource ?? StoreIntegralRouteTypeEnum.MY_INTEGRAL,
  });

  /** 分页 */
  const pageVO = reactive({
    size: 30,
    current: 1,
    total: 0,
  });

  /** 获取搜索参数 */
  function getSearchParams() {
    return {
      data: {
        ...model.value,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getPointsDetailsList();
    }
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    getPointsDetailsList();
  }

  /** 初始化 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /**
   * 获取积分明细列表
   */
  async function getPointsDetailsList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getIntegralDetail(_params);

      // 更新订单列表
      if (current === 1) {
        pointsList.value = records;
      } else if (records.length) {
        pointsList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  return {
    isPageLoadingRef,
    pointsList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    model,
    onLoad,
    onRefresh,
    getPointsDetailsList,
  };
}
