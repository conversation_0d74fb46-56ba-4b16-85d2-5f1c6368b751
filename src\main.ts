import { createApp } from "vue";
import App from "./App.vue";
import { setupRouter } from "./router";
import "./assets/normalize.css";
import "@/styles/default.less";
import "virtual:svg-icons-register";
import { setupStores } from "./stores";
import { setupDirectives } from "./directives";
import "@vant/touch-emulator";
import { initDefHttp } from "./services";

async function init() {
  try {
    await initDefHttp();
  } catch (e) {
  } finally {
    const app = createApp(App);
    setupStores(app);
    setupRouter(app);
    setupDirectives(app);
    app.mount("#app");
  }
}
init();
