import { createSSRApp } from 'vue'
import App from './App.vue'
import { setupStores } from './stores' // 引入pinia
import 'virtual:uno.css'
import '@/style/index.scss'
// #ifdef H5
import {
  Button,
  Tabs,
  Tab,
  List,
  PullRefresh,
  Popover,
  ConfigProvider,
  Overlay,
  Loading,
} from 'vant'
import 'vant/lib/index.css'
// #endif

export function createApp() {
  const app = createSSRApp(App)
  // #ifdef H5
  app
    .use(Button)
    .use(Tabs)
    .use(Tab)
    .use(List)
    .use(PullRefresh)
    .use(Popover)
    .use(ConfigProvider)
    .use(Overlay)
    .use(Loading)
  // 额外注册别名
  app.component('JTabs', Tabs)
  app.component('JTab', Tab)
  app.component('JList', List)
  app.component('JPullRefresh', PullRefresh)
  app.component('JPopover', Popover)
  app.component('JConfigProvider', ConfigProvider)
  app.component('JOverlay', Overlay)
  app.component('JLoading', Loading)
  // 之后模板里可用 <VTabs>、<VTab> 或原本的 <van-tabs>、<van-tab>
  // #endif
  setupStores(app)
  return { app }
}
