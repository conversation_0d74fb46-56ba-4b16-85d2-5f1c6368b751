<template>
  <VanPopup v-model:show="showRef" round position="bottom" @close="emit('update:show', false)">
    <VanForm label-align="top" class="wrapper" @submit="handleClickExport">
      <!-- 表单 -->
      <div
        class="form-container"
        :class="{'form-container-buttom': exportTypeRef !== StoreDataExportEnum.A01_MEMBER_DATA}"
      >
        <!-- 导出会员数据 -->
        <template v-if="exportTypeRef === StoreDataExportEnum.A01_MEMBER_DATA">
          <div class="A01">直接导出即可</div>
        </template>

        <template
          v-if="[
            StoreDataExportEnum.B01_COUPON_CLAIM_STATS, 
            StoreDataExportEnum.B02_COUPON_USE_STATS,
          ].includes(exportTypeRef)"
        >
          <!-- 福利券分类ID -->
          <div class="field-wrapper">
            <VanField
              v-model="modelRef.couponCateId"
              label="福利券分类ID"
              name="couponCateId"
              required
              placeholder="请输入福利券分类ID"
              :border="false"
              :rules="[{ required: true, message: '请输入福利券分类ID' }]"
            />
          </div>
        </template>

        <template
          v-if="[
            StoreDataExportEnum.A02_MEMBER_CONSUMPTION, 
            StoreDataExportEnum.D01_PAID_ORDERS_WITHOUT_REFUNDS,
            StoreDataExportEnum.D02_PAID_ORDERS_WITH_REFUNDS,
            StoreDataExportEnum.D03_REFUNDED_ORDERS,
            StoreDataExportEnum.B01_COUPON_CLAIM_STATS, 
            StoreDataExportEnum.B02_COUPON_USE_STATS,
          ].includes(exportTypeRef)"
        >
          <!-- 开始时间 -->
          <div class="field-wrapper">
            <VanField
              v-model="modelRef.startTime"
              label="选择开始时间"
              name="startTime"
              readonly
              required
              placeholder="请选择"
              :border="false"
              :rules="[{ required: true, message: '请输入开始时间' }]"
              @click="handleClickInput('startTime')"
            />
          </div>
          <!-- 结束时间 -->
          <div class="field-wrapper">
            <VanField
              v-model="modelRef.endTime"
              label="选择结束时间"
              name="endTime"
              :border="false"
              readonly
              required
              placeholder="请选择"
              :rules="[{ required: true, message: '请输入结束时间' }]"
              @click="handleClickInput('endTime')"
            />
          </div>
        </template>

        <template v-if="[StoreDataExportEnum.C01_DAILY_VIEWING_TIME].includes(exportTypeRef)">
          <!-- 选择时间 -->
          <div class="field-wrapper">
            <VanField
              v-model="modelRef.selectTime"
              label="选择时间"
              name="selectTime"
              readonly
              required
              placeholder="请选择"
              :border="false"
              :rules="[{ required: true, message: '请输入选择时间' }]"
              @click="handleClickInput('selectTime')"
            />
          </div>
        </template>
      </div>

      <!-- footer -->
      <div class="footer">
        <VanRow justify="space-between" gutter="8">
          <VanCol span="12">
            <VanButton round type="default" @click="handleClickReset" style="width: 100%;height: 40px;">重置</VanButton>
          </VanCol>
          <VanCol span="12">
            <VanButton
              type="danger"
              round
              block
              native-type="submit"
              style="width: 100%;height: 40px;"
            >
              导出
            </VanButton>
          </VanCol>
        </VanRow>
      </div>
    </VanForm>
    <!-- 时间选择 -->
    <JselectTime v-model:show="isShowSelectTime" @selectTime="handleSelectTime" :precision="exportTypeRef !== StoreDataExportEnum.C01_DAILY_VIEWING_TIME" />
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed, watch } from "vue";
import { showToast } from "vant";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { StoreDataExportEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { useBoolean } from "@/views/StoreModule/hooks";
import { exportRecordApi } from "@/services/storeApi";
/** 相关组件 */
import JselectTime from "@/views/StoreModule/components/JselectTime.vue";
import dayjs from "dayjs";

defineOptions({ name: "StoreDataExportForm" });

/** props */
const props = withDefaults(defineProps<{
  exportType: StoreDataExportEnum;
  show: boolean;
}>(), {
});

/** emit */
const emit = defineEmits<{
  (e: 'update:show', show: boolean): void;
}>();

const { exportType: exportTypeRef } = toRefs(props);
const { bool: isShowSelectTime, setTrue: setIsShowSelectTime, setFalse: setIsShowSelectTimeFalse } = useBoolean();
const { createMessageError } = useMessages();
const { routerPushByRouteName } = useRouterUtils();

const showRef = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

/** 参数初始化 */
const initParams = {
  /** 福利券分类ID */
  couponCateId: null,
  startTime: null,
  endTime: null,

  /** 选择时间 */
  selectTime: null,
};
const modelRef = ref({...initParams});
const currentSelectModel = ref(null);

function handleClickInput(type: string) {
  currentSelectModel.value = type;
  setIsShowSelectTime();
}

/** 选择时间 */
/** 选择时间 */
function handleSelectTime(selectTime: { date: string; time: string | null }) {
  if (!selectTime.date) {
    console.error("selectTime.date is required");
    return;
  }

  let formattedTime;
  
  // 如果是开始时间字段
  if (currentSelectModel.value === 'startTime') {
    // 如果没有选择具体时间，默认设置为 00:00:00
    formattedTime = selectTime.time?.trim() 
      ? `${selectTime.date} ${selectTime.time.trim()}`
      : `${selectTime.date} 00:00:00`;
  } 
  // 如果是结束时间字段
  else if (currentSelectModel.value === 'endTime') {
    // 如果没有选择具体时间，默认设置为 23:59:59
    formattedTime = selectTime.time?.trim() 
      ? `${selectTime.date} ${selectTime.time.trim()}`
      : `${selectTime.date} 23:59:59`;
  }
  else {
    // 其他字段（如selectTime）保持原样
    formattedTime = selectTime.time?.trim() 
      ? `${selectTime.date} ${selectTime.time.trim()}`
      : selectTime.date;
  }

  // 存储到 modelRef
  modelRef.value[currentSelectModel.value] = formattedTime;
}

/** 重置 */
function handleClickReset() {
  modelRef.value = { ...initParams };
}

/** 
 * @description 导出数据
 */
 async function handleClickExport() {
  try {
    const exportStrategies = {
      [StoreDataExportEnum.A01_MEMBER_DATA]: () => ({
        params: { type: StoreDataExportEnum.A01_MEMBER_DATA },
        message: "会员数据"
      }),
      [StoreDataExportEnum.A02_MEMBER_CONSUMPTION]: () => ({
        params: { 
          type: StoreDataExportEnum.A02_MEMBER_CONSUMPTION,
          startTime: modelRef.value.startTime,
          endTime: modelRef.value.endTime
        },
        message: "会员消费数据"
      }),
      [StoreDataExportEnum.D01_PAID_ORDERS_WITHOUT_REFUNDS]: () => ({
        params: { 
          type: StoreDataExportEnum.D01_PAID_ORDERS_WITHOUT_REFUNDS,
          startTime: modelRef.value.startTime,
          endTime: modelRef.value.endTime
        },
        message: "已支付不含退款订单数据"
      }),
      [StoreDataExportEnum.D02_PAID_ORDERS_WITH_REFUNDS]: () => ({
        params: { 
          type: StoreDataExportEnum.D02_PAID_ORDERS_WITH_REFUNDS,
          startTime: modelRef.value.startTime,
          endTime: modelRef.value.endTime
        },
        message: "已支付包含退款订单数据"
      }),
      [StoreDataExportEnum.D03_REFUNDED_ORDERS]: () => ({
        params: { 
          type: StoreDataExportEnum.D03_REFUNDED_ORDERS,
          startTime: modelRef.value.startTime,
          endTime: modelRef.value.endTime
        },
        message: "已退款订单数据"
      }),
      [StoreDataExportEnum.B01_COUPON_CLAIM_STATS]: () => ({
        params: { 
          type: StoreDataExportEnum.B01_COUPON_CLAIM_STATS,
          startTime: modelRef.value.startTime,
          endTime: modelRef.value.endTime,
          couponCateId: modelRef.value.couponCateId
        },
        message: "福利券领取时间统计数据"
      }),
      [StoreDataExportEnum.B02_COUPON_USE_STATS]: () => ({
        params: { 
          type: StoreDataExportEnum.B02_COUPON_USE_STATS,
          startTime: modelRef.value.startTime,
          endTime: modelRef.value.endTime,
          couponCateId: modelRef.value.couponCateId
        },
        message: "福利券使用时间统计数据"
      }),
      [StoreDataExportEnum.C01_DAILY_VIEWING_TIME]: () => ({
        params: { 
          type: StoreDataExportEnum.C01_DAILY_VIEWING_TIME,
          startTime: dayjs(modelRef.value.selectTime).format('YYYY-MM-DD 00:00:00'),
          endTime: dayjs(modelRef.value.selectTime).format('YYYY-MM-DD 23:59:59')
        },
        message: "会员每日观看时长数据"
      })
    };

    const strategy = exportStrategies[exportTypeRef.value];
    if (!strategy) {
      throw new Error("未知的导出类型");
    }

    const { params, message } = strategy();
    const resp = await exportRecordApi(params);
    if (resp) {
      showToast(`导出${message}成功`);

      emit('update:show', false);
      toExportRecord();
    }
  } catch (error) {
    createMessageError(`导出失败：${error}`);
  }
}

/** 跳转导出记录 */
function toExportRecord() {
  routerPushByRouteName(RoutesName.StoreExportRecord);
}

/** 监听 */
watch(() => props.show, (val) => {
  if (val) {
    modelRef.value = { ...initParams };
  }
});
</script>

<style lang="less" scoped>
.wrapper {
  padding: 12px;
  padding-bottom: env(safe-area-inset-bottom);
  padding-top: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .form-container {
    flex: 1;
    .A01 {
      width: 100%;
      height: 146px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 26px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .field-wrapper {
      margin-bottom: 12px;
    }
    :deep(.van-field) {
      padding: 0;
    }
    :deep(.van-field__label) {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 26px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;
    }
    :deep(.van-field__control) {
      background: #F8F8F8;
      height: 36px;
      border-radius: 4px;
      padding: 0 12px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .form-container-buttom {
    margin-bottom: 48px;
  }
  .footer {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 12px;

    :deep(.van-button__text) {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }
  }
}
</style>
