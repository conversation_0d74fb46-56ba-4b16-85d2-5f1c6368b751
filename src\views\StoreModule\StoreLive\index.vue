<template>
    <div class="live-sensitive-words">
        <div class="user">
            <div class="userInfo">
                <van-image round width="40" height="40" fit="contain"
                    :src="userStore.storeUserInfo.img || defaultAvatar" />
                <span>{{ userStore.storeUserInfo.nickname || '-' }}</span>

            </div>
            <div class="center" @click="handelPersonalCenter">个人中心</div>
        </div>
        <WelfareWidget v-if="liveInfo.hsLiveLink" :liveInfo="liveInfo" />
        <div class="warpper">
            <iframe v-if="liveInfo.status == STREAM_STATUS.PROGRESS" allowfullscreen="true" allow="fullscreen;screen-wake-lock;camera;microphone"
                :src="liveInfo.hsLiveLink ? liveInfo.hsLiveLink + '?platform=mobile' : ''"></iframe>
            <div class="unbound" v-else-if="liveInfo.status == STREAM_STATUS.UNBOUND">
                <div>
                    <div>请先扫【会员邀请码】绑定门店，再观看直播</div>
                    <div class="marginTopStyle">
                        <van-button type="default" @click="handelScan">扫 一 扫</van-button>
                    </div>
                    <div class="backHome" @click="backHome">返回首页</div>
                </div>
            </div>
            <div class="unbound" v-else>
                <div>
                    <div class="imgStyle">
                        <van-image fit="contain"
                            :src="liveInfo.status == STREAM_STATUS.EFFICACY ? liveEfficacyImg : liveEmptyImg" />
                    </div>
                    <div class="marginTopStyle">
                        <div v-if="liveInfo.status == STREAM_STATUS.END">直播已结束</div>
                        <div v-else-if="liveInfo.status == STREAM_STATUS.EFFICACY">链接已失效</div>
                        <div v-else-if="liveInfo.status == STREAM_STATUS.DELEAT">课程已被删除</div>
                    </div>
                    <div class="marginTopStyle">
                        <van-button type="primary" @click="backHome">返回首页</van-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useUserStore } from '@/stores/modules/user';
import defaultAvatar from "@/assets/image/system/account/defaultAvatar.jpg"
import liveEmptyImg from "@/assets/store/liveEmptyImg.png"
import liveEfficacyImg from "@/assets/store/efficacy.png"
import { parseUrlParams } from '@/utils/http/urlUtils';
import { useMessages } from "@/hooks/useMessage";
const { createMessageSuccess, createMessageError } = useMessages()
const userStore = useUserStore()
import { useRouter, useRoute } from "vue-router"
import { RoutesName } from "@/enums/routes";
import { routesMap } from "@/router/maps";
import { getHsLiveLink,type HsLiveLinkResponse } from "@/services/storeApi";
import { wxSdkInit } from "@/utils/wxSDKUtils";
import { getJSSDKConfig } from "@/services/storeApi";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system"
import { is, isIOSEnv } from "@/utils/isUtils"
import { CacheConfig } from "@/utils/cache/config"
import { Cache_Key } from "@/enums/cache";
import { createCacheStorage } from "@/utils/cache/storageCache"
import WelfareWidget from '@/views/StoreModule/StoreLive/components/WelfareWidget/index.vue'
import { isInFrame } from '@/utils/envUtils';
import { MessageEventEnum, useWindowMessage } from '@/hooks/useWindowMessage';

const {sendMessageToWindows} = useWindowMessage()
const router = useRouter()
const route = useRoute()
const status = ref(0)
const orderCodeCache = createCacheStorage(CacheConfig.StoreOrderCache)
orderCodeCache.remove()
const liveInfo = ref<HsLiveLinkResponse>({
    status: 0,
    hsLiveLink: '',
    /* 直播间id */
    liveRoomId: '',
})
const enum STREAM_STATUS {
    PROGRESS = 0,//直播中
    UNBOUND = 1,//已注册未绑定门店
    DELEAT = 2, // 直播课已删除
    END = 3,//直播已结束
    EFFICACY = 4, // 链接已失效
}
const initJSSDKFlagRef = ref(false)
onMounted(async () => {
    let params = parseUrlParams(location.search)
    try {
        const resp = await getHsLiveLink(params.state as unknown as string)
        liveInfo.value = resp
        if (liveInfo.value.status == 1) {
            initJSSDKFlagRef.value = await initJSSDK()
        }
    }
    catch (e) {
        createMessageError(`'请求失败':${e}`)
    }
})
window.addEventListener('storage', function (e: StorageEvent) {
    if (e.key === Cache_Key.StoreOrderCode) {
        const orderCode = orderCodeCache.get() as string
        if(!orderCode){
            return
        }
        // const backUrl = `${location.origin}/st/cashier?orderCode=${orderCode}&state=${route.query.state}`
        // location.href = backUrl
        if(isInFrame()){
            const url = `${location.origin}/st/cashier?orderCode=${orderCode}&state=${route.query.state}&isLive=1`
            if(isIOSEnv()){
                sendMessageToWindows(MessageEventEnum.sendUrlToWindow,url)
            }
            else{
                window.open(url)
            }
           
        }
        else{
            router.replace({
                name:RoutesName.StoreCashier,
                query:{
                    ...route.query,
                    orderCode,
                    isLive:1
                }
            })
        }
       
    }
})
const systemStore = useSystemStoreWithoutSetup()
async function initJSSDK() {
    const _url = isIOSEnv() ? systemStore.entryUrl : `${window.location.origin}${route.fullPath}`
    try {
        const { signature, nonceStr, timestamp } = await getJSSDKConfig(_url)
        try {
            const stateCache = createCacheStorage(CacheConfig.StoreToken);
            const _stateInfo = stateCache.get();
            const JSSDK_Config_params = {
                debug: false,
                appId: _stateInfo.wxappId,
                timestamp, // 必填，生成签名的时间戳
                nonceStr, // 必填，生成签名的随机串
                signature,// 必填，签名
                jsApiList: ['scanQRCode'],
            }
            try {
                try {
                    await wxSdkInit(JSSDK_Config_params)
                    return true
                }
                catch (err) {
                    console.log('Init WeChat JSSDK error:', err);
                    return false
                }
            }
            catch (err) {
                console.log('Init WeChat JSSDK error:', err);
                return false
            }
        }
        catch (err) {
            console.log('getJSSDKConfig:', err);
        }
    }
    catch (e) {

    }
}
const handelScan = async () => {
    if (initJSSDKFlagRef.value) {
        window.wx.scanQRCode({
            needResult: 0,
            scanType: ["qrCode", "barCode"],
            success: function (res) {
                var result = res.resultStr;
            }
        });
    } else {
        createMessageError('JSSDK Not Ready')
    }
}
const handelPersonalCenter = () => {
    //跳转到个人中心
    router.push(routesMap[RoutesName.StoreMine]);
}
const backHome = () => {
    //返回首页
    router.push(routesMap[RoutesName.StoreHome]);
}



</script>
<style lang="less" scoped>
.live-sensitive-words {
    width: 100%;
    height: 100vh;
    background-color: white;

    .user {
        width: 95%;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: auto;
        font-size: 15px;
        padding-top: 5px;
    }

    .userInfo {
        display: flex;
        align-items: center;

        span {
            margin-left: 10px;
        }
    }

    .center {
        font-size: 14px;
    }

    .warpper {
        width: 100%;
        height: calc(100% - 53px);
        margin-top: 8px;

        iframe {
            width: 100%;
            height: 100%;
        }

        .unbound {
            width: 100%;
            height: 100%;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;

            .backHome {
                color: #1677FF;
                margin-top: 30px;
            }

            .marginTopStyle {
                margin-top: 20px;
            }

            .imgStyle {
                width: 240px;
                height: 150px;

                .van-image {
                    width: 100%;
                    height: 100%;
                }

            }
        }
    }

}
</style>
