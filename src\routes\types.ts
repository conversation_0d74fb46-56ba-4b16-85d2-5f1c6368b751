import type { RoutesName } from './enums/routeNameEnum'

export interface NavigateDefaultParams {
  url: RoutesName
  /** 跳转参数，会转换成键值对拼接在url后面 */
  props?: Record<string, string | number>
  success?: () => void
  fail?: () => void
  complete?: () => void
}

export interface NavigateToParams extends NavigateDefaultParams {
  events?: {}
}

export interface NavigateBackParams {
  delta?: number
  success?: () => void
  fail?: () => void
  complete?: () => void
}

export interface PageStyle {
  /** 导航栏背景颜色 */
  navigationBarBackgroundColor?: string
  /** 导航栏文字样式 */
  navigationBarTextStyle?: string
  /** 导航栏标题文字 */
  navigationBarTitleText: string
  /** 导航栏阴影 */
  navigationBarShadow?: string
  /** 导航样式，默认为 'default' */
  navigationStyle?: 'default' | 'custom'
  /** 是否禁用滚动 */
  disableScroll?: string
  /** 页面背景颜色 */
  backgroundColor?: string
  /** 背景文字样式，默认为 'dark' */
  backgroundTextStyle?: 'dark' | 'light'
  /** 是否启用下拉刷新 */
  enablePullDownRefresh?: boolean
  /** 触底距离 */
  onReachBottomDistance?: number
  /** 页面方向，默认为 'auto' */
  pageOrientation?: 'auto' | 'portrait' | 'landscape'
  /** 微信小程序特有配置 */
  mpWeixin?: {}
  /** 使用的组件 */
  usingComponents?: {}
  /** 渲染模式 */
  renderingMode?: string
}

export interface RoutePageConfig {
  /** 路由路径 */
  path: string
  /** 页面样式配置 */
  style?: PageStyle
  /** 是否需要登录 */
  needLogin?: boolean
}

export type RoutesMap = Partial<Record<RoutesName, RoutePageConfig>>
