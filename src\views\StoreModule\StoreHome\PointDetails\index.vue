<template>
  <div class="point-wrapper">
    <div class="point-header">
      <div class="point-header__title">积分余额</div>
      <div class="point-header__content">{{ integralBalance }}</div>
    </div>
    <!-- 内容 -->
    <div class="point-content-containter">
      <!-- tabs -->
      <VanTabs
        :style="{ '--van-tabs-line-height': '40px', height: '40px', width: '80%', margin: '0 auto'}"
        v-model:active="activeTabRef"
        color="#EF1115"
        title-inactive-color="#333333"
        line-width="36px"
        line-height="2px"
        swipeable
      >
        <VanTab v-for="item in pointsStatusOptions" :key="item.value" :name="item.value">
          <template #title>
            <span class="tab-title">{{ item.label }}</span>
          </template>
        </VanTab>
      </VanTabs>
      <div class="point-content">
        <PointsList ref="pointsListRef" :pointType="activeTabRef" :userId="userIdRef" :type="typeRef" />
      </div>
    </div>
    <!-- 加减积分 -->
    <div class="footer">
      <VanButton
        v-if="typeRef == StoreIntegralRouteTypeEnum.SCAN_INTEGRAL"
        type="danger"
        block
        round
        @click="handleAddSubtractPoints"
      >
        加减积分
      </VanButton>
      <VanButton
        v-if="typeRef == StoreIntegralRouteTypeEnum.MY_INTEGRAL"
        type="danger"
        block
        round
        @click="handleToPointsStore"
      >
        去积分商城兑换礼品
      </VanButton>
    </div>
    <!-- 加减积分 -->
    <AddSubtractPoints 
      v-model:show="showAddSubtractPointsRef" 
      :userId="props.userId" 
      :unionId="props.unionId" 
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, onMounted } from "vue";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import useGetIntegralBalance from "./hooks/useGetIntegralBalance"
import { IntegralEnum, StoreIntegralRouteTypeEnum } from "@/views/StoreModule/enums";
/** 相关组件 */
import PointsList from "./components/PointsList.vue";
import AddSubtractPoints from "./components/AddSubtractPoints.vue";

defineOptions({ name: 'StoreHomePointDetails' });

/** props */
const props = defineProps<{
  type: StoreIntegralRouteTypeEnum;
  userId: string;
  unionId: string;
}>();

const { type: typeRef, userId: userIdRef } = toRefs(props);
const { routerPushByRouteName } = useRouterUtils();
const { integralBalance, queryIntegralBalance } = useGetIntegralBalance({
  customerId: userIdRef.value,
  requestPageSource: typeRef.value
});
const pointsListRef = ref<InstanceType<typeof PointsList> | null>();

/** tab */
const activeTabRef = ref(IntegralEnum.INCOME);
const showAddSubtractPointsRef = ref(false);

/** 积分状态 */
const pointsStatusOptions = [
  {
    label: '收入',
    value: IntegralEnum.INCOME,
  },
  {
    label: '支出',
    value: IntegralEnum.EXPEND,
  },
];

function handleAddSubtractPoints() {
  showAddSubtractPointsRef.value = true;
}

function handleToPointsStore() {
  routerPushByRouteName(RoutesName.StoreIntegralMall);
}

function handleSuccess() {
  queryIntegralBalance();
  pointsListRef.value && pointsListRef.value.onRefresh();
}

/** 组件挂载 */
onMounted(() => {
  queryIntegralBalance();
});
</script>

<style lang="less" scoped>
.point-wrapper {
  width: 100%;
  height: calc(100vh - env(safe-area-inset-bottom));
  background-color: #FFFFFF;
  .point-header {
    width: 100%;
    height: 280px;
    background: url(@/assets/storeImage/storeHome/point/pointBg.png) no-repeat;
    background-size: 100% 100%;
    margin-top: -32px;
    padding: 58px 32px 24px 32px;
    box-sizing: border-box;
    .point-header__title {
      height: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .point-header__content {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: bold;
      font-size: 44px;
      color: #FFFFFF;
      line-height: 48px;
      text-shadow: 0px 2px 2px rgba(238,154,35,0.63);
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-top: 8px;
    }
  }
  .point-content-containter {
    width: 100%;
    height: calc(100% - 128px - 60px);
    background-color: #fff;
    margin-top: -120px;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    .tab-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .point-content {
      width: 100%;
      height: calc(100% - 40px);
    }
  }
  .footer {
    height: 60px;
    padding: 8px 12px;
    box-sizing: border-box;
    background-color: #FFFFFF;
  }
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
