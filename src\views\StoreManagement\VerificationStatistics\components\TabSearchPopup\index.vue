<template>
  <van-popup
    :show="props.show"
    teleport="body"
    position="bottom"
    round
    closeable
    close-icon="close"
    @click-close-icon="handleClose"
    @click-overlay="handleClose"
    style="height: auto;"
  >
    <div class="search-tag">
      <div class="searchItem">
        <!-- 订单类型 -->
        <div class="form-item" style="margin-top: 24px;">
          <span>订单类型</span>
          <VanPopover v-model:show="showPopover" :show-arrow="false">
            <template #reference>
              <div class="order-type">
                <span class="title">{{ orderTypeLabel }}</span>
                <img :src="pullDownSrc" alt="" class="order-type-icon" :class="{ 'rotate-180': showPopover }" />
              </div>
            </template>
            <div style="width: calc(100vw - 24px);padding: 12px;box-sizing: border-box;">
              <div
                v-for="item in OrderTypeOptions"
                :key="item.value"
                class="order-type-item"
                @click="handleOrderTypeChange(item)"
              >
                <span :class="{ 'order-type-active': item.value === orderType }">{{ item.label }}</span>
              </div>
            </div>
          </VanPopover>
        </div>
        <van-field
          v-if="userStore.storeUserInfo.type == StatisticsEnum.IsStoreManager"
          v-model="storeClerkInput"
          label="归属店员"
          placeholder="请输入店员编号"
          label-align="top"
          type="digit"
          :border="false"
          class="input"
          style="padding: 0px;margin-bottom: 24px;margin-top: 24px;"
        />
        <van-field
          v-model="membersInput"
          label="归属会员"
          placeholder="请输入会员编号"
          label-align="top"
          type="digit"
          :border="false"
          class="input"
          style="padding: 0px;margin-bottom: 24px;margin-top: 24px;"
        />
        <van-field
          v-model="productName"
          label="商品名称"
          placeholder="请输入商品名称"
          label-align="top"
          :border="false"
          class="input"
          style="padding: 0px;margin-bottom: 24px;margin-top: 24px;"
        />
      </div>
      <div class="footer">
        <van-button style="color:black;width:45%;" @click="handleReset" round type="default" color="#F8F8F8">
          重置
        </van-button>
        <van-button style="width:45%;" @click="handleConfirm" round type="danger">查询</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useUserStore } from '@/stores/modules/user';
import {StatisticsEnum} from '@/views/StoreManagement/WelfareVoucherStatistics/type';
import { OrderStatisticsTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import pullDownSrc from "@/assets/storeImage/storeHome/pull-down.png";

export interface ModelType {
  orderType: OrderStatisticsTypeEnum;
  staffShortId: string;
  csShortId: string;
  productName: string;
}

const userStore = useUserStore();
const props = withDefaults(defineProps<{
  show: boolean;
  model: ModelType;
}>(),{
  show:false,
})
const emits = defineEmits<{
  (e:'update:show',val:boolean):void,
  (e:"close"):void;
  (e:'update:search',val:any):void,
}>()

const storeClerkInput = ref('')
const membersInput = ref('')
const productName = ref('')

/** 订单类型 */
const orderType = ref(null);
const OrderTypeOptions = [
  { label: '全部', value: null },
  { label: '销售订单', value: OrderStatisticsTypeEnum.SHOPPING },
  { label: '福利券兑换', value: OrderStatisticsTypeEnum.COUPON },
  { label: '积分兑换', value: OrderStatisticsTypeEnum.INTEGRAL },
];
const orderTypeLabel = computed(() => {
  const found = OrderTypeOptions.find((item) => item.value == orderType.value);
  return found?.label || '全部';
});

const showPopover = ref(false);
function handleOrderTypeChange(item) {
  orderType.value = item.value;
  showPopover.value = false;
};

function handleConfirm() {
  let param = {
    orderType: orderType.value,
    staffShortId: storeClerkInput.value,
    csShortId: membersInput.value,
    productName: productName.value
  };
  emits('update:search',param);
  handleClose();
}

function handleReset() {
  orderType.value = null;
  storeClerkInput.value = '';
  membersInput.value = '';
  productName.value = '';
}

function handleClose() {
  handleReset();
  emits('update:show',false);
}

/** 监听 */
watch(() => props.show, (newVal) => {
  if (newVal) {
    const { orderType: orderTypeVal, staffShortId, csShortId, productName: productNameVal } = props.model;
    orderType.value = orderTypeVal;
    storeClerkInput.value = staffShortId;
    membersInput.value = csShortId;
    productName.value = productNameVal;
  }
}, { immediate: true });
</script>

<style scoped lang="less">
.search-tag{
  margin-top: 10px;
  .searchItem{
    padding: 18px 12px;
    .input.van-field {
      :deep(.van-field__body) {
        input {
          height: 40px;
          background: #f8f8f8;
          border-radius: 4px;
          padding-left: 8px;
        }
      }
    }
    .form-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      span {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .order-type {
        height: 40px;
        background: #F8F8F8;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        box-sizing: border-box;
        .title {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .order-type-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
  .footer{
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    margin-bottom: env(safe-area-inset-bottom);
    padding: 12px;
    :deep(.van-button__text) {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
.order-type-item {
  width: 100%;
  padding: 8px 12px;
  span {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    font-style: normal;
    text-transform: none;
  }
  .order-type-active {
    color: #EF1115 !important;
  }
}
:deep(.van-cell__right-icon) {
  line-height: 40px;
}
:deep(.van-field__label) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #333333;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8px;
}
:deep(.van-field__control) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.rotate-180 {
  transform: rotate(-180deg);
}
</style>
