<template>
  <VanDialog
    v-model:show="showRef"
    title="提示"
    show-cancel-button
    confirm-button-text="确定"
    confirm-button-color="#EF1115"
    @confirm="confirm"
  >
    <div class="wrapper">
      <span>是否取消该订单</span>
    </div>
  </VanDialog>
</template>

<script lang="ts" setup>
import { computed } from "vue";

defineOptions({ name: 'CancelOrder' });

/** props */
const props = defineProps<{
    show: boolean;
}>();

/** emits */
const emits = defineEmits<{
    (e: 'update:show', show: boolean): void
    (e: 'confirm'): void
}>();

const showRef = computed({
  get() {
    return props.show;
  },
  set(show: boolean) {
    emits('update:show', show);
  }
});

function confirm() {
  emits('confirm');
}
</script>

<style lang="less" scoped>
.wrapper {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  span {
    font-family: Source <PERSON>, Source <PERSON> CN;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}
</style>
