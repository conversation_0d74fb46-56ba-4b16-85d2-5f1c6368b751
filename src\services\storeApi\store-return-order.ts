import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 退货订单 */
export const enum storeReturnOrderApiEnum {
  // 门店退货订单-分页列表
  pageListStoreReturnOrder = "/h5/afterSaleRecord/pageListStoreReturnOrder",
  // 门店退货订单-确认退货
  confirmReturn = "/h5/afterSaleRecord/confirmReturn",
  // 门店退货订单-提交物流单
  submitLogistics = "/h5/afterSaleRecord/submitReturn",
  // 物流公司
  logisticsCompany = "/shipCompany/page",
  // 是否开启指定的业务流程审核功能
  isBusinessAudit = "/processDef/hasEnabledProcessDef",
}

/**
 * @description 是否开启指定的业务流程审核功能
 */
export function isBusinessAudit(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeReturnOrderApiEnum.isBusinessAudit),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 物流公司
 */
export function getLogisticsCompany(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeReturnOrderApiEnum.logisticsCompany),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 退货订单列表
 */
export function getReturnOrderList(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeReturnOrderApiEnum.pageListStoreReturnOrder),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 确认退货
 */
export function confirmReturn(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeReturnOrderApiEnum.confirmReturn),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 提交物流单
 */
export function submitLogistics(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeReturnOrderApiEnum.submitLogistics),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}
