<template>
    <div class="store_after_sales_status_wrapper">
        <img :src="iconSrc" alt="" />
        <span class="title">{{ afterSaleStatusMap[stateRef] }}</span>
    </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
import { AfterSaleStatusEnum } from "@/views/StoreModule/enums";
import { afterSalesStatusLabels } from "@/views/StoreModule/constants";
/** 静态资源 */
import acceptanceSrc from "@/assets/storeImage/storeAfterSales/acceptance.png";
import refuseRefundSrc from "@/assets/storeImage/storeAfterSales/refuseRefund.png";
import refundProgressSrc from "@/assets/storeImage/storeAfterSales/refundProgress.png";
import refundCompletedSrc from "@/assets/storeImage/storeAfterSales/refundCompleted.png";
import salesReturnSrc from "@/assets/storeImage/storeAfterSales/salesReturn.png";
import returnClosedSrc from "@/assets/storeImage/storeAfterSales/returnClosed.png";
import pendingMerchantReceiptSrc from "@/assets/storeImage/storeAfterSales/pendingMerchantReceipt.png";
import cancelSrc from "@/assets/storeImage/storeAfterSales/cancel.png";

defineOptions({ name: 'StoreAfterSalesStatus' });

/** props */
const props = defineProps<{
    state: AfterSaleStatusEnum;
}>();

const { state: stateRef } = toRefs(props);

const iconSrc = computed(() => {
    const srcMap = {
        [AfterSaleStatusEnum.PENDING_MERCHANT]: acceptanceSrc,
        [AfterSaleStatusEnum.MERCHANT_REFUSED]: refuseRefundSrc,
        [AfterSaleStatusEnum.REFUNDING]: refundProgressSrc,
        [AfterSaleStatusEnum.PENDING_PAYMENT]: refundProgressSrc,
        [AfterSaleStatusEnum.REFUND_COMPLETED]: refundCompletedSrc,
        [AfterSaleStatusEnum.PENDING_RETURN]: salesReturnSrc,
        [AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT]: pendingMerchantReceiptSrc,
        [AfterSaleStatusEnum.RETURN_CLOSED]: returnClosedSrc,
        [AfterSaleStatusEnum.CUSTOMER_WITHDRAWN]: cancelSrc,
    };
    return srcMap[stateRef.value];
});

/** 售后状态 */
const afterSaleStatusMap = {
  [AfterSaleStatusEnum.NON_AFTER_SALE]: '非售后状态',
  [AfterSaleStatusEnum.PENDING_MERCHANT]: '待商家受理',
  [AfterSaleStatusEnum.CUSTOMER_WITHDRAWN]: '售后申请已取消',
  [AfterSaleStatusEnum.MERCHANT_REFUSED]: '商家拒绝退款',
  [AfterSaleStatusEnum.PENDING_RETURN]: '售后申请已通过',
  [AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT]: '待商家收货',
  [AfterSaleStatusEnum.RETURN_CLOSED]: '退货退款关闭',
  [AfterSaleStatusEnum.REFUNDING]: '退款中',
  [AfterSaleStatusEnum.PENDING_PAYMENT]: '退款中',
  [AfterSaleStatusEnum.REFUND_COMPLETED]: '退款完成',
  [AfterSaleStatusEnum.CANCEL_APPROVED]: '商家同意取消订单',
  [AfterSaleStatusEnum.CANCEL_REJECTED]: '商家拒绝取消订单'
};
</script>

<style lang="less" scoped>
.store_after_sales_status_wrapper {
    width: 100%;
    height: 38px;
    display: flex;
    align-items: center;
    gap: 4px;
    img {
        width: 38px;
        height: 38px
    }
    .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>