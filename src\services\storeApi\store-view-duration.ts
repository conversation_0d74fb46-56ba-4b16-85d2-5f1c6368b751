import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
const enum ViewDurationApi {
   viewDurationPage = "/applet/storeEntity/queryWatchSeconds",
   queryWatch = "/applet/storeEntity/queryWatchRecordsByCustomerId"
 
}

// 分页查询观看时长
export function getViewDurationPage(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(ViewDurationApi.viewDurationPage),
    params: params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}
export function getViewDurationDetial(params) {
  return defHttp.post({
    url: getStoreApiUrl(ViewDurationApi.queryWatch),
    params: params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}