import { ref, reactive } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { storeFlowListApi } from "@/services/storeApi";

export default function useStoreWallet() {
  const isPageLoadingRef = ref(false);
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 收支流水数据 */
  const storeWalletFlowList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getStoreWalletFlowList();
    }
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    return {
      data: {},
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取收支流水列表数据 */
  async function getStoreWalletFlowList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await storeFlowListApi(_params);

      // 更新订单列表
      if (current === 1) {
        storeWalletFlowList.value = records;
      } else if (records.length) {
        storeWalletFlowList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 数据初始化 */
  async function initStoreWalletFlowList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreWalletFlowList();
    isPageLoadingRef.value = false;
  }

  /** 初始化参数 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    initStoreWalletFlowList();
  }

  return {
    isPageLoadingRef,
    storeWalletFlowList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onLoad,
    onRefresh,
    initStoreWalletFlowList
  };
}
