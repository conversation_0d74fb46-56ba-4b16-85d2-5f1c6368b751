import { defHttp } from "@/services";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
const systemStore = useSystemStoreWithoutSetup();

const enum ProductApiEnum {
  dosageForm = "/product/manage/get/dosageForm",
}

export async function queryDosageForm(params = {}){
	if (!systemStore.storeUrl) {
		return Promise.reject("storeUrl为空");
	}
	return defHttp.post({
		url:`${systemStore.storeUrl}${ProductApiEnum.dosageForm}`,
		params,
	})
}

