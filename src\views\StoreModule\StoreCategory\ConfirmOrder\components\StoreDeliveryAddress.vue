<template>
  <div class="delivery_address_wrapper">
    <!-- 添加收货地址 -->
    <div v-if="!isDefaultAddress" class="address_add">
      <SvgIcon name="IonAdd" style="font-size: 14px;color: #4DA4FF;" />
      <span class="add_title">添加收货地址</span>
    </div>
    <!-- 地址 -->
    <div v-else class="address_wrapper">
      <div class="address_info_container">
        <!-- 图片 -->
        <img :src="addressSrc" alt="" />
        <div class="address_info">
          <div class="address_info_top">
            <span class="name">{{ addressInfoRef.name ?? '-' }}</span>
            <span class="phone">{{ addressInfoRef.mobile ?? '-' }}</span>
          </div>
          <p class="address">{{ addressStr }}</p>
        </div>
      </div>
      <SvgIcon name="ArrowForward" style="font-size: 22px;" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
/** 静态资源 */
import addressSrc from "@/assets/storeImage/storeMine/address.png";

defineOptions({ name: 'DeliveryAddress' });

/** props */
const props = defineProps<{
    addressInfo: {
        id?: string;
        name?: string;
        mobile?: string;
        companyId?: string;
        company?: string;
        provinceId?: string;
        province?: string;
        cityId?: string;
        cityName?: string;
        areaId?: string;
        area?: string;
        townId?: string;
        town?: string;
        address?: string;
        isDefault?: 0 | 1;
        csWxNickname?: string;
    };
}>();

const { addressInfo: addressInfoRef } = toRefs(props);

const addressStr = computed(() => {
    return `${addressInfoRef.value.province + addressInfoRef.value.cityName + addressInfoRef.value.area + addressInfoRef.value.town + addressInfoRef.value.address}`;
});

/** 是否存在默认地址 */
const isDefaultAddress = computed(() => {
    if (!addressInfoRef.value) return false;
    const { name, mobile, province } = addressInfoRef.value;
    return name && mobile && province;
});
</script>

<style lang="less" scoped>
.delivery_address_wrapper {
    height: 78px;
    padding: 12px;
    box-sizing: border-box;
    margin-bottom: 8px;
    background-color: #fff;
    border-radius: 8px;
    .address_add {
        width: 100%;
        height: 56px;
		border-radius: 10px;
		border: 1px dashed #4DA4FF;
		display: flex;
		justify-content: center;
		align-items: center;
        gap: 4px;
        .add_title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #4DA4FF;
            line-height: 20px;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }
    }
    .address_wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .address_info_container {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            img {
                width: 32px;
                height: 32px;
            }
            .address_info {
                display: flex;
                flex-direction: column;
                gap: 4px;
                .address_info_top {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    .name,
                    .phone {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        font-size: 14px;
                        color: #333333;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                    }
                }
                .address {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 18px;
                    color: #666666;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }
            }
        }
    }
}
</style>
