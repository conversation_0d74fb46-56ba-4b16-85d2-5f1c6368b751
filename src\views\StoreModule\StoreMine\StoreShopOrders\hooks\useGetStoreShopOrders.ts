import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import { SearchTypeEnum } from "../type";
import { useMessages } from "@/hooks/useMessage";
import { OrderStatusEnum } from "@/views/StoreModule/enums";
import { getStoreOrderList, getStoreAfterSaleOrderList } from "@/services/storeApi";

export default function useGetStoreShopOrders() {
  const scope = effectScope();
  const isPageLoadingRef = ref(false);

  const { createMessageSuccess, createMessageError } = useMessages();
  /** 店铺订单数据 */
  const storeShopOrderList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 搜索值 */
  const searchValueRef = ref("");
  /** 搜索类型 */
  const searchTypeRef = ref(SearchTypeEnum.CustomerName);
  /** 搜索类型 */
  const searchTypeOptions = [
    { text: "客户昵称", value: SearchTypeEnum.CustomerName },
    { text: "商品名称", value: SearchTypeEnum.ProductName },
    { text: "订单编号", value: SearchTypeEnum.OrderNo },
  ];
  /** tab */
  const activeTabRef = ref(OrderStatusEnum.ALL);
  /** 店铺订单状态 */
  const orderStatusList = [
    {
      label: "全部",
      value: OrderStatusEnum.ALL,
    },
    {
      label: "待付款",
      value: OrderStatusEnum.WAIT_PAY,
    },
    {
      label: "待发货/提货",
      value: OrderStatusEnum.WAIT_DELIVER,
    },
    {
      label: "待收货",
      value: OrderStatusEnum.WAIT_RECEIVE,
    },
    {
      label: "退款/售后",
      value: OrderStatusEnum.REFUND,
    },
  ];

  /** 分页 */
  const pageVO = reactive({
    size: 30,
    current: 1,
    total: 0,
  });

  /** 获取搜索参数 */
  function getSearchParams() {

    const status = activeTabRef.value === OrderStatusEnum.ALL ? null : activeTabRef.value;
    const searchValue = searchValueRef.value.trim();
    const searchType = searchTypeRef.value;

    // 基础参数结构
    const params = {
      data: {
        status,
        customerNickname: null,
        productName: null,
        orderCode: null,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };

    // 只有当有搜索值时才添加搜索条件
    if (searchValue) {
      switch (searchType) {
        case SearchTypeEnum.CustomerName:
          params.data.customerNickname = searchValue;
          break;
        case SearchTypeEnum.ProductName:
          params.data.productName = searchValue;
          break;
        case SearchTypeEnum.OrderNo:
          params.data.orderCode = searchValue;
          break;
      }
    }

    return params;
  }

  /** 获取店铺订单（不包含售后订单） */
  async function getStoreShopOrders() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getStoreOrderList(_params);

      // 更新订单列表
      if (current === 1) {
        storeShopOrderList.value = records;
      } else if (records.length) {
        storeShopOrderList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 店铺订单售后订单列表 */
  async function getStoreShopAfterSaleOrders() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getStoreAfterSaleOrderList(_params);

      // 更新订单列表
      if (current === 1) {
        storeShopOrderList.value = records;
      } else if (records.length) {
        storeShopOrderList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < Number(total);
      
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      if (activeTabRef.value === OrderStatusEnum.REFUND) {
        getStoreShopAfterSaleOrders();
      } else {
        getStoreShopOrders();
      }
    }
  }

  /** 初始化 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    if (
      [
        OrderStatusEnum.ALL,
        OrderStatusEnum.WAIT_PAY,
        OrderStatusEnum.WAIT_DELIVER,
        OrderStatusEnum.WAIT_RECEIVE,
      ].includes(activeTabRef.value)
    ) {
      initStoreShopOrdersList();
    } else if (activeTabRef.value == OrderStatusEnum.REFUND) {
      /** 退款/售后另外接口 */
      initStoreAfterSaleOrderList();
    }
  }

  /** 数据初始化 */
  async function initStoreShopOrdersList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreShopOrders();
    isPageLoadingRef.value = false;
  }

  /** 店铺售后订单数据初始化 */
  async function initStoreAfterSaleOrderList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreShopAfterSaleOrders();
    isPageLoadingRef.value = false;
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    /** 监听 */
    watch(
      () => activeTabRef.value,
      newVal => {
        if (
          [
            OrderStatusEnum.ALL,
            OrderStatusEnum.WAIT_PAY,
            OrderStatusEnum.WAIT_DELIVER,
            OrderStatusEnum.WAIT_RECEIVE,
          ].includes(newVal)
        ) {
          initStoreShopOrdersList();
        } else if (newVal == OrderStatusEnum.REFUND) {
          /** 退款/售后另外接口 */
          initStoreAfterSaleOrderList();
        }
      },
      {
        immediate: true,
      },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    isPageLoadingRef,
    storeShopOrderList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    searchValueRef,
    searchTypeRef,
    searchTypeOptions,
    activeTabRef,
    orderStatusList,
    onLoad,
    onRefresh,
    getStoreShopOrders,
    initStoreShopOrdersList,
  };
}
