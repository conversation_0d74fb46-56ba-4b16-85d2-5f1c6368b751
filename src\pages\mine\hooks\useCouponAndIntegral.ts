import { ref } from 'vue'
import { getMyCouponAndIntegral } from '@/services/api'

export default function useCouponAndIntegral() {
  const storeMineCouponAndPointsRef = ref({
    availPoints: 0,
    unUseNum: 0,
  })

  /** 我的-福利券张数&积分余额 */
  async function queryMyCouponAndIntegral() {
    try {
      const resp = await getMyCouponAndIntegral()
      if (resp) {
        Object.assign(storeMineCouponAndPointsRef.value, resp)
      }
    } catch (error) {
      console.log('获取我的福利券张数和积分余额失败: ', error)
    }
  }

  return {
    storeMineCouponAndPointsRef,
    queryMyCouponAndIntegral,
  }
}
