import { ref,reactive } from 'vue';
import { getqueryWriteOffStatistics } from "@/services/storeApi";
import {useMessages} from "@/hooks/useMessage"
import { useUserStore } from '@/stores/modules/user';
const {createMessageError,createMessageSuccess} = useMessages()
const isPageLoadingRef = ref(false)
const verifiPageVO = {
    current:1,
    size:30,
    total:1
}
const listData = ref([])
export interface GetDataByTypeResponse{
    productId:string,
    productFrontName:string,
    specName:string,
    productNum:number,
    orderNum:number,
    totalMoney:number,
}
export function VerificationStatisticsList(params){
    const userStore = useUserStore()
    const loadData = async(flag) =>{
        if(flag){
            verifiPageVO.current = 1
            listData.value = []
        }
        isPageLoadingRef.value = true
        groupMgrListStatusReactive.isNextPageLoading = true
        let param = {
            data:{
                storeId:userStore.storeUserInfo.storeId,
                ...params
            },
            pageVO: {
                current:verifiPageVO.current,
                size: verifiPageVO.size
            },
        }
        try {
            const {current,size,total,records} = await getqueryWriteOffStatistics(param)
            verifiPageVO.current = Number(current)
            verifiPageVO.size = Number(size)
            verifiPageVO.total = Number(total)
            listData.value.push(...records)
            if(Number(verifiPageVO.current) * Number(verifiPageVO.size) >=  Number(verifiPageVO.total)){
                groupMgrListStatusReactive.isNextPageFinished = true
            }
        } catch (error) {
            createMessageError("获取核销统计数据异常")
        }finally{
            // groupMgrListStatusReactive.isNextPageFinished = true
            groupMgrListStatusReactive.isNextPageLoading = false
            groupMgrListStatusReactive.isPullLoading = false
            isPageLoadingRef.value = false
        }
    }
    const groupMgrListStatusReactive = reactive({
            isPullLoading:false,
            isNextPageLoading:false,
            isNextPageFinished:false
    }) 
    function onGroupMgrListRefresh(){
        groupMgrListStatusReactive.isPullLoading = true
        loadData(true)
    }
    function onGroupMgrListNextPageLoad(){
        if(Number(verifiPageVO.current) * Number(verifiPageVO.size) <  Number(verifiPageVO.total)){
            verifiPageVO.current++
            groupMgrListStatusReactive.isNextPageLoading = true
            loadData(false)
        }
    }
    return {
        loadData,
        listData,
        verifiPageVO,
        isPageLoadingRef,
        groupMgrListStatusReactive,
        onGroupMgrListNextPageLoad,
        onGroupMgrListRefresh
    }
}