<template>
    <div class="warrper">
        <div class="title">
            <span class="name"> 商品名 </span>
            <span class="totalStyle"> 商品总数</span>
            <span class="numStyle">订单数</span>
            <span class="numStyle">订单总额 </span>
        </div>
        <div class="boday">
            <van-list
                :offset="50"
                v-model:loading="groupMgrListStatusReactive.isNextPageLoading"
                @load="onGroupMgrListNextPageLoad"
                :finished="groupMgrListStatusReactive.isNextPageFinished"
            >
                <div class="item" v-for="data in listDatas">
                    <span class="name">{{`${data.productName}(${data.specName})`}}</span>
                    <span class="totalStyle"> {{ data.productNum }}</span>
                    <span class="numStyle">{{ data.orderNum }}</span>
                    <span class="numStyle">{{data?.totalAmount ? (data.totalAmount / 100).toFixed(2) : '0.00'}}</span>
                </div>
            </van-list>
        </div>
        
    </div>
</template>
<script setup lang="ts">
import { ref,reactive,watch } from "vue";

import  {VerificationStatisticsList} from '@/views/StoreManagement/VerificationStatistics/hooks/index'
type ListCardProps = {
    listDatas:any,
    searchParam:any

}
const props = withDefaults(defineProps<ListCardProps>(),{
    listDatas: [],
    searchParam:{}
})
const {groupMgrListStatusReactive,onGroupMgrListNextPageLoad} = VerificationStatisticsList(props.searchParam)
</script>
<style scoped lang="less">
.warrper{
    // width: 100%;
    padding: 10px;
    height: calc(100% - 20px);
    .title{
        display: flex;
        align-items: center;
        border-bottom: 1px solid #EEEEEE;
        padding: 10px;
        font-size: 14px;
    }
    .boday{
        height: calc(100% - 35px);
        overflow-y: scroll;
        
        .item{
            display: flex;
            align-items: center;
            padding: 10px;
            font-size: 13px;
        }
        .item:nth-child(2n -1) {
            background-color: #FFF4F4;
        }
    }

    .name{
        width: 34%;
        display: -webkit-box;
        -webkit-line-clamp: 3;  /* 控制显示行数 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .totalStyle{
        width: 28%;
    }
    .numStyle{
        width: 20%;
    }
}
</style>