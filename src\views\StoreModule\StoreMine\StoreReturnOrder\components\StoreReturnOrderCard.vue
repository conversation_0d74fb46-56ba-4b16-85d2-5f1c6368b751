<template>
  <div class="store_return_order_card_wrapper">
    <!-- title -->
    <div class="title_wrapper">
      <span class="title van-ellipsis">{{ orderInfoRef?.productName ?? '-'}}</span>
      <span class="store_return_order_count">
        退货件数
        <span class="order_count">{{`(${orderInfoRef?.count ?? 0})`}}</span>
      </span>
    </div>
    <!-- 内容 -->
    <div class="content_wrapper">
      <!-- 退款金额 -->
      <div class="store_payment_info_item">
        <span class="store_payment_info_item_label">退款金额</span>
        <span class="store_payment_info_item_value">
          {{`￥${Number((orderInfoRef?.actualRefundAmount ?? 0) / 100).toFixed(2)}`}}
        </span>
      </div>
      <!-- 订单号 -->
      <div class="store_payment_info_item">
        <span class="store_payment_info_item_label">订单号</span>
        <span class="store_payment_info_item_value">
            {{ orderInfoRef?.orderCode ?? '-' }}
        </span>
      </div>
      <!-- 退款时间 -->
      <div class="store_payment_info_item">
        <span class="store_payment_info_item_label">退款时间</span>
        <span class="store_payment_info_item_value">
            {{ orderInfoRef?.refundTime ?? '-' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs } from "vue";

defineOptions({ name: "StoreReturnOrderCard" });

/** props */
const props = defineProps<{
  orderInfo: {
    id: string;
    productName: string;
    count: number;
    actualRefundAmount: number;
    orderCode: string;
    refundTime: string;
  };
}>();

const { orderInfo: orderInfoRef } = toRefs(props);
</script>

<style lang="less" scoped>
.store_return_order_card_wrapper {
    background-color: #fff;
    border-radius: 12px;
    padding: 12px;
    box-sizing: border-box;
    margin-bottom: 8px;
    .title_wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 15px;
        .title {
            max-width: calc(100% - 100px);
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_return_order_count {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            .order_count {
                color: #EF1115;
            }
        }
    }
    .content_wrapper {
        background: #F8F8F8;
        border-radius: 8px;
        padding: 8px 12px;
        box-sizing: border-box;
        .store_payment_info_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            .store_payment_info_item_label {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .store_payment_info_item_value {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                text-align: right;
                font-style: normal;
                text-transform: none;
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>
