<template>
  <view
    class="w-full h-full text-#fff text-22rpx flex justify-center items-center rounded-16rpx bg-[rgba(0,0,0,0.4)] absolute top-0 left-0 z-100"
    :style="_customStyle"
  >
    <OutOfStockOverlay v-if="props.type === 'noStock'" />
    <PresOverlay v-if="props.type === 'pres'" />
    <!-- 购物车处方药 -->
    <view v-if="props.type === 'cartPres'" class="flex flex-col items-center">
      <view>{{ typeMap[props.type] }}</view>
      <view>请在药师指导</view>
      <view>下购买</view>
    </view>
    <!-- 其他 -->
    <view v-if="props.type !== 'noStock' && props.type !== 'pres' && props.type !== 'cartPres'">
      {{ typeMap[props.type] }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { type StyleValue } from 'vue'
/** 相关组件 */
import OutOfStockOverlay from './OutOfStockOverlay.vue'
import PresOverlay from './PresOverlay.vue'

type MaskType = 'noStock' | 'pres' | 'shelves' | 'specDeleted' | 'stockPlain' | 'cartPres'

defineOptions({ name: 'Overlay' })

/** props */
const props = withDefaults(
  defineProps<{
    type: MaskType
    customStyle: StyleValue
  }>(),
  {
    type: 'noStock',
    customStyle: () => ({}),
  },
)

/** 提示内容 */
const typeMap: Record<MaskType, string> = {
  pres: '处方药',
  noStock: '无库存',
  stockPlain: '无库存',
  shelves: '已下架',
  specDeleted: '规格已删除',
  cartPres: '处方药',
}

/** 自定义样式 */
const _customStyle = computed(() => {
  let styles: StyleValue = {}
  return [styles, props.customStyle]
})
</script>

<style scoped lang="scss"></style>
