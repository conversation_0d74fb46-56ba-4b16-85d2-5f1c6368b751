<template>
    <div class="goods-search">
        <view class="search-box">
            <van-search shape="round" show-action @search="handleSearch" maxlength="60" :autofocus="true" v-model="modal.keyword"
                placeholder="请输入商品名称搜索">
                <template #action>
                    <div @click="handleSearch">搜索</div>
                </template>
            </van-search>
        </view>
        <van-pull-refresh class="content-warpper" v-model="isPullLoadingRef" @refresh="onRefresh">
            <van-list :loading="listLoadingRef" class="content-list" :finished="listFinishedRef" :offset="50"
                @load="loadPageData" finished-text="">
                <div v-if="dataList.length">
                    <GoodsCard v-for="item in dataList" @click="handleClick(item.id || item.productId)" :type="type"
                        :border-all="false" :key="item.id" :card-info="item">
                        <template #btn>
                            <SoldQty :card-info="item" />
                        </template>
                    </GoodsCard>
                </div>
                <van-empty v-else description="暂无商品" :image="goodsEmpty" />
            </van-list>
        </van-pull-refresh>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useMessages } from "@/hooks/useMessage"
import GoodsCard from "../components/GoodsCard.vue";
import SoldQty from "../components/SoldQty.vue";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { useRouter, useRoute } from "vue-router"
import goodsEmpty from "@/assets/storeImage/product/goodsEmpty.png";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { queryWelfareSearch, queryGoodslist, queryIntergralList } from "@/services/storeApi/store-category";
import { RoutesName } from "@/enums/routes";
const { storeUserInfo } = useUserStoreWithoutSetup()
const route = useRoute()
const router = useRouter()
const message = useMessages()
const props = withDefaults(defineProps<{
    type: StoreGoodsEnum
}>(), {
    type: StoreGoodsEnum.Goods
})
const initParams = {
    keyword: '',
}
const modal = ref({ ...initParams })
//分页
const pageVO = ref({
    size: 30,
    current: 1,
    total: 0,
})
const dataList = ref([])
const listLoadingRef = ref<boolean>(false)
const isPullLoadingRef = ref<boolean>(false)
const listFinishedRef = ref<boolean>(false)
async function getList() {
    try {
        const params: any = {
            data: {},
            pageVO: {
                current: pageVO.value.current,
                size: pageVO.value.size,
            },
        }
        let api = queryGoodslist
        if (props.type == StoreGoodsEnum.Goods) {
            params.data = {
                keyWord: modal.value.keyword,
                storeId: storeUserInfo.storeId,
                visibleScope: storeUserInfo.type
            }
        }
        if (props.type == StoreGoodsEnum.IntegralGoods) {
            api = queryIntergralList
            params.data = {
                frontName: modal.value.keyword,
            }
        }
        if (props.type == StoreGoodsEnum.WelfareTicket) {
            api = queryWelfareSearch
            params.data = {
                keyWord: modal.value.keyword,
                storeId: storeUserInfo.storeId,
                visibleScope: storeUserInfo.type
            }
        }

        const res: any = await api(params)
        if (pageVO.value.current == 1) {
            dataList.value = res.records;
        } else {
            dataList.value.push(...res.records);
        }
        pageVO.value.total = Number(res.total) || 0
        //加载完成
        if (pageVO.value.current * pageVO.value.size >= pageVO.value.total) {
            listFinishedRef.value = true
        }
    } catch (e) {
        message.createMessageError(`获取失败：${e}`)
    } finally {
        isPullLoadingRef.value = false
        listLoadingRef.value = false;
    }
}
//加载分页数据
const loadPageData = async () => {
    // 数据全部加载完成
    if (pageVO.value.current * pageVO.value.size < pageVO.value.total) {
        listLoadingRef.value = true
        pageVO.value.current++;
        getList()
    }
}
function onRefresh() {
    isPullLoadingRef.value = true
    reloadData()
}
const reloadData = () => {
    dataList.value = []
    listFinishedRef.value = false
    pageVO.value.current = 1
    pageVO.value.total = 1
    getList()
}
const handleSearch = () => {
    console.log(modal.value.keyword, 'reloadData');
    reloadData()
}
const handleClick = (id: string) => {
    router.push({
        name: RoutesName.StoreDetail,
        query: {
            ...route.query,
            type: props.type,
            id
        }
    })
}
onMounted(() => {
    getList()
})
</script>

<style scoped lang="less">
:deep(.van-cell) {
    background: none !important;
    padding: 0 !important;
}

:deep(.van-field__clear, .van-field__right-icon) {
    margin-right: 0 !important;
}

.goods-search {
    height: calc(100vh - env(safe-area-inset-bottom));
    display: flex;
    flex-direction: column;

    .search-box {}

    .content-warpper {
        flex: 1;
        overflow-y: auto;
        padding: 12px;

        .content-list {
            padding: 12px;
            background-color: #fff;
            border-radius: 8px;
        }
    }
}
</style>