<template>
  <view class="store_commodity_info">
    <view class="store_commodity_info_title">商品信息</view>
    <!-- 订单信息 -->
    <view class="pending-orders-info">
      <image
        :src="firstOrderItem?.productImgPath ? firstOrderItem?.productImgPath : CouponSrc"
        alt=""
        class="pending-orders-info-img"
      />
      <view class="pending-orders-info-text">
        <view class="pending-orders-info-title">
          <p class="van-multi-ellipsis--l2 product-front-name">
            {{ firstOrderItem?.productFrontName }}
          </p>
          <view class="pending-orders-info-right">
            <!-- 普通订单 -->
            <span v-if="StoreOrderTypeEnum.NORMAL == orderInfoRef?.type" class="price">
              {{ `¥ ${Number((firstOrderItem?.price ?? 0) / 100).toFixed(2)}` }}
            </span>
            <!-- 积分订单 -->
            <span v-if="StoreOrderTypeEnum.INTEGRAL == orderInfoRef?.type" class="price">
              {{ firstOrderItem.exchangePoints || 0
              }}{{
                firstOrderItem.exchangePrice
                  ? '积分+￥' + firstOrderItem.exchangePrice / 100
                  : '积分'
              }}
            </span>
            <!-- 福利券订单 -->
            <span v-if="StoreOrderTypeEnum.COUPON == orderInfoRef?.type" class="price">
              {{ `${firstOrderItem?.exchangeCount}张${firstOrderItem?.couponName}` }}
            </span>
            <!-- 数量 -->
            <span class="count">{{ `x ${firstOrderItem?.count ?? 0}` }}</span>
          </view>
        </view>
        <!-- 规格 -->
        <view class="specification">{{ firstOrderItem?.specName }}</view>
        <!-- 订单金额 -->
        <view class="order-amount">
          {{ `实付款￥${Number((orderInfoRef?.money ?? 0) / 100).toFixed(2)}` }}
        </view>
      </view>
    </view>
    <!-- 核销码状态 -->
    <view
      v-if="orderInfoRef?.orderVerificationDTO?.status == StoreOrderDetailVerifyStatusEnum.VERIFIED"
      class="verification_code_status_wrapper"
      @click="handleClickVerificationCodeStatus"
    >
      <!-- 核销码状态 -->
      <view class="verification_code_status">已使用</view>
      <view class="verification_code_status_right">
        <span class="verification_code_status_right_text">查看详情</span>
        <img :src="pullDownSrc" alt="" class="verification_code_status_right_text_img" />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from 'vue'
import {
  StoreOrderTypeEnum,
  StoreOrderStatusEnum,
  StoreOrderDetailVerifyStatusEnum,
  ProductPickupModeEnum,
} from '@/enum'
/** 静态资源 */
import CouponSrc from '@/static/images/storeHome/coupon.png'
import pullDownSrc from '@/static/images/storeHome/pull-down.png'

defineOptions({ name: 'StoreCommodityInfo' })

interface OrderVerificationDTO {
  id: string
  code: string // 核销码
  status: StoreOrderDetailVerifyStatusEnum // 核销状态
  verificationTime?: string // 核销时间
  storeId?: string // 店铺id
}

/** props */
const props = defineProps<{
  orderInfo: {
    type?: StoreOrderTypeEnum
    status?: StoreOrderStatusEnum
    code?: string
    pickupType?: ProductPickupModeEnum
    money?: number
    goodsAmount?: number
    payStatus?: number
    afterSaleState?: number
    orderItemDTOList?: Array<{
      type?: 1 | 2 | 3
      orderId?: string
      productImgPath?: string
      productFrontName?: string
      specName?: string
      price?: number
      count?: number
      exchangePoints?: number
      exchangePrice?: number
      couponCateId?: string
      couponName?: string
      exchangeCount?: number
    }>
    // 订单核销记录
    orderVerificationDTO?: OrderVerificationDTO
  }
}>()

const { orderInfo: orderInfoRef } = toRefs(props)
/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
  return orderInfoRef.value?.orderItemDTOList?.[0]
})

/** emit */
const emit = defineEmits<{
  /** 点击查看详情 */
  (event: 'clickVerificationCodeStatus'): void
}>()

/** 点击查看详情 */
function handleClickVerificationCodeStatus() {
  emit('clickVerificationCodeStatus')
}
</script>

<style lang="scss" scoped>
.store_commodity_info {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 16rpx;
  .store_commodity_info_title {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .pending-orders-info {
    display: flex;
    gap: 16rpx;
    .pending-orders-info-img {
      width: 128rpx;
      height: 128rpx;
      border-radius: 16rpx;
    }
    .pending-orders-info-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16rpx;
      .pending-orders-info-title {
        display: flex;
        gap: 24rpx;
        .product-front-name {
          flex: 1;
          font-family:
            Source Han Sans CN,
            Source Han Sans CN;
          font-weight: 500;
          font-size: 32rpx;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 44rpx;
        }
        .pending-orders-info-right {
          display: flex;
          flex-direction: column;
          gap: 16rpx;
          .price {
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 600;
            font-size: 28rpx;
            color: #333333;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
          .count {
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 400;
            font-size: 26rpx;
            color: #333333;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
        }
      }
      .specification {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .order-amount {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 48rpx;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .verification_code_status_wrapper {
    height: 56rpx;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    border-top: none;
    background-image: linear-gradient(to right, #dedcda 50%, transparent 50%);
    background-size: 20rpx 2rpx;
    background-repeat: repeat-x;
    background-position: top;
    .verification_code_status {
      height: 40rpx;
      background: #4be092;
      border-radius: 8rpx;
      padding: 4rpx 20rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 32rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .verification_code_status_right {
      display: flex;
      align-items: center;
      gap: 4rpx;
      .verification_code_status_right_text {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 40rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .verification_code_status_right_text_img {
        width: 28rpx;
        height: 28rpx;
        transform: rotate(-90deg);
      }
    }
  }
}
</style>
