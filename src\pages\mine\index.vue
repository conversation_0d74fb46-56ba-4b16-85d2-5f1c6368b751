<template>
  <TabbarLayouts>
    <scroll-view
      scroll-y
      enhanced
      :show-scrollbar="false"
      class="store_mine_wrapper"
      :style="{ backgroundImage: `url(${storeMineBgSrc})` }"
    >
      <!-- 用户信息 -->
      <view class="store_mine_user_info">
        <image
          fit="contain"
          :src="(storeMemberInfo && storeMemberInfo.img) || defaultAvatar"
          class="store_mine_user_avatar"
        />
        <view class="store_mine_user_info_right">
          <view class="store_mine_user_info_right_name">
            <text class="name">{{ (storeMemberInfo && storeMemberInfo.nickname) || '-' }}</text>
            <!-- 店长标识 -->
            <view v-if="isStoreOwner" class="store_mine_user_info_owner">店长</view>
            <!-- 店员标识 -->
            <view v-if="isStoreStaff" class="store_mine_user_info_staff">店员</view>
          </view>
          <view class="store_mine_user_info_right_serial">
            {{ `编号：${storeMemberInfo?.shortId ?? '-'}` }}
            <image
              :src="copySrc"
              alt=""
              @click.stop="copyToClipboard(storeMemberInfo?.shortId)"
              class="store_mine_user_info_right_icon"
            />
          </view>
        </view>
      </view>
      <!-- 用户福利券信息 -->
      <view class="store_mine_user_coupon_info">
        <view class="store_mine_user_coupon_info_item" @click="">
          <view class="store_mine_user_coupon_info_item_name">福利券(张)</view>
          <view class="store_mine_user_coupon_info_item_num">
            {{ storeMineCouponAndPointsRef.unUseNum }}
          </view>
        </view>
        <view class="store_mine_user_coupon_info_item" @click="">
          <view class="store_mine_user_coupon_info_item_name">积分余额</view>
          <view class="store_mine_user_coupon_info_item_num">
            {{ storeMineCouponAndPointsRef.availPoints }}
          </view>
        </view>
      </view>
      <!-- 门店地址 -->
      <StoreAddress :storeInfo="storeInfo" />
      <!-- 我的订单 -->
      <BannerContainer>
        <template #title>
          <text class="title">我的订单</text>
        </template>
        <template #headerRight>
          <view class="header-right" @click="">
            <text class="header-right-text">全部</text>
            <image :src="headerRightSrc" alt="" class="header-right-icon" />
          </view>
        </template>
        <view class="store_mine_order" style="justify-content: space-between">
          <view
            v-for="item in mineOrderOptions"
            :key="item.key"
            class="store_mine_order_item"
            @click="item.onClick"
          >
            <van-badge :content="item.value" max="99" :show-zero="false" color="#EF1115">
              <image :src="item.icon" alt="" class="store_mine_item_icon" />
            </van-badge>
            <text class="store_mine_item_text">{{ item.label }}</text>
          </view>
        </view>
      </BannerContainer>
      <!-- 店铺管理 -->
      <BannerContainer>
        <template #title>
          <view style="display: flex; align-items: center; gap: 12rpx">
            <text class="title">店铺管理</text>
            <StoreSelectOrderType v-model:value="storeOrderTypeRef" />
          </view>
        </template>
        <template #headerRight>
          <StoreSelectTime v-model:value="storeSelectTimeRef" />
        </template>
        <!-- 内容 -->
        <view class="store_management_container">
          <!-- 数据概览 -->
          <view class="store_management_container_data">
            <template v-for="item in orderOverview" :key="item.key">
              <view class="store_management_container_data_item">
                <text class="data_title">{{ item.label }}</text>
                <text class="data_num" :class="getSizeClass(item.value)">
                  {{ item.value }}
                </text>
              </view>
              <!-- 竖线 -->
              <view class="store_management_container_data_line"></view>
            </template>
          </view>
          <!-- 选项 -->
          <view class="store_mine_management">
            <template v-for="item in storeManagementOptions" :key="item.key">
              <view v-if="item.isShow" class="store_mine_management_item" @click="item.onClick">
                <VanBadge :content="item.value" max="99" :show-zero="false" color="#EF1115">
                  <image :src="item.icon" alt="" class="store_mine_item_icon" />
                </VanBadge>
                <text class="store_mine_item_text">{{ item.label }}</text>
              </view>
            </template>
          </view>
        </view>
      </BannerContainer>
      <!-- 区域管理 -->
      <BannerContainer>
        <template #title>
          <text class="title">区域管理</text>
        </template>
        <view class="store_mine_district">
          <view
            v-for="item in mareaManagementOptions"
            :key="item.key"
            class="store_mine_district_item"
            @click="item.onClick"
          >
            <image :src="item.icon" alt="" class="store_mine_item_icon" />
            <text class="store_mine_item_text">{{ item.label }}</text>
          </view>
        </view>
      </BannerContainer>
      <!-- 经销商管理 -->
      <BannerContainer>
        <template #title>
          <text class="title">经销商管理</text>
        </template>
        <view class="store_mine_district">
          <view
            v-for="item in dealerManagementOptions"
            :key="item.key"
            class="store_mine_district_item"
            style="width: 25%"
            @click="item.onClick"
          >
            <image :src="item.icon" alt="" class="store_mine_item_icon" />
            <text class="store_mine_item_text">{{ item.label }}</text>
          </view>
        </view>
      </BannerContainer>
      <!-- 更多 -->
      <BannerContainer>
        <template #title>
          <text class="title">更多</text>
        </template>
        <view class="store_mine_more">
          <view
            v-for="item in moreOptions"
            :key="item.key"
            class="store_mine_more_item"
            @click="item.onClick"
          >
            <image :src="item.icon" alt="" class="store_mine_item_icon" />
            <text class="store_mine_item_text">{{ item.label }}</text>
          </view>
        </view>
      </BannerContainer>
      <view style="height: 24rpx"></view>
    </scroll-view>
  </TabbarLayouts>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useTabbar } from '@/components/Tabbar/hooks/useTabbar'
import { routesMap } from '@/routes/maps'
import { RoutesName } from '@/routes/enums/routeNameEnum'
import { useRectInfo } from '@/hooks/common'
import { systemStore } from '@/stores/modules/system'
import { storeToRefs } from 'pinia'
import { copyToClipboard } from '@/utils/clipboardUtils'
import { OrderStatusEnum } from '@/enum'
import { useGetStoreInfo } from '@/hooks/business'
import { useGetStoreUserInfo, useStoreManagement, useCouponAndIntegral } from './hooks'
import { useRouter } from '@/hooks/common'
/** 相关资源 */
import storeMineBgSrc from '@/static/images/storeUser/storeMineBg.png'
import copySrc from '@/static/images/storeUser/copy.png'
import defaultAvatar from '@/static/images/storeUser/defaultAvatar.jpg'
import headerRightSrc from '@/static/images/storeUser/headerRight.png'
import WAIT_PAY from '@/static/images/storeUser/WAIT_PAY.png'
import WAIT_DELIVER from '@/static/images/storeUser/WAIT_DELIVER.png'
import WAIT_RECEIVE from '@/static/images/storeUser/WAIT_RECEIVE.png'
import REFUND from '@/static/images/storeUser/REFUND.png'
import couponStoreSrc from '@/static/images/storeUser/couponStore.png'
import integralStoreSrc from '@/static/images/storeUser/pointStore.png'
import settingSrc from '@/static/images/storeUser/setting.png'
import addressIconSrc from '@/static/images/storeUser/address-icon.png'
import walletIconSrc from '@/static/images/storeUser/icon-wallet.png'
/** 相关组件 */
import TabbarLayouts from '@/layouts/tabbar.vue'
import StoreAddress from '@/pages/business/components/StoreAddress.vue'
import BannerContainer from '@/pages/business/components/BannerContainer.vue'
import StoreSelectOrderType from '@/pages/business/components/StoreSelectOrderType.vue'
import StoreSelectTime from '@/pages/business/components/StoreSelectTime.vue'

defineOptions({
  name: 'Home',
})

const { routerPushByKey } = useRouter()
const { getStoreMemberInfo, storeMemberInfo } = useGetStoreUserInfo()
const {
  storeManagementOptions,
  mareaManagementOptions,
  dealerManagementOptions,
  orderOverview,
  // isShowStoreMemberCodeRef,
  // qrCodeUrlRef,
  isStoreOwner,
  isStoreStaff,
  isStoreMember,
  isAreaManager,
  isDistributor,
  isStore,
  storeSelectTimeRef,
  storeOrderTypeRef,
  // isPageLoadingRef,
  // getOrderOverview,
  // getStoreAfterSaleOrderCount,
} = useStoreManagement()
const { storeMineCouponAndPointsRef, queryMyCouponAndIntegral } = useCouponAndIntegral()
const { storeInfo, getStoreInfoByStoreId } = useGetStoreInfo()

/** 我的订单 */
const mineOrderOptions = ref([
  {
    label: '待付款',
    key: OrderStatusEnum.WAIT_PAY,
    icon: WAIT_PAY,
    fieldName: 'pendingPaymentCount',
    value: 0,
    onClick: () => {
      routerPushByKey('MyOrders', { status: OrderStatusEnum.WAIT_PAY })
    },
  },
  {
    label: '待发货/提货',
    key: OrderStatusEnum.WAIT_DELIVER,
    icon: WAIT_DELIVER,
    fieldName: 'toBeShippedCount',
    value: 0,
    onClick: () => {},
  },
  {
    label: '待收货',
    key: OrderStatusEnum.WAIT_RECEIVE,
    icon: WAIT_RECEIVE,
    fieldName: 'toBeReceivedCount',
    value: 0,
    onClick: () => {},
  },
  {
    label: '退款/售后',
    key: OrderStatusEnum.REFUND,
    icon: REFUND,
    fieldName: 'afterSale',
    value: 0,
    onClick: () => {},
  },
])

/** 更多 */
const moreOptions = [
  {
    label: '福利券商城',
    key: 'coupon',
    icon: couponStoreSrc,
    onClick: () => {},
  },
  {
    label: '积分商城',
    key: 'integral',
    icon: integralStoreSrc,
    onClick: () => {},
  },
  {
    label: '收货地址',
    key: 'address',
    icon: addressIconSrc,
    onClick: () => {},
  },
  {
    label: '钱包',
    key: 'wallet',
    icon: walletIconSrc,
    onClick: () => {},
  },
  {
    label: '账号设置',
    key: 'setting',
    icon: settingSrc,
    onClick: () => {},
  },
]

/** 字体大小 */
const getSizeClass = (value) => {
  const num = Number(value)
  if (num >= 10000000) return 'store-size-ssm'
  if (num >= 1000000) return 'store-size-sm'
  if (num >= 100000) return 'store-size-md'
  if (num >= 10000) return 'store-size-lg'
  return 'store-size-xl'
}

/** 页面初始化 */
async function onInit() {
  try {
    /** 获取门店用户信息 */
    await getStoreMemberInfo()
    // /** 获取门店信息 */
    // await getStoreInfoByStoreId(storeId.value);
    // /** 我的-福利券张数&积分余额 */
    // await queryMyCouponAndIntegral();
    // if(userStore.storeUserInfo.type != StoreUserTypeEnum.CUSTOMER){
    //   await getOrderOverview();
    // }
    // /** 店长 */
    // if (userStore.storeUserInfo.type == StoreUserTypeEnum.OWNER) {
    //   await getStoreAfterSaleOrderCount();
    // }
    // await getOrderStatusCount();
  } catch (error) {
    console.log('页面初始化：' + error)
  }
}

const { setSelectedTabKey, setTabbarDisplay } = useTabbar()

onShow(() => {
  setSelectedTabKey(routesMap[RoutesName.StoreMine].path)
  setTabbarDisplay(true)
})

onLoad(() => {
  onInit()
})
</script>

<style scoped lang="scss">
.store_mine_wrapper {
  width: 100vw;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 120%;
  padding: 0 24rpx 0 24rpx;
  box-sizing: border-box;
  .store_mine_user_info {
    display: flex;
    align-items: center;
    padding: 24rpx 16rpx;
    gap: 8px;
    .store_mine_user_avatar {
      width: 136rpx;
      height: 136rpx;
      border-radius: 50%;
      border: 4rpx solid #ffffff;
    }
    .store_mine_user_info_right {
      display: flex;
      flex-direction: column;
      gap: 8rpx;
      .store_mine_user_info_right_name {
        height: 52rpx;
        display: flex;
        align-items: center;
        gap: 16rpx;
        .name {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 36rpx;
          color: #ffffff;
          line-height: 52rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .store_mine_user_info_owner,
        .store_mine_user_info_staff {
          width: 80rpx;
          height: 44rpx;
          border-radius: 4px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 24rpx;
          color: #ffffff;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .store_mine_user_info_owner {
          background: linear-gradient(270deg, #1677ff 0%, #16c1ff 100%);
        }
        .store_mine_user_info_staff {
          background: linear-gradient(270deg, #ff8922 0%, #ffe416 100%);
        }
      }
      .store_mine_user_info_right_serial {
        height: 44rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 44rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        gap: 8rpx;
        .store_mine_user_info_right_icon {
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
  }
  .store_mine_user_coupon_info {
    width: 96%;
    height: 128rpx;
    min-height: 128rpx;
    background: url('@/static/images/storeUser/storeCouponBg.png') no-repeat center;
    background-size: 100% 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .store_mine_user_coupon_info_item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .store_mine_user_coupon_info_item_name,
      .store_mine_user_coupon_info_item_num {
        font-family: Source Han Sans CN, Source Han Sans CN;
        color: #ffffff;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .store_mine_user_coupon_info_item_name {
        height: 44rpx;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 44rpx;
      }
      .store_mine_user_coupon_info_item_num {
        height: 48rpx;
        font-weight: 500;
        font-size: 32rpx;
        line-height: 48rpx;
      }
    }
  }
  .title {
    height: 48rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 48rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .header-right {
    display: flex;
    align-items: center;
    .header-right-text {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      line-height: 40rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .header-right-icon {
      width: 28rpx;
      height: 28rpx;
    }
  }
  .store_mine_order,
  .store_mine_management,
  .store_mine_district,
  .store_mine_more {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8rpx;
    margin-top: 24rpx;
    box-sizing: border-box;
    .store_mine_order_item,
    .store_mine_management_item,
    .store_mine_district_item,
    .store_mine_more_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 152rpx;
      height: 122rpx;
      box-sizing: border-box;
      margin-top: 8rpx;
      &:hover {
        opacity: 0.9;
        background: #f5f5f5;
        transition: all 0.2s ease;
        border-radius: 16rpx;
      }
      :deep(.van-badge--top-right) {
        top: 12rpx;
        right: 4rpx;
      }
      .store_mine_item_icon {
        width: 64rpx;
        height: 64rpx;
      }
      .store_mine_item_text {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  // 我的订单数据概览
  .store_management_container {
    display: flex;
    flex-direction: column;
    margin-top: 40rpx;
    .store_management_container_data {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      box-sizing: border-box;
      border-radius: 8rpx;
      box-shadow: rgba(0, 0, 0, 0.1) 0px -6rpx 6rpx 0rpx;
      .store_management_container_data_item {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        width: 24%;
        height: 136rpx;
        padding: 0px 12rpx;
        box-sizing: border-box;
        .data_title {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 24rpx;
          color: #666666;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .data_num {
          font-family: DIN Pro, DIN Pro;
          font-weight: 500;
          font-size: 40rpx;
          color: #333333;
          line-height: 56rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .store-size-ssm {
          font-size: 22rpx;
        }
        .store-size-sm {
          font-size: 24rpx;
        }
        .store-size-md {
          font-size: 26rpx;
        }
        .store-size-lg {
          font-size: 28rpx;
        }
        .store-size-xl {
          font-size: 36rpx;
        }
      }
      .store_management_container_data_line:not(:nth-child(8n)) {
        width: 2rpx;
        height: 48rpx;
        background: #eeeeee;
      }
    }
  }
}
</style>
