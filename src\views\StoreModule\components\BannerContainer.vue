<template>
    <div class="wrapper">
        <div class="banner-header">
            <slot name="title"></slot>
            <slot name="headerRight"></slot>
        </div>
        <slot></slot>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";

defineOptions({ name: 'BannerContainer' });
</script>

<style lang="less" scoped>
.wrapper {
    width: 100%;
    background: linear-gradient( 180deg, #FFF3EA 0%, #FFFFFF 100%);
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    .banner-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
    }
}
</style>