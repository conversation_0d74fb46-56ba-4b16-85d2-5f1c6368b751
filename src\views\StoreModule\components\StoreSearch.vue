<template>
  <div class="search-wrapper">
    <div class="search">
      <div class="search-btn">
        <img :src="searchIcon" alt="" class="search-btn-icon" />
      </div>
      <div class="search-input">
        <!-- 绑定本地状态 -->
        <VanField
          clearable
          :type="props.type"
          v-model="localValue"
          :placeholder="props.placeholder"
          @update:model-value="handleInput"
          enterkeyhint="search"
          @keyup.enter="searchEvent"
        />
      </div>
      <div class="search-btn-wrapper" @click="handleSearch">搜索</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { FieldType } from "vant";
import searchIcon from "@/assets/storeImage/storeHome/search.png";

defineOptions({ name: 'StoreSearch' });

/** props */
const props = withDefaults(defineProps<{
  type?: FieldType;
  value: string;
  placeholder: string;
}>(), {
  placeholder: '请输入内容',
  type: 'text'
});

/** emit */
const emit = defineEmits<{
  (e: 'update:value', value: string): void;
  (e: 'search'): void;
}>();

const localValue = ref(props.value);

const handleInput = (value: string) => {
  localValue.value = value;
  emit('update:value', value);
};

function handleSearch() {
  emit('search');
}

function searchEvent() {
  if (document.activeElement instanceof HTMLElement) {
    document.activeElement.blur();
    handleSearch();
  }
}

/** 监听 props.value 的变化，更新本地状态 */
watch(() => props.value, (newVal) => {
  localValue.value = newVal;
});
</script>

<style lang="less" scoped>
.search-wrapper {
  height: 48px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  box-sizing: border-box;
  .search {
    width: 100%;
    height: 32px;
    background: #F8F8F8;
    border-radius: 27px;
    display: flex;
    align-items: center;
    .search-input {
      height: 100%;
      flex: 1;
      display: flex;
      align-items: center;
    }
    .search-btn {
      width: 32px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      .search-btn-icon {
        width: 16px;
        height: 16px;
      }
      &:hover {
        background: #e8e8e8;
        border-top-right-radius: 27px;
        border-bottom-right-radius: 27px;
      }
    }
    .search-btn-wrapper {
      width: 58px;
      height: 28px;
      background: #EF1115;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
:deep(.van-cell) {
  padding: 0;
  background: #F8F8F8;
}
.search-type-list {
  width: 80px;
  background: #FFFFFF;
  .search-type-item {
    height: 36px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 20px;
    text-align: right;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #EEEEEE;
    box-sizing: border-box;
  }
}
</style>
