<template>
  <VanDialog v-model:show="showRef" title="拒绝原因" show-cancel-button @confirm="onConfirm" @cancel="onCancel">
    <div class="wrapper">
      <VanField
        :border="false"
        type="textarea"
        v-model="model.rejectionReasonDescription"
        :maxlength="100"
        :autosize="{ minHeight: 100 }"
        :show-word-limit="true"
        placeholder="请输入拒绝原因"
        class="refund_reason_textarea"
      />
    </div>
  </VanDialog>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { showToast } from "vant";
import { StoreRoleOperationEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { executeAfterSaleActionByAdminApi } from "@/services/storeApi";

defineOptions({ name: "StoreRefundReject" });

/** props */
const props = defineProps<{
  show: boolean;
  recordNo?: string;
}>();

/** emits */
const emit = defineEmits<{
  (e: "update:show", show: boolean): void;
  /** 操作成功 */
  (e: "success"): void;
}>();

const { createMessageSuccess, createMessageError } = useMessages();

const showRef = computed({
  get: () => props.show,
  set: (val) => emit("update:show", val),
});

const model = ref({
  rejectionReasonDescription: "",
});

/** methods */
async function onConfirm() {
  try {
    let _params = {
      rejectionReasonDescription: model.value.rejectionReasonDescription,
      recordNo: props.recordNo,
      action: StoreRoleOperationEnum.REFUSE_APPLICATION
    };
    await executeAfterSaleActionByAdminApi(_params);
    showToast("操作成功");
    emit("success");
    emit("update:show", false);
  } catch (error) {
    createMessageError("操作失败：" + error);
  }
}

function onCancel() {
  emit("update:show", false);
}
</script>

<style lang="less" scoped>
.wrapper {
    padding: 12px 16px;
}
.refund_reason_textarea {
  background: #F8F8F8;
  border-radius: 4px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
