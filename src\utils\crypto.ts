// import CryptoJS from 'crypto-js'
const SECRET_KEY = "jtsoft0123456789";
import AES from "crypto-js/aes";
import CORE from "crypto-js/core";
import MD5 from "crypto-js/md5";
// 加密函數
function encryption(word:string,encrypt_key:string=SECRET_KEY,decrypt_iv:string=SECRET_KEY):string {
  const key = CORE.enc.Utf8.parse(encrypt_key);
  const iv = decrypt_iv?CORE.enc.Utf8.parse(decrypt_iv):key

  let enc = "";
  if (typeof word === "string") {
    enc = AES.encrypt(word, key, {
      iv,
      mode: CORE.mode.CBC,
      padding: CORE.pad.Pkcs7,
    });
  } else if (typeof word === "object") {
    let data = JSON.stringify(word);
    enc = AES.encrypt(data, key, {
      iv,
      mode: CORE.mode.CBC,
      padding: CORE.pad.Pkcs7,
    });
  }
  let encResult = enc.toString();
  return encResult;
}
// 解密函數
function decryption(word:string,decrypt_key:string=SECRET_KEY,decrypt_iv:string=SECRET_KEY):string {
  const key = CORE.enc.Utf8.parse(decrypt_key);
  const iv = decrypt_iv?CORE.enc.Utf8.parse(decrypt_iv):key
  let dec = AES.decrypt(word, key, {
    iv,
    mode: CORE.mode.CBC,
    padding: CORE.pad.Pkcs7,
  });
  let decData = dec.toString(CORE.enc.Utf8);
  return decData;
}

function md5Encryption(word) {
  return MD5(word).toString();
}

export { decryption, encryption, md5Encryption };
