import { ref } from "vue";
import { CouponStatusEnum, StoreCouponRouteTypeEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { getWelfareCouponDetail, getWelfareCouponByUserIdAndCouponId } from "@/services/storeApi";

export default function useGetCoupDetails(_params: {
  type: StoreCouponRouteTypeEnum;
  userId: string;
  categoryId: string;
  useStatus: CouponStatusEnum;
 }) {
  const { createMessageSuccess, createMessageError } = useMessages();
  const isPageLoadingRef = ref(false);
  /** tab */
  const activeTabRef = ref(Number(_params.useStatus) ?? CouponStatusEnum.NOT_USED);

  const model = ref({
    type: _params.type,
    userId: _params.userId,
    categoryId: _params.categoryId,
  });

  /** 福利券明细数据 */
  const couponDetailsList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(true);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);

  /** 加载数据 */
  function onLoad() {}

  /** 获取搜索参数 */
  function getSearchParams() {
    const { categoryId, type, userId } = model.value;
    if (type == StoreCouponRouteTypeEnum.MY_COUPON) {
      return {
        categoryId,
        useStatus: activeTabRef.value,
      };
    } else {
      return {
        userId,
        categoryId,
        useStatus: activeTabRef.value,
      };
    }
  }

  /** 获取福利券详情 */
  async function getCouponDetails() {
    const _params = getSearchParams();
    const { type } = model.value; 
    isPageLoadingRef.value = true;

    try {
      let resp = null;
      if (type == StoreCouponRouteTypeEnum.MY_COUPON) {
        resp = await getWelfareCouponDetail(_params);
      }
      if (type == StoreCouponRouteTypeEnum.SCAN_COUPON) {
        resp = await getWelfareCouponByUserIdAndCouponId(_params);
      }

      if (resp) {
        couponDetailsList.value = resp ?? [];
      }
    } catch (error) {
      createMessageError("获取福利券详情失败：" + error);
    } finally {
      isPageLoadingRef.value = false;
      refreshingRef.value = false;
    }
  }

  /** 刷新 */
  function onRefresh() {
    // 重新加载数据
    getCouponDetails();
  }

  return {
    isPageLoadingRef,
    couponDetailsList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    activeTabRef,
    onLoad,
    onRefresh,
    getCouponDetails,
  };
}
