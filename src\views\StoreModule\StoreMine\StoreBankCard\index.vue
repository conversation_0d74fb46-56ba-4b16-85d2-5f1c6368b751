<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh">
    <div class="store_bankCard__wrapper">
      <div class="store_bankCard__content">
        <!-- 提现金额 -->
        <div class="store_bankCard__title" style="margin-bottom: 18px;">
          <span style="color: #FF3E3E;">*</span>
          账户类型
        </div>
        <VanRadioGroup
          v-model="formValue.accountType"
          direction="horizontal"
          shape="dot"
          style="margin-left: 12px;gap: 24px;"
        >
          <VanRadio icon-size="16px" :name="AccountTypeEnum.PRIVATE">
            <span class="van_radio_group__text">对私账户</span>
          </VanRadio>
          <VanRadio icon-size="16px" :name="AccountTypeEnum.PUBLIC">
            <span class="van_radio_group__text">对公账户</span>
          </VanRadio>
        </VanRadioGroup>

        <!-- 户名 -->
        <div class="store_bankCard__title" style="margin-top: 16px;">
          <span style="color: #FF3E3E;">*</span>
          户名
        </div>
        <VanField
          v-model="formValue.accountName"
          placeholder="私户请输入开户人姓名，公户请输入公司名称"
          :maxlength="60"
          :border="false"
          style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
        />

        <!-- 银行卡号 -->
        <div class="store_bankCard__title" style="margin-top: 16px;">
          <span style="color: #FF3E3E;">*</span>
          银行卡号
        </div>
        <VanField
          v-model="formValue.cardNumber"
          placeholder="请输入银行卡号"
          type="number"
          :minlength="13"
          :maxlength="19"
          :border="false"
          style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
        />

        <template v-if="formValue.accountType == AccountTypeEnum.PUBLIC">
          <transition appear name="fade" mode="out-in">
            <div class="store_opening_bank_wrapper">
              <!-- 开户行 -->
              <div class="store_bankCard__title" style="margin-top: 16px;">
                <span style="color: #FF3E3E;">*</span>
                开户行
              </div>
              <!-- 开户行 -->
              <div v-if="!formValue.bankId" class="store_bankCard__bank_container" @click="handleAddOpeningBank">
                <div class="store_bankCard__bank" @click="">
                  <SvgIcon name="IonAdd" style="font-size: 18px;" />
                  <span class="title">添加开户行</span>
                </div>
              </div>
              <!-- 开户行信息 -->
              <div
                v-else
                class="store_bankCard__bank_container"
                style="padding: 12px;display: flex;justify-content: space-between;"
              >
                <div class="store_bankCard__bank_left">
                  <!-- 账户名 -->
                  <div class="store_bankCard__bank_info">
                    <span class="store_bankCard__bank_info_content van-ellipsis">{{ formValue.bankName }}</span>
                  </div>
                  <!-- 银行卡号 -->
                  <div class="store_bankCard__bank_info">
                    <span class="store_bankCard__bank_info_title">行号：</span>
                    <span class="store_bankCard__bank_info_content">{{ formValue.bankNo }}</span>
                  </div>
                </div>
                <div class="store_bankCard__bank_right" @click="handleAddOpeningBank">
                  <span>请选择</span>
                  <VanIcon name="arrow" style="color:  #D9D9D9;font-size: 12px;" />
                </div>
              </div>
            </div>
          </transition>
        </template>
      </div>

      <!-- footer -->
      <div class="footer">
        <VanRow justify="space-between" gutter="8">
          <VanCol span="24">
            <VanButton v-if="isEdit" type="danger" @click="handleUpdate(props.id)" round block style="width: 100%;height: 36px;">
              修 改
            </VanButton>
            <VanButton v-else type="danger" @click="handleSave" round block style="width: 100%;height: 36px;">
              保 存
            </VanButton>
          </VanCol>
        </VanRow>
      </div>
    </div>
    <!-- 选择开户行 -->
    <StoreOpeningBank v-model:show="isShowOpeningBank" @select="handleSelectOpeningBank" />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import { useBoolean } from "../../hooks";
import { AccountTypeEnum } from "@/views/StoreModule/enums";
import useStoreBankCard from "./hooks/useStoreBankCard";
import useBankCardInfo from "@/views/StoreModule/StoreMine/hooks/useBankCardInfo";
/**  相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreOpeningBank from "./components/StoreOpeningBank.vue";

defineOptions({ name: "StoreBankCard" });

/** props */
const props = defineProps<{
  id?: string;
}>();

/** 是否编辑状态 */
const isEdit = computed(() => props.id);

const { bankCardInfo, getBankCardInfo, hasBankCardInfo } = useBankCardInfo();
const { bool: isShowOpeningBank, setTrue: setIsShowOpeningBank, setFalse: setIsShowOpeningBankFalse } = useBoolean(false);
const {
  isPageLoadingRef,
  formValue,
  handleSave,
  handleUpdate
} = useStoreBankCard();

/** 添加开户行 */
function handleAddOpeningBank() {
  setIsShowOpeningBank();
}

/** 选择开户支行回调 */
function handleSelectOpeningBank(openingBank: {
  id: string;
  name: string;
  no: string;
}) {
  formValue.value.bankId = openingBank.id;
  formValue.value.bankName = openingBank.name;
  formValue.value.bankNo = openingBank.no;
}

onMounted(async () => {
  if (isEdit.value) {
    isPageLoadingRef.value = true;
    await getBankCardInfo();
    isPageLoadingRef.value = false;
  }
});

/** 监听 */
watch(
  () => hasBankCardInfo.value,
  (val) => {
    if (val) {
      Object.assign(formValue.value, bankCardInfo.value);
    }
  }
);
</script>

<style lang="less" scoped>
.store_bankCard__wrapper {
  width: 100%;
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
  padding-bottom: calc(env(safe-area-inset-bottom) + 12px);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .store_bankCard__content {
    flex: 1;
    .store_bankCard__title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin: 8px 0px;
    }
    .van_radio_group__text {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }
    .store_bankCard__bank_container {
      box-sizing: border-box;
      background: #FFFFFF;
      box-shadow: 0px 4px 16px -4px rgba(12,12,12,0.12);
      border-radius: 8px;
      .store_bankCard__bank {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        .title {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .store_bankCard__bank_left {
        display: flex;
        flex-direction: column;
        gap: 4px;
        .store_bankCard__bank_info {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          .store_bankCard__bank_info_title {
            color: #999999;
          }
          .store_bankCard__bank_info_content {
            color: #333333;
          }
        }
      }
      .store_bankCard__bank_right {
        display: flex;
        align-items: center;
        gap: 4px;
        span {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #1677FF;
          line-height: 22px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }

  .footer {
    box-sizing: border-box;
    :deep(.van-button__text) {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }
  }
}
</style>
