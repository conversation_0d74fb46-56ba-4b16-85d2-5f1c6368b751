<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" @scroll="onScroll" class="coupon-content" :class="`coupon-content_${props.useStatus}`">
      <template v-if="couponList.length">
        <VanList v-model:loading="isLoadingRef" :finished="isFinishedRef" finished-text="没有更多了">
          <CouponCard
            v-for="item in couponList"
            :key="item.id"
            :couponInfo="item"
            @onChange="(key) => handleChange(key, item)"
            :type="props.type"
            :useStatus="props.useStatus"
          />
        </VanList>
      </template>
      <template v-else>
        <EmptyData />
      </template>
    </VanPullRefresh>
    <!-- 兑换说明 -->
    <RedeemInstructions v-model:show="showInstructionsRef" :couponInfo="currentCouponRef" />
    <!-- 核销 -->
    <CouponRedeem v-model:show="showRedeemRef" :couponInfo="currentCouponRef" :userId="props.userId" @success="handleRedeemSuccess" />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, watch, onActivated, nextTick } from "vue";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import useGetCouponList from "../hooks/useGetCouponList";
import { OperationTypeEnum, CouponStatusEnum, StoreCouponRouteTypeEnum, KeepAliveRouteNameEnum } from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import CouponCard from "./CouponCard.vue";
import RedeemInstructions from "./RedeemInstructions.vue";
import CouponRedeem from "./CouponRedeem.vue";

defineOptions({ name: 'CouponList' });

/** props */
const props = defineProps<{
  userId: string;
  useStatus: CouponStatusEnum;
  type: StoreCouponRouteTypeEnum;
}>();

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom } = useKeepAliveRoute();
const {
  isPageLoadingRef,
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  couponList,
  model: modelRef,
  onRefresh,
  getCouponList
} = useGetCouponList({
  userId: props.userId,
  useStatus: props.useStatus
});

const showInstructionsRef = ref(false);
const showRedeemRef = ref(false);
const { routerPushByRouteName } = useRouterUtils();
const currentCouponRef = ref({});

function handleChange(key: OperationTypeEnum, couponData) {
  /** 策略模式 */
  const actions = {
    /** 点击兑换说明 */
    [OperationTypeEnum.EXCHANGE]: () => {
      showInstructionsRef.value = true;
      currentCouponRef.value = couponData;
    },
    /** 点击券明细 */
    [OperationTypeEnum.VOUCHER_DETAILS]: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.COUPON);
      routerPushByRouteName(RoutesName.StoreCouponDetail, {
        type: props.type,
        userId: props.userId,
        categoryId: couponData.categoryId ?? "",
        useStatus: props.useStatus,
        categoryName: couponData.categoryName ?? "",
        exchangeNote: couponData.exchangeNote ?? "",
      });
    },
    /** 点击核销 */
    [OperationTypeEnum.VERIFICATION]: () => {
      showRedeemRef.value = true;
      currentCouponRef.value = couponData;
    },
  };

  // 如果未定义的动作，则直接返回
  if (!actions[key]) {
    return;
  }

  // 执行对应动作
  actions[key]();
}

/** 核销成功回调 */
function handleRedeemSuccess() {
  getCouponList();
}

/** 滚动触发 */
function onScroll(e) {
  scrollEventHandler(e, KeepAliveRouteNameEnum.COUPON);
}

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName(`coupon-content_${props.useStatus}`)[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.COUPON);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.COUPON);
  });
});

/** 监听 */
watch(
  () => props.useStatus,
  (newVal) => {
    modelRef.value.useStatus = newVal;
    getCouponList();
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
.coupon-content {
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
  overflow-y: auto;
}
</style>
