import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";
import { StoreIntegralRouteTypeEnum, StoreCouponRouteTypeEnum, StorePendingOrderRouteTypeEnum } from "@/views/StoreModule/enums";

export const StoreHome1 = {
  [RoutesName.StoreHome]: {
    path: "main",
    component: () => import("@/views/StoreModule/StoreHome/index.vue"),
    meta: {
      isMenu: true,
      title: "首页",
      level: 1,
      icon: {
        active: "/icons/storeTabbar/home-active.png",
        inactive: "/icons/storeTabbar/home.png",
      },
    },
  },
  [RoutesName.StoreCoupon]: {
    path: "couponReceiveRecord/getByCouponUseId",
    component: () => import("@/views/StoreModule/StoreHome/CouponModule/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "福利券",
    },
    props: (route: RouteLocation) => ({
      type: route.query.type ?? StoreCouponRouteTypeEnum.MY_COUPON,
      userId: route.query.userId ?? "",
    }),
  },
  [RoutesName.StoreCouponDetail]: {
    path: "couponDetail",
    component: () => import("@/views/StoreModule/StoreHome/CouponDetails/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "福利券",
    },
    props: (route: RouteLocation) => ({
      type: route.query.type ?? "",
      userId: route.query.userId ?? "",
      categoryId: route.query.categoryId ?? "",
      useStatus: route.query.useStatus ?? "",
      categoryName: route.query.categoryName ?? "",
      exchangeNote: route.query.exchangeNote ?? "",
    }),
  },
  [RoutesName.StoreIntegral]: {
    path: "integral",
    component: () => import("@/views/StoreModule/StoreHome/PointDetails/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "积分",
    },
    props: (route: RouteLocation) => ({
      userId: route.query.userId ?? "",
      unionId: route.query.unionId ?? "",
      type: route.query.type ?? StoreIntegralRouteTypeEnum.MY_INTEGRAL,
    }),
  },
  [RoutesName.StorePendingVerificationOrders]: {
    path: "pendingOrders",
    component: () => import("@/views/StoreModule/StoreHome/PendingVerificationOrders/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "待核销订单",
    },
    props: (route: RouteLocation) => ({
      userId: route.query.userId ?? "",
      type: route.query.type ?? StorePendingOrderRouteTypeEnum.MY_WAIT_VERIFY,
    }),
  },
  [RoutesName.StoreWatchTime]: {
    path: "watchTime",
    component: () => import("@/views/StoreModule/StoreHome/StoreWatchTime/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "观看时长",
    },
    props: (route: RouteLocation) => ({
      userId: route.query.userId ?? "",
    })
  },
  [RoutesName.StoreInviteMember]: {
    path: "inviteStoreMember",
    redirect: "/st/main",
    meta: {
      title: "首页",
      isMenu: false,
      isShow: false,
    },
  },
};
