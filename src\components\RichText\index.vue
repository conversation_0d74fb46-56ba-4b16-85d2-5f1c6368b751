<template>
    <div class="rich-text-wrapper" v-html="props.value"></div>
</template>
<script setup lang="ts">
interface RichTextProps{
    /**富文本 */
    value:string
}
const props = withDefaults(defineProps<RichTextProps>(),{
    value:''
})


</script>
<style lang="less" scoped>
@import "@/assets/quill/quill.snow.css";
.rich-text-wrapper{
    line-height:initial;
    background-color: #fff;
}

</style>