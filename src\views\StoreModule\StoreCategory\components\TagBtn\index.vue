<template>
    <div class="tag-btn" :class="_class">
      <slot></slot>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
type BtnType = 'primary' | 'success' | 'warning' | 'danger';
const props = withDefaults(defineProps<{
   active:boolean;
   type?:BtnType;
   disabled:boolean;
}>(),{
   active:false,
   type:'warning',
   disabled:false
})
const _class = computed(()=>{
    return {
        'disabled':props.disabled,
        [`${props.type}-active`]:props.active
    }
})
</script>

<style scoped lang="less">
@import "@/styles/defaultVar.less";
.primary-active {
    border-color: @primary-color !important;
    background-color: #F8F8F8 !important;
    color: @primary-color !important;
}
.success-active {
    border-color: @success-color !important;
    background-color: #F8F8F8 !important;
    color: @success-color !important;
}
.warning-active {
    border-color: @warning-color !important;
    background-color: #F8F8F8 !important;
    color: @warning-color !important;
}
.danger-active {
    border-color: @error-color !important;
    background-color: #F8F8F8 !important;
    color: @error-color !important;
}
.disabled{
    filter: opacity(50%);
    cursor: not-allowed;
}
.tag-btn{
    font-size: 12px;
    padding: 7px 12px;
    border-radius: 40px;
    background-color: #F8F8F8;
    border: 1px solid transparent;
    cursor: pointer;
    white-space: nowrap;
}
</style>