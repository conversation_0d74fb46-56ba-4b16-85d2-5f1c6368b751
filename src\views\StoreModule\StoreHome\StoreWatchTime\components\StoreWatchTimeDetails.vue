<template>
  <VanPopup
    :show="showRef"
    @close="close"
    :style="{ width: '100%', borderRadius: '16px', background: '#fff', padding: '12px' }"
    :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)' }"
  >
    <div class="wrapper">
        <p class="title">进入时间-离开时间</p>
        <div class="watch-time-list-container">
            <div class="watch-time-item" v-for="item in watchTimeListRef" :key="item.watchStart">
                <p>{{`${item.watchStart} - ${item.watchEnd}`}}</p>
            </div>
        </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed, toRefs } from "vue";

defineOptions({ name: 'StoreWatchTimeDetails' });

/** props */
const props = withDefaults(defineProps<{
  show: boolean;
  watchTimeList?: Array<{
    watchStart: string;
    watchEnd: string;
  }>;
}>(), {
});

/** emit */
const emit = defineEmits<{
    (e: 'update:show', show: boolean): void;
}>();

const { watchTimeList: watchTimeListRef } = toRefs(props);

const showRef = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

function close() {
  showRef.value = false;
}
</script>

<style lang="less" scoped>
.wrapper {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-bottom: 18px;
    }
    .watch-time-list-container {
        width: 100%;
        max-height: 400px;
        overflow-y: scroll;
        .watch-time-item {
            width: 100%;
            height: 32px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 15px;
            color: #333333;
            text-align: left;
            font-style: normal;
            text-transform: none;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #f0f0f0;
            &:last-child {
                border-bottom: none;
            }
        }
    }
}
</style>
