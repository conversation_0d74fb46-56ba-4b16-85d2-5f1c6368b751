<template>
  <div class="store_return_order_wrapper">
    <JLoadingWrapper :show="isPageLoadingRef">
      <!-- tabs -->
      <VanTabs
        :style="{ 
          '--van-tabs-line-height': '40px', 
          height: isStatusFinished ? '100%' : 'calc(100% - 62px)'
        }"
        v-model:active="activeTabRef"
        color="#EF1115"
        title-active-color="#EF1115"
        title-inactive-color="#333333"
        line-width="36px"
        line-height="2px"
        swipeable
      >
        <VanTab v-for="item in returnOrderStatusList" :key="item.value" :name="item.value">
          <template #title>
            <span class="tab-title" :class="{ 'tab-active': activeTabRef === item.value }">{{ item.label }}</span>
          </template>
          <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="tab-content">
            <template v-if="storeReturnOrderList.length">
              <VanList
                v-model:loading="isLoadingRef"
                :finished="isFinishedRef"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <template
                  v-if="[ReturnOrderStatusEnum.PENDING_RETURN, ReturnOrderStatusEnum.LOCKED].includes(activeTabRef)"
                >
                  <VanCheckboxGroup v-model="checkedReturnOrderListId" checked-color="#ee0a24">
                    <VanCheckbox v-for="item in storeReturnOrderList" :key="item.id" :name="item.id">
                      <StoreReturnOrderCard :orderInfo="item" />
                    </VanCheckbox>
                  </VanCheckboxGroup>
                </template>
                <template v-else>
                  <StoreReturnOrderCard v-for="item in storeReturnOrderList" :key="item.id" :orderInfo="item" />
                </template>
              </VanList>
            </template>
            <template v-else>
              <EmptyData style="min-height: 400px;" />
            </template>
          </VanPullRefresh>
        </VanTab>
      </VanTabs>
      <!-- footer -->
      <div
        v-if="[ReturnOrderStatusEnum.PENDING_RETURN, ReturnOrderStatusEnum.LOCKED].includes(activeTabRef)"
        class="footer"
      >
        <template v-if="ReturnOrderStatusEnum.PENDING_RETURN === activeTabRef">
          <div class="footer-left">
            <div class="order-count">
              退货总件数：
              <span>{{ totalRefundCount }}</span>
            </div>
            <div class="order-count">
              退款总金额：
              <span>{{`￥${totalRefundAmount}`}}</span>
            </div>
          </div>
          <div class="footer-right">
            <VanButton
              type="danger"
              @click="showConfirmReturnRef"
              :disabled="!checkedReturnOrderListId.length"
              round
              block
              style="width: 120px;height: 40px;"
            >
              确定退货
            </VanButton>
          </div>
        </template>
        <template v-if="ReturnOrderStatusEnum.LOCKED === activeTabRef">
          <VanButton
            type="danger"
            @click="handleFillLogistics"
            :disabled="!checkedReturnOrderListId.length"
            round
            block
          >
            填写物流
          </VanButton>
        </template>
      </div>
    </JLoadingWrapper>
    <!-- 确定退货 -->
    <StoreConfirmReturn
      v-model:show="showConfirmReturn"
      @confirm="confirmReturnOrder"
      @confirmAndFillLogistics="handleConfirmAndFillLogistics"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import useGetReturnOrder from "./hooks/useGetReturnOrder";
import { useBoolean } from "@/views/StoreModule/hooks";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { ReturnOrderStatusEnum, StoreLogisticsRouteTypeEnum } from "@/views/StoreModule/enums";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreReturnOrderCard from "./components/StoreReturnOrderCard.vue";
import StoreConfirmReturn from "./components/StoreConfirmReturn.vue";

defineOptions({ name: 'StoreReturnOrder' });

const {
    isPageLoadingRef,
    activeTabRef,
    returnOrderStatusList,
    refreshingRef,
    storeReturnOrderList,
    isFinishedRef,
    isLoadingRef,
    onRefresh,
    onLoad,
    checkedReturnOrderListId,
    confirmReturnOrder
} = useGetReturnOrder();
const { bool: showConfirmReturn, setTrue: showConfirmReturnRef, setFalse: hideConfirmReturnRef } = useBoolean(false);
const { routerPushByRouteName } = useRouterUtils();

/** 筛选选中的退货单 */
const filteredReturnOrders = computed(() => {
  return storeReturnOrderList.value.filter(order =>
    checkedReturnOrderListId.value.includes(order.id)
  );
});

/** 计算选中的总退货件数 */
const totalRefundCount = computed(() => {
  return filteredReturnOrders.value.reduce((sum, order) => sum + (order.count || 0), 0);
});

/** 计算选中的总退款金额 */
const totalRefundAmount = computed(() => {
  return parseFloat((filteredReturnOrders.value.reduce((sum, order) => sum + (order.actualRefundAmount || 0), 0) / 100).toFixed(2));
});

/** 是否状态为已完成 */
const isStatusFinished = computed(() => {
  return [ReturnOrderStatusEnum.COMPLETED].includes(activeTabRef.value);
})

/** 确认收货并填写物流 */
function handleConfirmAndFillLogistics() {
  routerPushByRouteName(RoutesName.StoreFillLogistics, { 
    afterSalesOrderId: checkedReturnOrderListId.value.join(','),
    routeType: StoreLogisticsRouteTypeEnum.CONFIRM_AND_FILL_LOGISTICS,
  });
}

/** 填写物流 */
function handleFillLogistics() {
  routerPushByRouteName(RoutesName.StoreFillLogistics, { 
    afterSalesOrderId: checkedReturnOrderListId.value.join(','),
    routeType: StoreLogisticsRouteTypeEnum.FILL_LOGISTICS,
  });
}
</script>

<style lang="less" scoped>
.store_return_order_wrapper {
  width: 100%;
  height: 100vh;
  :deep(.van-tab) {
    flex: auto;
  }
  :deep(.van-tabs__line) {
    bottom: 18px;
  }
  .tab-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .tab-active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #EF1115;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  :deep(.van-tabs) {
    &__wrap {
      height: 40px;
    }

    &__content {
      height: calc(100% - 40px);
      .van-tab__panel {
        height: 100%;
      }
    }
   }
  .tab-content {
    height: 100%;
    padding: 12px 10px;
    box-sizing: border-box;
    overflow-y: auto;
    :deep(.van-checkbox__label) {
      flex: 1;
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 62px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    box-sizing: border-box;
    margin-bottom: env(safe-area-inset-bottom);
    .footer-left {
      display: flex;
      flex-direction: column;
      gap: 6px;
      .order-count {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        span {
          color: #EF1115;
        }
      }
    }
  }
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.store_return_footer_order_h1 {
  height: 62px;
}
.store_return_footer_order_h2 {
  height: 0;
}
</style>
