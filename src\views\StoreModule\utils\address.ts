export function groupByPinyinInitial(objects: { pinYin: string }[]): { letter: string, items: { pinYin: string }[] }[] {
    // 1. 复制原数组并排序（避免直接修改输入）
    const sortedObjects = [...objects].sort((a, b) => a.pinYin.localeCompare(b.pinYin));
    
    // 2. 使用 reduce 按拼音首字母分组
    const groupedObjects = sortedObjects.reduce((acc, obj) => {
        const firstLetter = obj.pinYin.charAt(0).toUpperCase();
        acc[firstLetter] = acc[firstLetter] || []; // 若不存在则初始化空数组
        acc[firstLetter].push(obj);
        return acc;
    }, {} as Record<string, { pinYin: string }[]>);
    
    // 3. 将分组对象转为目标数组格式
    return Object.entries(groupedObjects)
        .map(([letter, items]) => ({ letter, items }));
}