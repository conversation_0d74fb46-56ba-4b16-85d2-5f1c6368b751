<template>
  <div class="exception-wrapper">
    <img :src="exceptionInfo.imgSrc" alt="门店状态提示" />
    <p class="title">{{ exceptionInfo.notice }}</p>
    <p v-if="exceptionInfo.subContent" class="sub-content">{{ exceptionInfo.subContent }}</p>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
/** 静态资源 */
import NotPermissionSrc from '@/assets/storeImage/exception/notPermission.png';

interface ExceptionInfo {
  imgSrc: string;
  notice: string;
  subContent?: string;
}

const props = defineProps<{
  code: string;
}>();

/** 缺省信息 */
const exceptionMap = {
  "403": {
    imgSrc: NotPermissionSrc,
    notice: "门店已停用",
    subContent: ""
  }
} as Record<string, ExceptionInfo>;

// 计算属性获取异常信息
const exceptionInfo = computed(() => {
  return exceptionMap[props.code] || exceptionMap["403"];
});
</script>

<style lang="less" scoped>
.exception-wrapper {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 108px;
  box-sizing: border-box;
  background-color: #FFFFFF;

  img {
    width: 200px;
    height: 200px;
  }

  .title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .sub-content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666;
    line-height: 20px;
    text-align: center;
    max-width: 80%;
  }
}
</style>
