<template>
  <div class="store_wallet_card_wrapper">
    <div class="store_wallet_header">
      <div class="store_wallet_header_title">{{ withdrawRecordRef?.receiverName ?? `-` }}</div>
      <div class="store_wallet_header_right">
        {{`-￥${((withdrawRecordRef?.amount ?? 0) / 100).toFixed(2)}`}}
      </div>
    </div>
    <div class="store_wallet_reason_wrapper">
      <div class="store_wallet_header_reason">
        <span>{{`银行卡号：${withdrawRecordRef?.bankCardNo ?? '-'}`}}</span>
        <!-- 状态 -->
        <div
          class="store_wallet_header_status"
          :style="{ color: statusInfo?.color, backgroundColor: statusInfo?.bgColor }"
        >
          {{ statusInfo?.label ?? `-`}}
        </div>
      </div>
      <div class="store_wallet_header_time">{{`提现单号：${withdrawRecordRef?.applicationNo ?? '-'}`}}</div>
      <!-- 发票图片 -->
      <div v-if="invoiceImageListSrc?.length" class="invoice_img_wrapper">
        <div class="title">发票图片：</div>
        <div class="img_wrapper">
         <VanImage v-for="item in invoiceImageListSrc" :key="item" width="60" height="60" :src="item" /> 
        </div>
      </div>
    </div>
    <!-- 线 -->
    <div class="store_wallet_line"></div>
    <!-- 时间 -->
    <span class="store_wallet_time">{{ withdrawRecordRef?.createTime ?? `-` }}</span>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs } from "vue";
import { WithdrawStatusEnum } from "@/views/StoreModule/enums";

defineOptions({ name: "StoreWithdrawRecordCard" });

/** props */
const props = defineProps<{
  withdrawRecord: {
    createTime?: string;
    applicationNo?: string;
    amount?: number;
    status?: WithdrawStatusEnum;
    bankCardNo?: string;
    receiverName?: string;
    invoiceImage?: string; // 发票图片：多张以;分割
    invoiceImageList?: string[];
  }
}>();

const { withdrawRecord: withdrawRecordRef } = toRefs(props);

/** 发票图片 */
const invoiceImageListSrc = computed(() => {
  return withdrawRecordRef.value?.invoiceImageList ?? [];
});

/** 状态颜色 */
const statusInfoMap = {
  /** 待审核 */
  [WithdrawStatusEnum.PENDING_REVIEW]: {
    label: "待审核",
    color: "#196EFF",
    bgColor: "#E4EEFF",
  },
  /** 待打款 */
  [WithdrawStatusEnum.PENDING_PAYMENT]: {
    label: "待打款",
    color: "#A022FF",
    bgColor: "#F3EDFF",
  },
  /** 打款中 */
  [WithdrawStatusEnum.PAYMENT_IN_PROGRESS]: {
    label: "打款中",
    color: "#FF9309",
    bgColor: "#FFF3E3",
  },
  /** 打款失败 */
  [WithdrawStatusEnum.PAYMENT_FAILED]: {
    label: "打款失败",
    color: "#FF3B2F",
    bgColor: "#FF6864",
  },
  /** 已打款 */
  [WithdrawStatusEnum.PAID]: {
    label: "已打款",
    color: "#02C863",
    bgColor: "#E6FFF2",
  },
  /** 已驳回 */
  [WithdrawStatusEnum.REJECTED]: {
    label: "已驳回",
    color: "#999999",
    bgColor: "#F8F8F8",
  },
};
const statusInfo = computed(() => statusInfoMap[withdrawRecordRef.value?.status]);
</script>

<style lang="less" scoped>
.store_wallet_card_wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background-color: #fff;
    padding: 12px 16px;
    box-sizing: border-box;
    border-radius: 12px;
    margin-bottom: 12px;
    .store_wallet_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .store_wallet_header_title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_wallet_header_right {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 26px;
            text-align: right;
            font-style: normal;
            text-transform: none;
        }
    }
    .store_wallet_reason_wrapper {
        display: flex;
        flex-direction: column;
        gap: 2px;
        .store_wallet_header_reason,
        .store_wallet_header_time,
        .invoice_img_wrapper {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .invoice_img_wrapper {
          display: flex;
          flex-direction: column;
          gap: 8px;
          .title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .img_wrapper {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }
        }
        .store_wallet_header_reason {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .store_wallet_header_status {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 10px;
                line-height: 16px;
                text-align: center;
                font-style: normal;
                text-transform: none;
                border-radius: 2px;
                padding: 0px 5px;
                box-sizing: border-box;
            }
        }
    }
    .store_wallet_line {
        width: 100%;
        height: 1px;
        background: #f0f0f0;
        margin-top: 12px;
        margin-bottom: 8px;
    }
    .store_wallet_time {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>
