import { ref } from "vue";
import { getLiveRoomLink } from "@/services/storeApi";

export default function useGetLiveRoomLink() {
  /** 直播链接 */
  const liveRoomLink = ref<string>("");

  /** 获取用户今日最近进入的直播间链接 */
  async function getLiveRoomLinkData() {
    try {
        const liveRoomLinkRes = await getLiveRoomLink();
        if (liveRoomLinkRes) {
          liveRoomLink.value = liveRoomLinkRes;
        }
    } catch (error) {
      console.log("获取用户今日最近进入的直播间链接失败：" + error);
    }
  }

  return {
    liveRoomLink,
    getLiveRoomLinkData
  };
}
