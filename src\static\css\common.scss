// background-color
$default-background-color:#f2f2f2;
$white-background-color:#fff;

//font-color
$sub-title-color: rgba(0,0,0,0.45);
//box-shadow
$item-card-box-shadow:0px 0px 10px rgba(0,0,0,0.3);

//border-radius
$default-border-radius:5px;

//height
$tab-default-height: 44px;


.base-page{
	background:$default-background-color;
	height:100vh;
	width:100vw;
}
.flex-center{
	display: flex;
	justify-content: center;
	align-items: center;
}
.position-center{
	position: absolute;
	top:50%;
	left:50%;
	transform: translate(-50%,-50%);
}

.text-ellipsis{
	width:100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.text-wrap{
	word-wrap: break-word;
	word-break: break-all;
}
.isIPhoneXRegexBottom {
  padding-bottom: constant(safe-area-inset-bottom) !important;   /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom) !important;  /*兼容 IOS>11.2*/
}

 /* 订单详情组件表单样式 */
.preMessage{
    ::v-deep .van-cell{
        padding: 20rpx 20rpx 20rpx 0rpx !important;
      }
      ::v-deep .van-field__label{
          margin-left: 0rpx;
      }
}