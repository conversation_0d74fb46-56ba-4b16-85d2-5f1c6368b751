<template>
    <div class="banner-container" :style="[{ 'background-color': bgColor }, customStyle]">
        <div class="header" v-if="isShowTitle">
            <div class="header-title-container">
                <div class="header-line" v-if="isShowLine" :style="{ 'background-color': lineColor }"></div>
                <div class="header-title">{{ title }}</div>
            </div>
            <div class="header-more-container" v-if="isShowMore" @click="handleMore">
                查看更多
                <img class="rightIcon" :src="rightIcon" />
            </div>
        </div>
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
import type { StyleValue } from "vue";
import rightIcon from "@/assets/storeImage/rightArrow.png"
const props = withDefaults(defineProps<{
    title: string;
    lineColor: string;
    bgColor: string;
    isShowTitle: boolean;
    isShowLine: boolean;
    customStyle?: StyleValue;
    isShowMore?: boolean;
}>(), {
    title: "标题",
    lineColor: "#EF1115",
    bgColor: "#fff",
    isShowTitle: true,
    isShowLine: true,
    customStyle: "",
    isShowMore: false,
})
const emits = defineEmits<{
    (e: "clickMore"): void;
}>();
const handleMore = () => {
    emits('clickMore')
}
</script>

<style scoped lang="less">
@import url('@/styles/storeVar.less');
.banner-container {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    border-radius: 8px;

    .header {
        display: flex;
        justify-content: space-between;
        .header-title-container {
            display: flex;
            align-items: center;
            margin: 9px 0 12px 8px;

            .header-line {
                width: 3px;
                height: 16px;
                border-radius: 10px;
                margin-right: 5px;
            }

            .header-title {
                font-size: 16px;
                font-weight: bold;
            }
        }

        .header-more-container {
            font-size: 12px;
            color: #666666;
            display: flex;
            align-items: center;
            .rightIcon{
                width: 12px;
                height: 12px;
            }
        }
    }

}
</style>