<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <div class="store_my_dealer_wrapper">
      <!-- 邀请店长注册 -->
      <div class="my_dealer_container">
        <div class="my_dealer" @click="handleToDealerApplyQrCode">
          <SvgIcon name="IonAdd" style="font-size: 18px;" />
          <span class="title">邀请店长注册</span>
        </div>
      </div>

      <!-- tabs -->
      <div class="my_store_tabs_container">
        <div
          v-for="item in tabOptionsList"
          :key="item.value"
          class="my_store_item"
          :class="{ 'active_tab': activeTabRef == item.value }"
          @click="handleClickTab(item.value)"
        >
          <span class="title" :data-name="item.value">{{ item.label }}</span>
        </div>
      </div>

      <!-- 内容 -->
      <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" @scroll="onLoad" class="store_content_wrapper">
        <!-- 搜索栏 -->
        <div v-if="activeTabRef === StoreManageEnum.MY_STORE" class="form_header">
          <StoreSelectOrderType v-model:value="storeOrderTypeRef" />
          <StoreSmallSelectTime v-if="isSamllMobile" v-model:value="storeSelectTimeRef" />
          <StoreMyShopSelectTime v-else v-model:value="storeSelectTimeRef" />
        </div>
        <template v-if="storePendingListRef.length == 0 && activeTabRef === StoreManageEnum.PENDING_REVIEW">
          <EmptyData style="min-height: 400px;" />
        </template>
        <template v-else>
          <component
            :is="currentPage"
            :shopOrderOverview="shopOrderOverview"
            :storeOverview="storeOverviewRef"
            :shopOrderOverviewPage="shopOrderOverviewPage"
            :storePendingList="storePendingListRef"
            @success="onRefresh"
          />
        </template>
        <!-- 是否加载完 -->
        <div v-if="isFinishedRef && storePendingListRef.length > 0 || storeOverviewRef.length > 0" class="is_load_all">
          <span>没有更多数据了</span>
        </div>
      </VanPullRefresh>
    </div>
    <!-- 店长邀请码 -->
    <StoreApplyQrCode
      v-model:show="isShowDealerApplyQrCode"
      :qrCodeType="QrCodeTypeEnum.STORE_MANAGER_REGISTER"
      :qrCodeUrl="qrCodeUrlRef"
    />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { showToast } from "vant";
import QRcode from "qrcode";
import { useMessages } from "@/hooks/useMessage";
import useStoerManagement from "./hooks/useStoerManagement";
import { QrCodeTypeEnum, StoreManageEnum } from "@/views/StoreModule/enums";
import { getStructureApplyApi } from "@/services/storeApi";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreApplyQrCode from "../components/StoreApplyQrCode.vue";
import StoreMyShop from "./components/StoreMyShop/index.vue";
import StorePendingReview from "./components/StorePendingReview/index.vue";
import StoreSelectOrderType from "@/views/StoreModule/StoreMine/components/StoreSelectOrderType.vue";
import StoreSmallSelectTime from "@/views/StoreModule/StoreMine/components/StoreSmallSelectTime.vue";
import StoreMyShopSelectTime from "./components/StoreMyShopSelectTime.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";

defineOptions({ name: 'StoreManagement' });

const {
  refreshingRef,
  isPageLoadingRef,
  storeOrderTypeRef,
  storeSelectTimeRef,
  shopOrderOverview,
  shopOrderOverviewPage,
  isFinishedRef,
  tabOptionsList,
  activeTabRef,
  handleClickTab,
  onLoad,
  onRefresh,
  storePendingListRef,
  storeOverviewRef,
} = useStoerManagement();
const { createMessageSuccess, createMessageError } = useMessages();
const isShowDealerApplyQrCode = ref(false);
const qrCodeUrlRef = ref<string>('');

async function handleToDealerApplyQrCode() {
  try {
    const resp = await getStructureApplyApi();
    if (resp) {
      qrCodeUrlRef.value = await generateQRCode(resp);
      isShowDealerApplyQrCode.value = true;
    }
  } catch (error) {
    showToast({
      message: error,
      duration: 3000
    });
  }
}

/** 生成二维码 */
async function generateQRCode(url: string): Promise<string | null> {
  try {
    const qrCodeDataUrl = await QRcode.toDataURL(url, {
      width: 156,
      height: 156,
      margin: 2,
    });
    return qrCodeDataUrl;
  } catch (err) {
    createMessageError("生成二维码失败：" + err);
    return null;
  }
}

/** 相关组件 */
const pageMap = {
  [StoreManageEnum.MY_STORE]: StoreMyShop,
  [StoreManageEnum.PENDING_REVIEW]: StorePendingReview,
};

/** 当前页 */
const currentPage = computed(() => pageMap[activeTabRef.value]);

/** 屏幕Api */
const isSamllMobile = computed(() => {
  if (window.innerWidth <= 320) {
    return true;
  }
  return false;
});
</script>

<style lang="less" scoped>
.store_my_dealer_wrapper {
  width: 100%;
  height: 100vh;
  background-color: #F8F8F8;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .my_dealer_container {
    padding: 12px;
    box-sizing: border-box;
    .my_dealer {
      height: 48px;
      background: #FFFFFF;
      box-shadow: 0px 4px 16px -4px rgba(12,12,12,0.12);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .my_store_tabs_container {
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 12px;
    .my_store_item {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
      line-height: 24px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .active_tab {
      font-weight: 500;
      font-size: 24px;
      color: #333333;
    }
  }
  .store_content_wrapper {
    width: 100%;
    height: calc(100vh - 120px - env(safe-area-inset-bottom));
    padding: 8px 12px;
    box-sizing: border-box;
    overflow-y: auto;
    .form_header {
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .is_load_all {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
