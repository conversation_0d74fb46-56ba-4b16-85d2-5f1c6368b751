<template>
  <div class="j-date-picker">
    <div class="start-time-picker" @click="handleOpenPopup('start')">
      <div v-if="dateValue[0]" class="value">{{ moment(dateValue[0]).format("YYYY-MM-DD") }}</div>
      <div v-else class="placeholder">开始日期</div>
    </div>
    <div class="separator">至</div>
    <div class="end-time-picker" @click="handleOpenPopup('end')">
      <div v-if="dateValue[1]" class="value">{{ moment(dateValue[1]).format("YYYY-MM-DD") }}</div>
      <div v-else class="placeholder">结束日期</div>
    </div>
  </div>
  <van-popup v-model:show="startShow" position="bottom" @open="handleOpen" @close="handleCancel('start')">
    <van-date-picker
      v-model="startDate"
      title="选择日期"
      @confirm="handleConfirm('start')"
      @cancel="handleCancel('start')"
    />
  </van-popup>
  <van-popup v-model:show="endShow" position="bottom" @open="handleOpen" @close="handleCancel('end')">
    <van-date-picker
      v-model="endDate"
      title="选择日期"
      @confirm="handleConfirm('end')"
      @cancel="handleCancel('end')"
      :min-date="minEndDate"
      :max-date="maxEndDate"
    />
  </van-popup>
</template>

<script setup lang="ts">
import { computed, ref, watch, watchEffect } from "vue";
import moment from "moment";
type JDatePicker = {
  value: null | undefined | [number, number];
};
const emits = defineEmits<{
  (e: "update:value", value: [number, number]): void;
}>();
const props = withDefaults(defineProps<JDatePicker>(), {
  value: null,
});
const startShow = ref(false);
const endShow = ref(false);
const startDate = ref([]);
const endDate = ref([]);
const minEndDate = ref<Date>(new Date(2020, 0, 1));
const maxEndDate = ref<Date>(new Date(2025, 5, 1));

// 当开始日期变化时更新结束日期限制范围
watchEffect(() => {
  if (startDate.value.length) {
    const date = moment(startDate.value.join("-"));
    minEndDate.value = date.toDate();
    maxEndDate.value = date.add(31, "days").toDate();
  } else {
    // 恢复默认值（可选）
    minEndDate.value = new Date(2020, 0, 1);
    maxEndDate.value = new Date(2025, 5, 1);
  }
});
const dateValue = computed({
  get() {
    if (!Array.isArray(props.value) || props.value.length !== 2) {
      return [null, null];
    }

    return props.value.map(validateTimestamp) as [number | null, number | null];
  },
  set(value) {
    console.log("val", value);

    emits("update:value", value);
  },
});
const handleOpenPopup = (type: "start" | "end") => {
  type === "start" ? (startShow.value = true) : (endShow.value = true);
};
const handleConfirm = (type: "start" | "end") => {
  const [currentStart, currentEnd] = dateValue.value;
  if (type === "start") {
    const newStart = moment(startDate.value.join("-")).valueOf();
    dateValue.value = [newStart, currentEnd];
    startShow.value = false;
  } else {
    const newEnd = moment(endDate.value.join("-")).valueOf();
    dateValue.value = [currentStart, newEnd];
    endShow.value = false;
  }
};
const handleCancel = (type: "start" | "end") => {
  if (type === "start") {
    startDate.value = [];
    startShow.value = false;
  } else {
    endDate.value = [];
    endShow.value = false;
  }
};
const handleOpen = () => {
  console.log(dateValue.value);

  startDate.value = dateValue.value[0] ? moment(dateValue.value[0]).format("YYYY-MM-DD").split("-") : moment().format("YYYY-MM-DD").split("-");
  endDate.value = dateValue.value[1] ? moment(dateValue.value[1]).format("YYYY-MM-DD").split("-") : [];
};
// 单个时间戳校验
function validateTimestamp(value: any): number | null {
  if (typeof value === "string") value = Number(value);
  if (!Number.isInteger(value)) return null;
  if (value < 0 || value >= 1e13) return null;
  return moment(value).isValid() ? value : null;
}
</script>
<style lang="less" scoped>
.j-date-picker {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  background: #f8f8f8;
  border-radius: 4px;
  .start-time-picker,
  .end-time-picker {
    text-align: center;
    width: 45%;
    .placeholder {
      color: #999;
    }
  }
  .separator {
    text-align: center;
    width: 10%;
  }
}
</style>
