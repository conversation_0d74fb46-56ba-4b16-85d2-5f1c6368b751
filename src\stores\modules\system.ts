import { defineStore } from 'pinia'
import { stores } from '@/stores'
import {
  StoreNamesEnum,
  StoreQualityEnum,
  StorePayMode,
  StoreFyPayMode,
  StoreTypeEnum,
} from '@/enum'
import { SystemInterface } from './type'

type ObjToKeyValArray<T> = {
  [K in keyof T]: [K, T[K]]
}[keyof T]

export const systemStore = defineStore(StoreNamesEnum.System, {
  state: (): SystemInterface => {
    return {
      /** 版本号 */
      version: '1.0.4',
      /** 是否限制 */
      isLimit: true,
      /** 是否显示积分商城 */
      isShowIntegralStore: true,
      /** 商城性质 */
      storeQuality: StoreQualityEnum.Public,
      /** 导航配置 */
      navigationConfigList: [],
      /** 支付方式 */
      payMode: StorePayMode.WeChatPay,
      /** 富友支付方式 */
      fyPayMode: StoreFyPayMode.CurrentApp,
      /** 商城类型 */
      storeType: StoreTypeEnum.Medicine,
    }
  },
  getters: {
    getIsShowIntegralStore: (state) => {
      return state.isShowIntegralStore
    },
    /** 是否为医药商城 */
    isPharmacyMall: (state): boolean => {
      return state.storeType === StoreTypeEnum.Medicine && state.isLimit
    },
  },
  actions: {
    setLimit(limit: boolean) {
      this.isLimit = limit
    },
    setVersion(version: string) {
      this.version = version
    },
    setIsShowIntegralStore(value: boolean) {
      this.isShowIntegralStore = value
    },
    setStoreQuality(quality: StoreQualityEnum) {
      this.storeQuality = quality
    },
    setNavigationConfigList(config) {
      this.navigationConfigList = config
    },
    setPayMode(type) {
      this.payMode = type
    },
    setFyPayMode(type) {
      this.fyPayMode = type
    },
    setStoreType(type) {
      this.storeType = type
    },
    /** Set GlobalState */
    setGlobalState(...args: ObjToKeyValArray<SystemInterface>) {
      this.$patch({ [args[0]]: args[1] })
    },
  },
})

export function useSystemStoreWithoutSetup() {
  return systemStore(stores)
}
