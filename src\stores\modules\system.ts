import { defineStore } from "pinia";
import { StoreName } from "@/enums/stores";
import { stores } from "@/stores";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";

export const useSystemStore = defineStore(StoreName.System, {
  state: () => {
    return {
      _imgPrefix: "",
      entryUrl:'',
      _storeUrl:'',
      _cacheOrderCode:''
    };
  },
  getters: {
    imgPrefix: state => {
      if (!state._imgPrefix) {
        try {
          const userSystemStorage = createCacheStorage(CacheConfig.System);
          const _systemCache = userSystemStorage.get();
          state._imgPrefix = _systemCache.imgPrefix;
        } catch (e) {
          console.error(e);
        }
      }
      return state._imgPrefix;
    },
    storeUrl: state => {
      // return "http://192.168.0.220:8858"
      if (!state._storeUrl) {
        try {
          const userSystemStorage = createCacheStorage(CacheConfig.System);
          const _systemCache = userSystemStorage.get();
          state._storeUrl = _systemCache.storeUrl;
        } catch (e) {
          console.error(e);
        }
      }
      return state._storeUrl;
    },
    cacheOrderCode: state => {
      // return "http://192.168.0.220:8858"
      if (!state._cacheOrderCode) {
        try {
          const userSystemStorage = createCacheStorage(CacheConfig.System);
          const _systemCache = userSystemStorage.get();
          state._cacheOrderCode = _systemCache.cacheOrderCode;
        } catch (e) {
          console.error(e);
        }
      }
      return state._cacheOrderCode;
    },
  },
  actions: {
    setImgPrefix(imgPrefix: string) {
      const userSystemStorage = createCacheStorage(CacheConfig.System);
      let _systemCache = userSystemStorage.get();
      if (_systemCache) _systemCache.imgPrefix = imgPrefix;
      else _systemCache = { imgPrefix };
      userSystemStorage.set(_systemCache);
      this._imgPrefix = imgPrefix;
    },
    setStoreUrl(storeUrl: string) {
      const userSystemStorage = createCacheStorage(CacheConfig.System);
      let _systemCache = userSystemStorage.get();
      if (_systemCache) _systemCache.storeUrl = storeUrl;
      else _systemCache = { storeUrl };
      userSystemStorage.set(_systemCache);
      this._storeUrl = storeUrl;
    },
    setCacheOrderCode(cacheOrderCode: string) {
      const userSystemStorage = createCacheStorage(CacheConfig.System);
      let _systemCache = userSystemStorage.get();
      if (_systemCache) _systemCache.cacheOrderCode = cacheOrderCode;
      else _systemCache = { cacheOrderCode };
      userSystemStorage.set(_systemCache);
      this._cacheOrderCode = cacheOrderCode;
    },
    setEntryUrl(url:string){
      this.entryUrl = url
    }
  },
});

export function useSystemStoreWithoutSetup() {
  return useSystemStore(stores);
}
