<template>
    <van-tabbar v-show='isShowTabRef' v-model="activeMenuKeyRef" @change="onTabChange" active-color="#EF1115">
        <template v-for='menu in menuListRef' :key="menu.id">
            <van-tabbar-item v-if='menu.show' :name="menu.id">
                <span>{{ menu.title }}</span>
                <template #icon="props">
                    <img :src="props.active ? menu.icon.active : menu.icon.inactive" />
                </template>
            </van-tabbar-item>
        </template>
    </van-tabbar>
</template>
<script setup lang="ts">
import { RouterView, useRouter } from 'vue-router';
import { useMenu } from '@/hooks/useMenu';
import { routesMap } from '@/router/maps';
import { computed,ref,watch } from 'vue';
import { useActiveRoute } from "@/hooks/useActiveRoute";
const router = useRouter();
const { activeMenuKeyRef } = useMenu();
const menuListRef = ref([
    {"id":"jw-root","path":"jw-root","title":"","show":false,"level":1,"icon":{"active":"","inactive":""}},
    {"id":"jw-root-store-home","path":"jw-root-store-home","title":"首页","show":true,"level":1,"icon":{"active":"/icons/storeTabbar/home-active.png","inactive":"/icons/storeTabbar/home.png"}},
    {"id":"jw-root-store-category","path":"jw-root-store-category","title":"分类","show":true,"level":2,"icon":{"active":"/icons/storeTabbar/cate-active.png","inactive":"/icons/storeTabbar/cate.png"}},
    {"id":"jw-root-store-mine","path":"jw-root-store-mine","title":"我的","show":true,"level":3,"icon":{"active":"/icons/storeTabbar/mine-active.png","inactive":"/icons/storeTabbar/mine.png"}}
])
const { activeRouteName } = useActiveRoute();
function onTabChange(routeName: string) {
    router.push(routesMap[routeName])
}
const isShowTabRef = computed<boolean>(() => {
    const flag = menuListRef.value.filter(menu => menu.show).map(menu => menu.id).includes(activeRouteName.value as string)
    return flag
})
menuListRef.value.sort((a, b) => a.level - b.level)
</script>
<style scoped lang="less">
@import "@/styles/defaultVar.less";
.main-layout-wrapper {
    width: 100vw;
}

.main-content-bg {
    background-color: @blank-background-color;
    width: 100%;
    overflow: auto;
    box-sizing: border-box;

    &.hasTab {
        height: calc(100% - var(--van-tabbar-height));
    }

    &.noTab {
        height: 100%;
    }
}
</style>