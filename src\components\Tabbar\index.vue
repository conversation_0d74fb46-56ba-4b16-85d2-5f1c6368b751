<template>
  <view v-show="!isHiddenTabbar" :class="['tab-bar', { 'tabbar-hidden': isHiddenTabbar }]">
    <view
      v-for="tab in navigationListRef"
      :key="tab.pagePath"
      @click="() => switchTab(tab.pagePath)"
      class="tab-bar-item"
    >
      <image
        :src="selectedTabKeyRef === tab.pagePath ? tab.selectedIconPath : tab.iconPath"
      ></image>
      <view
        class="title"
        :style="`color: ${selectedTabKeyRef === tab.pagePath ? selectedColor : defaultColor}`"
      >
        {{ tab.text }}
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { useTabbar } from './hooks/useTabbar'

defineOptions({ name: 'Tabbar' })

/** 选中字体 */
const selectedColor = '#EF1115'
/** 默认字体 */
const defaultColor = '#666666;'

/** useTabbar */
const { isHiddenTabbar, navigationListRef, selectedTabKeyRef, loadNavigationConfig } = useTabbar()

/** 切换tabbar */
function switchTab(url: string) {
  wx.switchTab({
    url: `/${url}`,
  })
}

/** 组件挂载 */
onMounted(() => {
  loadNavigationConfig()
})
</script>

<style lang="scss" scoped>
@import '@/style/tabbar.scss';
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: $tabbar-height;
  background: white;
  display: flex;
  flex-direction: row;
  pointer-events: auto;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10;
  &.tabbar-hidden {
    height: 0;
    display: none;
  }
  .tab-bar-item {
    flex: 1;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4rpx;

    image {
      width: 48rpx;
      height: 48rpx;
    }

    .title {
      font-family: 'Source Han Sans CN', sans-serif;
      font-weight: 500;
      font-size: 28rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
