<template>
  <VanPopup
    :show="props.show"
    @close="emit('update:show', false)"
    :close-on-click-overlay="false"
    :style="{ borderRadius: '16px', background: 'transparent' }"
    :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)' }"
  >
    <div class="wrapper">
      <div class="qrCode-container">
        <div class="qrCode_title">会员邀请码</div>
        <VanImage width="156" height="156" fit="contain" :src="props.qrCodeUrl" />
        <div class="title-wrapper">
          <span class="title">长按图片保存</span>
        </div>
      </div>
      <!-- 关闭 -->
      <img :src="closeBtn" alt="" class="colse" @click="emit('update:show', false)" />
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed } from "vue";
/** 静态资源 */
import closeBtn from "@/assets/storeImage/storeHome/closeBtn.png"

defineOptions({ name: 'StoreMemberCode' });

/** props */
const props = defineProps<{
    show: boolean;
    qrCodeUrl?: string;
}>();

/** emit */
const emit = defineEmits<{
    (e: 'update:show', show: boolean): void;
}>();
</script>

<style lang="less" scoped>
.wrapper {
    box-sizing: border-box;
    position: relative;
    margin-bottom: 60px;
    .qrCode-container {
        width: 236px;
        height: 287px;
        background: #FFFFFF;
        border-radius: 16px;
        padding: 24px 40px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        .qrCode_title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 20px;
            color: #333333;
            line-height: 20px;
            text-align: center;
            font-style: normal;
            text-transform: none;
            margin-bottom: 18px;
        }
        .title-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 16px;
            .title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 16px;
                color: #999999;
                line-height: 20px;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
        }
    }

    .colse {
        position: absolute;
        bottom: -42px;
        left: 50%;
        transform: translate(-50%, 0);
        width: 32px;
        height: 32px;
        cursor: pointer;
    }
}
</style>
