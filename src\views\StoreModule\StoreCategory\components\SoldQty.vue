<template>
    <span class="soldQty">{{ qtySufix }}</span>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { genSaleCount, saleComputedSum } from "@/utils/storeUtils";
const props = withDefaults(defineProps<{
    cardInfo: any,
    type: StoreGoodsEnum,
}>(), {
    cardInfo: () => ({}),
    type: StoreGoodsEnum.Goods
})
const skuList = computed<any[]>(() => {
    let list = []
    if (props.type == StoreGoodsEnum.Goods) {
        list = props.cardInfo?.appletProductSpecDTOList || [];
    }
    if (props.type == StoreGoodsEnum.IntegralGoods) {
        list = props.cardInfo?.appletPointSpecDTOS || []
    }
    if (props.type == StoreGoodsEnum.WelfareTicket) {
        list = props.cardInfo?.couponProductSpecList || []
    }
    return list
});
const qtySufix = computed(() => {
    const _text = props.type == StoreGoodsEnum.Goods ? '已售' : '已兑换'
    const sumCount = genSaleCount(skuList.value)
    return _text + saleComputedSum(sumCount)
})
</script>

<style scoped lang="less">
.soldQty {
    font-size: 14px;
    color: #999999;
}
</style>