<template>
  <div class="address_card_wrapper">
    <div class="address_card_header">
      <div class="address_card_content">
        <span class="name">{{ addressInfoRef.name }}</span>
        <span class="mobile">{{ addressInfoRef.mobile }}</span>
      </div>
      <div class="edit-wrapper" @click.stop="handleEditAddress">
        <img :src="editIcon" alt="" class="edit_icon" />
      </div>
    </div>
    <!-- 地址 -->
    <p class="address">{{ addressDetail }}</p>
    <!-- 线 -->
    <div class="line"></div>
    <div class="operation" @click.stop="">
      <VanRadioGroup v-model="addressInfoRef.isDefault" @click="handleSetDefaultAddress">
        <VanRadio icon-size="18px" :name="1" shape="dot">
          <span class="title">默认地址</span>
        </VanRadio>
      </VanRadioGroup>
      <div class="delete_icon_wrapper" @click="handleDeleteAddress">
        <img :src="deleteIcon" alt="" class="delete_icon" />
        <span class="title">删除</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from 'vue';
/** 相关静态资源 */
import editIcon from '@/assets/storeImage/storeMine/edit.png';
import deleteIcon from '@/assets/storeImage/storeMine/delete.png';

defineOptions({
  name: 'AddressCard'
});

const props = defineProps<{
  addressInfo: {
    id?: string;
    name?: string;
    mobile?: string;
    address?: string;
    province?: string;
    provinceId?: string | number;
    cityName?: string;
    cityId?: string | number;
    area?: string;
    areaId?: string | number;
    town?: string;
    townId?: string | number;
    isDefault?: 0 | 1;
  };
}>();

/** emit */
const emit = defineEmits<{
  (e: 'deleteAddress', id: string): void;
  (e: 'editAddress', id: string): void;
  (e: 'setDefaultAddress', id: string): void;
}>();

const { addressInfo: addressInfoRef } = toRefs(props);

const addressDetail = computed(() => {
  return `${addressInfoRef.value.province + addressInfoRef.value.cityName + addressInfoRef.value.area + addressInfoRef.value.town + addressInfoRef.value.address}`;
});

function handleDeleteAddress() {
  emit('deleteAddress', addressInfoRef.value?.id);
}

function handleEditAddress() {
  emit('editAddress', addressInfoRef.value?.id);
}

function handleSetDefaultAddress() {
  emit('setDefaultAddress', addressInfoRef.value?.id);
}
</script>

<style lang="less" scoped>
.address_card_wrapper {
    width: 100%;
    padding: 12px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 12px;
    .address_card_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .address_card_content {
            display: flex;
            align-items: center;
            gap: 8px;
            .name,
            .mobile {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 14px;
                color: #333333;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
        .edit-wrapper {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          .edit_icon {
            width: 16px;
            height: 16px;
          }
        }
    }
    .address {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .line {
        width: 100%;
        height: 1px;
        background: #f0f0f0;
    }
    .operation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .delete_icon_wrapper {
            display: flex;
            align-items: center;
            .delete_icon {
                width: 16px;
                height: 16px;
            }
        }
    }
}
</style>
