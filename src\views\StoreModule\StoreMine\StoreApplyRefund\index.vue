<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <div class="store_apply_refund_wrapper">
      <div class="store_apply_refund_container">
        <!-- 售后类型 -->
        <BannerContainer style="background: #FFFFFF;padding: 12px 12px 0px 12px;margin-bottom: 8px;">
          <template #title>
            <span class="title">售后类型</span>
          </template>
          <div class="after_sales_type_container">
            <div
              class="after_sales_type_item"
              v-for="item in afterSaleTypeList"
              :key="item.value"
              @click="handleAfterSaleTypeClick(item.value)"
            >
              <div class="after_sales_type_item_left">
                <span class="after_sales_title">{{ item.title }}</span>
                <span class="sub_title">{{ item.subTitle }}</span>
              </div>
              <div class="after_sales_type_item_right">
                <VanRadioGroup v-model="model.type" shape="dot">
                  <VanRadio :name="item.value" icon-size="18px" />
                </VanRadioGroup>
              </div>
            </div>
          </div>
        </BannerContainer>
        <!-- 退款金额 -->
        <BannerContainer style="background: #FFFFFF;margin-bottom: 8px;">
          <template #title>
            <div class="refund_amount_container">
              <span class="refund_amount_title">退款金额</span>
              <div class="refund_amount_content">
                <span class="refund_amount" style="font-size: 10px;">￥</span>
                <span class="refund_amount">{{ model.refundAmount?.toFixed(2) }}</span>
              </div>
            </div>
          </template>
          <span class="refund_amount_subTitle">金额不可修改</span>
        </BannerContainer>
        <!-- 联系电话 -->
        <BannerContainer style="background: #FFFFFF;margin-bottom: 8px;">
          <template #title>
            <div class="phone_container">
              <span class="phone_title">联系电话</span>
              <VanField
                :border="false"
                v-model="model.phone"
                input-align="right"
                style="flex: 1;padding: 0;"
                class="phone"
                placeholder="请填写联系电话"
              />
            </div>
          </template>
        </BannerContainer>
        <!-- 退款原因 -->
        <BannerContainer style="background: #FFFFFF;margin-bottom: 8px;">
          <template #title>
            <div class="refund_reason_container" style="margin-bottom: 12px;">
              <span class="refund_reason_title">退款原因</span>
              <div class="refund_reason_content" @click="showRefundReason = true">
                <span class="refund_reason_content_text">{{ model.causeName ?? '请选择' }}</span>
                <VanIcon name="arrow" />
              </div>
            </div>
          </template>
          <VanField
            :border="false"
            type="textarea"
            v-model="model.reasonDescription"
            :maxlength="200"
            :autosize="{ minHeight: 50 }"
            :show-word-limit="true"
            placeholder="请描述具体原因"
            class="refund_reason_textarea"
          />
        </BannerContainer>
        <!-- 上传图片 -->
        <BannerContainer style="background: #FFFFFF;margin-bottom: 8px;">
          <template #title>
            <div class="upload_pictures_container" style="margin-bottom: 12px;">
              <span class="upload_pictures_title">上传图片</span>
              <div class="upload_pictures_content">
                <span class="upload_pictures_content_text">
                  {{`最多可上传${ model.afterSaleImgVOList.length }/9张`}}
                </span>
              </div>
            </div>
          </template>
          <VanUploader
            v-model="tempAfterSaleImgVOList"
            :after-read="handleAfterRead"
            @delete="handleDeleteUploader"
            :max-count="9"
            multiple
          />
        </BannerContainer>
      </div>
      <!-- 提交 -->
      <div class="footer">
        <VanButton type="danger" @click="handleApplyRefund" round block style="width: 100%;height: 36px;">
          提 交
        </VanButton>
      </div>
      <!-- 退款原因 -->
      <VanActionSheet v-model:show="showRefundReason" title="选择退款原因" :closeable="false">
        <div class="refund_reason_list">
          <div
            class="refund_reason_item"
            v-for="item in refundReasonList"
            :key="item.value"
            @click="onSelectRefundReason(item.value, item.label)"
          >
            <span :class="{ 'active_title': model.reason == item.value }">{{ item.label }}</span>
          </div>
        </div>
      </VanActionSheet>
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { showToast } from 'vant';
import { useMessages } from "@/hooks/useMessage";
import useGetOrderDetail from "../hooks/useGetOrderDetail";
import useApplyRefund from "./hooks/useApplyRefund";
import { StoreAfterSaleTypeEnum, AfterSaleReasonEnum } from "@/views/StoreModule/enums";
import { applyRefundApi } from "@/services/storeApi";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import BannerContainer from "@/views/StoreModule/components/BannerContainer.vue";

defineOptions({ name: 'StoreApplyRefund' });

/** props */
const props = defineProps<{
  orderCode: string;
}>();

const router = useRouter();
const { createMessageError, createMessageSuccess } = useMessages();

/** 订单详情 */
const { isPageLoadingRef, getOrderDetail, orderDetailRef } = useGetOrderDetail({
  orderCode: props.orderCode
});

const { model, tempAfterSaleImgVOList, showRefundReason, refundReasonList, handleAfterRead, handleDeleteUploader } = useApplyRefund();

/** 售后类型 */
const afterSaleTypeList = [
  {
    title: "仅退款",
    subTitle: "没收到货或与商家协商不用退货",
    value: StoreAfterSaleTypeEnum.REFUND
  },
  {
    title: "退货退款",
    subTitle: "已收到货，需要退货给商家才能退款",
    value: StoreAfterSaleTypeEnum.REFUND_RETURN
  }
];

/** 点击售后类型 */
function handleAfterSaleTypeClick(type: StoreAfterSaleTypeEnum) {
  model.value.type = type;
}

/** 选择退款原因 */
function onSelectRefundReason(reason: AfterSaleReasonEnum, causeName: string) {
  model.value.reason = reason;
  model.value.causeName = causeName;
  showRefundReason.value = false;
}

/** 获取提交退款参数 */
function _getRefundParams() {
  const { type, reason, reasonDescription, recordNo, phone, refundAmount, afterSaleImgVOList } = model.value;
  return {
    type,
    reason,
    orderCode: props.orderCode,
    reasonDescription,
    recordNo,
    phone,
    refundAmount: Number(refundAmount) * 100,
    afterSaleImgVOList
  }
}

/** 提交申请退款 */
async function handleApplyRefund() {
  try {
    isPageLoadingRef.value = true;
    await applyRefundApi(_getRefundParams());
    showToast('申请退款成功');
    isPageLoadingRef.value = false;
    router.go(-2);
  } catch (error) {
    createMessageError("申请退款失败: " + error);
    isPageLoadingRef.value = false;
  }
}

/** 组件挂载 */
onMounted(async () => {
  if (!props.orderCode) {
    createMessageError("订单号不能为空");
    return;
  }
  await getOrderDetail();
  model.value.refundAmount = Number(orderDetailRef.value?.onlinePayment) / 100;
});
</script>

<style lang="less" scoped>
.store_apply_refund_wrapper {
  width: 100%;
  height: 100vh;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  .store_apply_refund_container {
    flex: 1;
    height: calc(100% - 36px);
    padding: 12px;
    box-sizing: border-box;
    overflow-y: auto;
    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 6px;
    }
    .after_sales_type_container {
      width: 100%;
      box-sizing: border-box;
      .after_sales_type_item {
          display: flex;
          align-items: center;
          cursor: pointer;
          height: 62px;
          .after_sales_type_item_left {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 4px;
              .after_sales_title {
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 400;
                  font-size: 14px;
                  color: #333333;
                  line-height: 20px;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
              }
              .sub_title {
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 400;
                  font-size: 12px;
                  color: #666666;
                  line-height: 14px;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
              }
          }
          .after_sales_type_item_right {
              width: 24px;
          }
          &:first-child {
              border-bottom: 1px solid #EEEEEE;
          }
      }
    }
    .refund_amount_container,
    .phone_container,
    .refund_reason_container,
    .upload_pictures_container {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .refund_amount_title,
      .phone_title,
      .refund_reason_title,
      .upload_pictures_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .refund_amount_content {
          display: flex;
          align-items: center;
          .refund_amount {
             flex: 1;
             font-family: Source Han Sans CN, Source Han Sans CN;
             font-weight: 500;
             font-size: 16px;
             color: #4DA4FF;
             line-height: 20px;
             text-align: right;
             font-style: normal;
             text-transform: none;
          }
      }
      .phone {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
      }
      .refund_reason_content,
      .upload_pictures_content {
        display: flex;
        align-items: center;
        .refund_reason_content_text,
        .upload_pictures_content_text {
          flex: 1;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
        .refund_reason_arrow {
          width: 8px;
          height: 12px;
        }
      }
    }
    .refund_amount_subTitle {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .refund_reason_textarea {
      background: #F8F8F8;
      border-radius: 4px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  .footer {
    padding: 8px 12px;
    box-sizing: border-box;
    background: #FFFFFF;
    box-sizing: border-box;
    padding-bottom: env(safe-area-inset-bottom);
    :deep(.van-button__text) {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }
  }
}

.refund_reason_list {
    height: 60vh;
    .refund_reason_item {
        width: 100%;
        height: 54px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #E5E6EB;
        box-sizing: border-box;
        &:first-child {
            border-top: 1px solid #E5E6EB;
        }
    }
}
.active_title {
  color: #4DA4FF;
}
</style>
