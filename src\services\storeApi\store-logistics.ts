import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
export const enum storeLogisticsApi {
    page = "/h5/storeLogistics/page",
    logisticsTraces = "/h5/storeLogistics/getLogisticsTraces"

}
export function getStoreLogisticsList(_params) {
    return defHttp.post({
      url: getStoreApiUrl(storeLogisticsApi.page),
      params: _params,
      requestConfig: {
        skipCrypto: true,
      },
    });
}
export function getLogisticTraces(params) {
    return defHttp.get({
        url: getStoreApiUrl(storeLogisticsApi.logisticsTraces),
        params,
        requestConfig: {
            isQueryParams: true,
            skipCrypto: true,
        },
    });
};