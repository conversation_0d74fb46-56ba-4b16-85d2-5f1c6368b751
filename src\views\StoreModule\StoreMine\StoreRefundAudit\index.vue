<template>
  <JLoadingWrapper :show="isPageLoadingRef" class="store_refund_audit_wrapper">
    <StoreSearch type="digit" v-model:value="searchValueRef" placeholder="请输入售后单号" @search="onRefresh" />
    <div class="store_refund_audit">
      <!-- tabs -->
      <VanTabs
        :style="{ '--van-tabs-line-height': '40px', height: '100%' }"
        v-model:active="activeTabRef"
        color="#EF1115"
        title-active-color="#EF1115"
        title-inactive-color="#333333"
        line-width="36px"
        line-height="2px"
        swipeable
      >
        <VanTab v-for="item in refundAuditStatusList" :key="item.value" :name="item.value">
          <template #title>
            <VanPopover
              v-if="item.value == AfterSaleStatusEnum.OTHER"
              v-model:show="showOtherStatusRef"
              placement="bottom-end"
            >
              <template #reference>
                <span class="tab-title" :class="{ 'tab-active': activeTabRef === item.value }">{{ item.label }}</span>
              </template>
              <div class="search-type-list">
                <div
                  v-for="item in otherStatusList"
                  :key="item.value"
                  class="search-type-item"
                  @click="handleClickItem(item)"
                  :class="{ 'active-type': otherStatusRef === item.value }"
                >
                  {{ item.text }}
                </div>
              </div>
            </VanPopover>
            <span v-else class="tab-title" :class="{ 'tab-active': activeTabRef === item.value }">
              {{ item.label }}
            </span>
          </template>
          <VanPullRefresh
            v-model="refreshingRef"
            @refresh="onRefresh"
            @scroll="onScroll"
            :class="`tab-content_${item.value}`"
            class="tab-content"
          >
            <template v-if="refundAuditListRef.length">
              <VanList
                v-model:loading="isLoadingRef"
                :finished="isFinishedRef"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <StoreRefundAuditOrderCard
                  v-for="item in refundAuditListRef"
                  :key="item.id"
                  :orderInfo="item"
                  @click="handleToAfterSaleDetail(item)"
                  @reject="handleReject(item?.recordNo)"
                  @agree="handleAgree(item)"
                  @receiveAndRefund="handleReceiveAndRefund(item?.recordNo)"
                />
              </VanList>
            </template>
            <template v-else>
              <EmptyData style="min-height: 400px;" />
            </template>
          </VanPullRefresh>
        </VanTab>
      </VanTabs>
    </div>
    <!-- 拒绝 -->
    <StoreRefundReject v-model:show="showRejectRef" :recordNo="currentRecordNoRef" @success="onRefresh" />
    <!-- 二次确认 -->
    <JDoubleConfirm v-model:show="showDoubleConfirmRef" :tip="doubleConfirmRef.tip" @confirm="handleConfirm" />
    <!-- 退货地址确认 -->
    <StoreReturnAddress
      v-model:show="showReturnAddressRef"
      :belongStoreId="currentStoreIdRef"
      :supplierId="currentSupplierIdRef"
      @confirm="handleRefundAndReturnConfirm"
    />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onActivated, nextTick } from "vue";
import { showToast } from 'vant';
import useGetRefundAudit from "./hooks/useGetRefundAudit";
import { useBoolean } from "@/views/StoreModule/hooks";
import { useMessages } from "@/hooks/useMessage";
import {
  KeepAliveRouteNameEnum,
  StoreRoleOperationEnum,
  StoreAfterSaleTypeEnum,
  StoreAfterSaleDetailRouteTypeEnum,
  AfterSaleStatusEnum
} from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { RoutesName } from "@/enums/routes";
import { executeAfterSaleActionByAdminApi } from "@/services/storeApi";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreSearch from "@/views/StoreModule/components/StoreSearch.vue";
import StoreRefundAuditOrderCard from "./components/StoreRefundAuditOrderCard.vue";
import StoreRefundReject from "./components/StoreRefundReject.vue";
import JDoubleConfirm from "@/views/StoreModule/components/JDoubleConfirm.vue";
import StoreReturnAddress from "../components/StoreReturnAddress.vue";

defineOptions({ name: KeepAliveRouteNameEnum.REFUND_AUDIT });

const { createMessageSuccess, createMessageError } = useMessages();
const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom } = useKeepAliveRoute();
const { routerPushByRouteName } = useRouterUtils();
const {
  isPageLoadingRef,
  refundAuditListRef,
  activeTabRef,
  refreshingRef,
  refundAuditStatusList,
  searchValueRef,
  isLoadingRef,
  isFinishedRef,
  otherStatusList,
  otherStatusRef,
  onRefresh,
  onLoad,
  initStoreRefundReviewAfterSaleOrderList
} = useGetRefundAudit();

const currentRecordNoRef = ref(null); // 当前记录编号
const currentStoreIdRef = ref(null); // 当前订单归属门店ID
const currentSupplierIdRef = ref(undefined); // 当前订单归属供应商ID

/** 退货退款，退货地址确认 */
const { bool: showReturnAddressRef, setFalse: hideReturnAddress, setTrue: showReturnAddress } = useBoolean(false);

/** 是否显示其他状态选项 */
const { bool: showOtherStatusRef, setFalse: hideOtherStatus, setTrue: showOtherStatus } = useBoolean(false);

/** 二次确认 start */
type DoubleConfirmType = "agreeToRefundAndReturn" | "agreeToRefund" | "receiveAndRefund";
const { bool: showDoubleConfirmRef, setTrue: showDoubleConfirm, setFalse: hideDoubleConfirm } = useBoolean(false);
const doubleConfirmRef = ref<{
  type: DoubleConfirmType;
  tip: string;
}>({
  type: 'agreeToRefundAndReturn',
  tip: '是否同意退货退款？',
});
async function handleConfirm() {
  isPageLoadingRef.value = true;
  const { type } = doubleConfirmRef.value;
  try {
    // 同意退货退款
    if (type == 'agreeToRefundAndReturn') {
      showReturnAddress();
      return;
    }

    // 同意退款
    if (type == 'agreeToRefund') {
      let _params = {
        recordNo: currentRecordNoRef.value,
        action: StoreRoleOperationEnum.AGREE_TO_REFUND_ONLINE,
      };
      await executeAfterSaleActionByAdminApi(_params);
      showToast("同意退款成功");
    }

    // 收货并退款
    if (type == 'receiveAndRefund') {
      let _params = {
        recordNo: currentRecordNoRef.value,
        action: StoreRoleOperationEnum.RECEIPT_AND_REFUND_ONLINE,
      };
      await executeAfterSaleActionByAdminApi(_params);
      showToast("收货并退款成功");
    }
    // 刷新
    onRefresh();
  } catch (error) {
    createMessageError(`操作失败：` + error);
  } finally {
    isPageLoadingRef.value = false;
    if (type !== 'agreeToRefundAndReturn') {
      currentRecordNoRef.value = null;
      currentStoreIdRef.value = null;
      currentSupplierIdRef.value = undefined;
    }
  }
}

/** 确认退货退款 */
async function handleRefundAndReturnConfirm() {
  try {
    isPageLoadingRef.value = true;
    let _params = {
      supplierId: currentSupplierIdRef.value,
      recordNo: currentRecordNoRef.value,
      action: StoreRoleOperationEnum.AGREE_TO_REFUND_AND_RETURN,
    };
    await executeAfterSaleActionByAdminApi(_params);
    showToast("同意退货退款成功");
    // 刷新
    onRefresh();
  } catch (error) {
    createMessageError(`操作失败：` + error);
  } finally {
    isPageLoadingRef.value = false;
    currentRecordNoRef.value = null;
    currentStoreIdRef.value = null;
    currentSupplierIdRef.value = undefined;
  }
}
/** 二次确认 end */

/** 同意 start */
function handleAgree(item: {
  recordNo: string;
  belongStoreId: string;
  type: StoreAfterSaleTypeEnum;
  supplierId: string;
}) {
  const { recordNo, type, belongStoreId, supplierId } = item;
  doubleConfirmRef.value = {
    type: type  == StoreAfterSaleTypeEnum.REFUND_RETURN ? 'agreeToRefundAndReturn' : 'agreeToRefund',
    tip: type  == StoreAfterSaleTypeEnum.REFUND_RETURN ? '是否同意退货退款？' : '是否同意退款？',
  };
  showDoubleConfirm();
  currentRecordNoRef.value = recordNo;
  if (supplierId) {
    currentSupplierIdRef.value = supplierId;
  } else {
    currentStoreIdRef.value = belongStoreId;
  }
}
/** 同意 end */

/** 收货并退款 start */
function handleReceiveAndRefund(recordNo: string) {
  doubleConfirmRef.value = {
    type: 'receiveAndRefund',
    tip: '是否确认收货并退款？',
  };
  showDoubleConfirm();
  currentRecordNoRef.value = recordNo;
}
/** 收货并退款 end */

/** 拒绝 start */
const showRejectRef = ref(false);
const handleReject = (recordNo: string) => {
  if (!recordNo) return;
  currentRecordNoRef.value = recordNo;
  showRejectRef.value = true;
};
/** 拒绝 end */

/** 其他状态选择 */
function handleClickItem(item: { text: string; value: AfterSaleStatusEnum }) {
  otherStatusRef.value = item.value;
  hideOtherStatus();

  initStoreRefundReviewAfterSaleOrderList();
}


/** 跳转售后详情 */
function handleToAfterSaleDetail(orderInfo) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.REFUND_AUDIT);
  routerPushByRouteName(RoutesName.StoreAfterSaleDetail, { recordNo: orderInfo?.recordNo, routeType: StoreAfterSaleDetailRouteTypeEnum.REFUND_AUDIT });
}

/** 滚动触发 */
function onScroll(e) {
  scrollEventHandler(e, KeepAliveRouteNameEnum.REFUND_AUDIT);
}

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName(`tab-content_${activeTabRef.value}`)[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.REFUND_AUDIT);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.REFUND_AUDIT);
  });
});
</script>

<style lang="less" scoped>
.store_refund_audit_wrapper {
  width: 100%;
  height: 100vh;
  .store_refund_audit {
    height: calc(100% - 48px);
    :deep(.van-tabs) {
      &__wrap {
        height: 40px;
      }

      &__content {
        height: calc(100% - 40px);
        .van-tab__panel {
          height: 100%;
        }
      }
    }
    :deep(.van-tab) {
      flex: auto;
    }
    :deep(.van-tabs__line) {
      bottom: 18px;
    }
    .tab-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .tab-active {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      color: #EF1115;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .tab-content {
      height: 100%;
      padding: 12px 10px;
      box-sizing: border-box;
      overflow-y: auto;
    }
  }
}
.search-type-list {
  background: #FFFFFF;
  box-sizing: border-box;
  .search-type-item {
    height: 36px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 20px;
    text-align: right;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #EEEEEE;
    box-sizing: border-box;
    padding: 0px 12px;
  }
}
.active-type {
  color: #EF1115 !important;
}

:deep(.van-popup .van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
:deep(.van-popup .van-dialog__confirm) {
  color: #EF1115;
}
</style>
