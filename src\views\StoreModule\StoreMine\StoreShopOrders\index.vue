<template>
  <JLoadingWrapper :show="isPageLoadingRef" class="store_shop_orders_wrapper">
    <!-- 门店店长、店员操作 -->
    <StoreSelectSearch
      v-model:value="searchValueRef"
      v-model:searchTypeValue="searchTypeRef"
      :searchTypeOptions="searchTypeOptions"
      @search="initStoreShopOrdersList"
    />
    <div class="store_shop_order">
      <!-- tabs -->
      <VanTabs
        :style="{ '--van-tabs-line-height': '40px', height: '100%' }"
        v-model:active="activeTabRef"
        color="#EF1115"
        title-active-color="#EF1115"
        title-inactive-color="#333333"
        line-width="36px"
        line-height="2px"
        swipeable
      >
        <VanTab v-for="item in orderStatusList" :key="item.value" :name="item.value">
          <template #title>
            <span class="tab-title" :class="{ 'tab-active': activeTabRef === item.value }">{{ item.label }}</span>
          </template>
          <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" @scroll="onScroll" :class="`tab-content_${item.value}`" class="tab-content">
            <template v-if="storeShopOrderList.length">
              <VanList
                v-model:loading="isLoadingRef"
                :finished="isFinishedRef"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <template v-if="[OrderStatusEnum.REFUND].includes(activeTabRef)">
                  <StoreAfterSalesOrderCard
                    v-for="item in storeShopOrderList"
                    :key="item.id"
                    :orderInfo="item"
                    @click="handleToAfterSaleDetail(item)"
                  />
                </template>
                <template v-else>
                  <StoreShopOrdersCard
                    v-for="item in storeShopOrderList"
                    :key="item.id"
                    :orderInfo="item"
                    @click="handleToOrderDetail(item)"
                    @writeOffOrder="handleWriteOffOrder(item)"
                  />
                </template>
              </VanList>
            </template>
            <template v-else>
              <EmptyData style="min-height: 400px;" />
            </template>
          </VanPullRefresh>
        </VanTab>
      </VanTabs>
    </div>
    <!-- 核销订单 -->
    <WriteOffOrder v-model:show="isShowWriteOffOrderRef" @confirm="handleConfirmWriteOffOrder" />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onActivated, nextTick } from "vue";
import useGetStoreShopOrders from "./hooks/useGetStoreShopOrders";
import { useMessages } from "@/hooks/useMessage";
import { OrderStatusEnum, KeepAliveRouteNameEnum, StoreOrderDetailRouteTypeEnum, StoreAfterSaleDetailRouteTypeEnum } from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { RoutesName } from "@/enums/routes";
import { verifyOrder } from "@/services/storeApi";
/**  相关组件 */
import StoreSelectSearch from "@/views/StoreModule/components/StoreSelectSearch.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import WriteOffOrder from "@/views/StoreModule/components/WriteOffOrder.vue";
import StoreShopOrdersCard from "./components/StoreShopOrdersCard.vue";
import StoreAfterSalesOrderCard from "./components/StoreAfterSalesOrderCard.vue";

defineOptions({ name: KeepAliveRouteNameEnum.STORE_ORDER });

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom } = useKeepAliveRoute();
const {
  isPageLoadingRef,
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  storeShopOrderList,
  searchValueRef,
  searchTypeRef,
  searchTypeOptions,
  activeTabRef,
  orderStatusList,
  onRefresh,
  onLoad,
  initStoreShopOrdersList
} = useGetStoreShopOrders();
const { routerPushByRouteName } = useRouterUtils();
const { createMessageError, createMessageSuccess } = useMessages();
const isShowWriteOffOrderRef = ref(false);
/** 当前核销订单 */
const currentVerifyOrderInfoRef = ref({
  code: null,
  verifyCode: null
});

function handleWriteOffOrder(orderInfo) {
  isShowWriteOffOrderRef.value = true;
  currentVerifyOrderInfoRef.value = {
    code: orderInfo?.code ?? "",
    verifyCode: orderInfo?.orderVerificationDTO?.code ?? "",
  };
}

/** 核销订单 */
async function handleConfirmWriteOffOrder() {
  try {
    const { code, verifyCode } = currentVerifyOrderInfoRef.value;
    const _params = {
      code, 
      verifyCode
    };
    const resp = await verifyOrder(_params);
    if (resp) {
      createMessageSuccess("核销成功");
      currentVerifyOrderInfoRef.value = {
        code: null,
        verifyCode: null
      };
      onRefresh();
    }
  } catch (error) {
    createMessageError("核销失败：" + error);
    currentVerifyOrderInfoRef.value = {
      code: null,
      verifyCode: null
    };
  }
}

/** 跳转订单详情 */
function handleToOrderDetail(orderInfo) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_ORDER);
  routerPushByRouteName(RoutesName.StoreOrderDetail, { 
    routeType: StoreOrderDetailRouteTypeEnum.SCAN,
    orderCode: orderInfo?.code ?? "",
  });
}

/** 跳转售后详情 */
function handleToAfterSaleDetail(orderInfo) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_ORDER);
  routerPushByRouteName(RoutesName.StoreAfterSaleDetail, { recordNo: orderInfo?.recordNo, routeType: StoreAfterSaleDetailRouteTypeEnum.MY_AFTER_SALE });
}

/** 滚动触发 */
function onScroll(e) {
  scrollEventHandler(e, KeepAliveRouteNameEnum.STORE_ORDER);
}

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName(`tab-content_${activeTabRef.value}`)[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.STORE_ORDER);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.STORE_ORDER);
  });
});
</script>

<style lang="less" scoped>
.store_shop_orders_wrapper {
  width: 100%;
  height: 100vh;
  .store_shop_order {
    height: calc(100% - 48px);
    :deep(.van-tabs) {
      &__wrap {
        height: 40px;
      }

      &__content {
        height: calc(100% - 40px);
        .van-tab__panel {
          height: 100%;
        }
      }
    }
    :deep(.van-tab) {
      flex: auto;
    }
    :deep(.van-tabs__line) {
      bottom: 18px;
    }
    .tab-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .tab-active {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      color: #EF1115;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .tab-content {
      height: 100%;
      padding: 12px 10px;
      box-sizing: border-box;
      overflow-y: auto;
    }
  }
}
</style>
