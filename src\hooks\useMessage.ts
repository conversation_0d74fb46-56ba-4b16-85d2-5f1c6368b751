import { isFunction, isNullOrUnDef, isString } from "@/utils/isUtils";
import { showNotify,closeNotify  } from 'vant';
import { onBeforeUnmount } from "vue";
import { emitEvent, EventsBusKeys } from "@/utils/eventsbusUtils";
import { ComponentInstance } from "vant/lib/utils";


type MessageType = "success" | "primary" | "danger" | "warning";
type MessageProps = {
  content: string;
  duration?: number;
  jIndependence?: boolean;
  onAfterLeave?: () => void;
};
type MessageTempItem = {
  content: string;
  duration: number;
  type: MessageType;
  onAfterLeave: () => void;
};
let allMsgData: Array<MessageTempItem> = [];
let msgReactive: ComponentInstance | null = null;

function _create() {
  if (!allMsgData.length) {
    return null;
  } else {
    const item = allMsgData[0];
    allMsgData.splice(0, 1);

    return showNotify({
      type:item.type,
      message:item.content,
      duration: item.duration,
      teleport:'body',
      onClose:() => {
        item.onAfterLeave();
        msgReactive = _create();
      }
    })
  }
}

export const useMessages = () => {
  let isIndependence: boolean = false;
  function removeAllMessage() {
    closeNotify();
    msgReactive = null;
    allMsgData = [];
  }
  const createMessage = (props: MessageProps | string, type: MessageType) => {
    const _message: MessageTempItem = {
      content: "",
      duration: 2000,
      type: type,
      onAfterLeave: ()=>{},
    };

    if (isString(props)) _message.content = props;
    else {
      _message.content = props.content;
      if(!isNullOrUnDef(props.duration)) _message.duration = props.duration;
      if(!isNullOrUnDef(props.jIndependence))  isIndependence = props.jIndependence;
      if(isFunction(props.onAfterLeave)) _message.onAfterLeave =  props.onAfterLeave;
    }

    //数量限制
    if (isIndependence) {
      showNotify({
        teleport:'body',
        type:_message.type,
        message:_message.content,
        duration: _message.duration,
        onClose:() => {
          _message.onAfterLeave();
        }
      })
    } else {
      //信息缓存
      allMsgData.push(_message);
      if (!msgReactive) {
        msgReactive = _create();
      }
    }
    onBeforeUnmount(() => {
      removeAllMessage();
    });
  };
  return {
    createMessageSuccess: (props: string | MessageProps) => {
      createMessage(props, "success");
    },
    createMessageInfo: (props: string | MessageProps) => {
      createMessage(props, "primary");
    },
    createMessageError: (props: string | MessageProps) => {
      createMessage(props, "danger");
    },
    createMessageWarning: (props: string | MessageProps) => {
      createMessage(props, "warning");
    },
    createMessageExportSuccess:(msg: string) => {
      const msgProp:MessageProps = {
        content: msg,
        duration:1000,
        onAfterLeave:()=>{
          emitEvent(EventsBusKeys.ExportSuccess)
        }
      }
      createMessage(msgProp,"success");
    },
    destoryMessage: () => {
      removeAllMessage();
    },
  };
};
