import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 公共 */
export const enum storeCommonApiEnum {
  // base64图片文件上传
  uploadBase64Img = "/common/uploadOfBase64",
}

/**
 * @description: base64图片文件上传
 */
export function uploadBase64ImgApi(_params: { fileName: string; base64: string }) {
  return defHttp.post({
    url: getStoreApiUrl(storeCommonApiEnum.uploadBase64Img),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}
