<template>
  <view
    :style="customStyle"
    v-if="show"
    class="flex justify-center items-center text-24rpx text-#969799 p-10rpx"
  >
    <van-loading size="24rpx" v-if="isLoading">{{ statusMap[status] }}</van-loading>
    <view v-else>{{ statusMap[status] }}</view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { StyleValue } from 'vue'

/** props */
const props = withDefaults(
  defineProps<{
    show: boolean
    status: 'loading' | 'noData'
    statusMap: {
      loading: string
      noData: string
    }
    customStyle?: StyleValue
  }>(),
  {
    show: false,
    status: 'loading',
    statusMap: () => ({
      loading: '加载中...',
      noData: '没有更多数据了',
    }),
    customStyle: () => ({}),
  },
)

// 新增计算属性
const isLoading = computed(() => props.status === 'loading')
</script>

<style lang="scss" scoped></style>
