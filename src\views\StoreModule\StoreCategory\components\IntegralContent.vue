<template>
    <div class="intagral-warp" v-bind="$attrs">
        <template v-if="exchangePoints>0">
            <span class="content-num" :style="priceStyle">{{ exchangePoints }}</span>
            <span class="content-span" :style="pricePreStyle">积分</span>
        </template>
        <template v-if="_price>0" >
            <span class="warp-icon" :style="pricePreStyle" v-if="exchangePoints">+</span>
            <span class="price-icon" :style="pricePreStyle">￥</span>
            <span class="price-num" :style="priceStyle">{{ priceStr }}</span>
            <span class="price-icon" :style="pricePreStyle">{{ remainderStr }}元</span>
        </template>
    </div>
</template>

<script setup lang="ts">
import { computed, type StyleValue } from "vue"
import { isNumber } from "@/utils/isUtils";
interface Props {
    //价格字体大小
	numFontSize: number | string,
    customPriceStyle?: StyleValue,
    skuInfo:any,
    //是否分转元
    isCut:boolean,
    // 保留小数位数
    remainder: number,
    points?: number,
    price?: number,
    pricePreFontSize?: number | string,
	customPrePriceStyle?: StyleValue,
}
const props = withDefaults(defineProps<Props>(), {
    skuInfo:()=>({}),
    numFontSize: 20,
    customPriceStyle:'',
    pricePreFontSize:12,
	customPrePriceStyle:'',
    isCut: true,
    points: 0,
    price:0,
	remainder: 2,
})
const _price = computed<number>(() => {
	let price = props.price || props.skuInfo?.exchangePrice || 0
    let result = props.isCut ? price / 100 : price
	return result.toFixed(props.remainder)
})
const priceStr = computed(() => {
    const str = _price.value.toString()
    const index = str.indexOf('.')
    if (index > -1) {
        return str.substring(0, index)
    }
    return str
})
const remainderStr = computed(() => {
    const str = _price.value.toString()
    const index = str.indexOf('.')
    if (index > -1) {
        return '.'+str.substring(index + 1)
    }
    return ''
})
const exchangePoints = computed(() => {
    return props.points || props.skuInfo?.exchangePoints || 0
})
const priceStyle = computed<StyleValue>(() => {
	return [
		{
			fontSize: isNumber(props.numFontSize) ? `${props.numFontSize}px` : props.numFontSize,
		},
		props.customPriceStyle,
	]
})
const pricePreStyle = computed<StyleValue>(() => {
	return [
		{
			fontSize: isNumber(props.pricePreFontSize) ? `${props.pricePreFontSize}px` : props.pricePreFontSize,
		},
		props.customPrePriceStyle,
	]
})
</script>

<style scoped lang="less">
@import url("@/styles/storeVar.less");
.intagral-warp {
    color: @error-color;
    margin-right: 10px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: baseline;

    span {
        display: inline-block;
    }

    .price-num,.content-num {
        word-break: break-all;
        font-size: 20px;
    }
}
</style>