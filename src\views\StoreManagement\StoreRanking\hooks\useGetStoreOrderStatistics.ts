import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import dayjs from "dayjs";
import { DataRangeValue, OrderSortTypeEnum, OrderStatisticsTypeEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { useUserRole } from "@/views/StoreModule/hooks";
import { leaderboardByOrder } from "@/services/storeApi";

export default function useGetStoreOrderStatistics() {
  const scope = effectScope();
  const { createMessageSuccess, createMessageError } = useMessages();
  const { storeId, staffId } = useUserRole();
  enum DataRangeValue {
    THIS_MONTH='THIS_MONTH',
    LAST_MONTH='LAST_MONTH',
    ALL_MONTH='ALL_MONTH'
  }
  /** 时间筛选 */
  const dataRangeList = [
    {
      label: "本月",
      value: DataRangeValue.THIS_MONTH,
    },
    {
      label: "上月",
      value: DataRangeValue.LAST_MONTH,
    },
    {
      label: "总榜",
      value: DataRangeValue.ALL_MONTH,
    },
  ];

  const isPageLoadingRef = ref(false);
  /** 订单数据 */
  const ordersList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);

  /** 筛选数据 */
  const initParams = {
    dataRangeRef: DataRangeValue.THIS_MONTH,
    timeRef: [dayjs().startOf("day").format("YYYY-MM-DD"), dayjs().endOf("day").format("YYYY-MM-DD")] as [
      string,
      string,
    ],
    type: OrderSortTypeEnum.AMOUNT,
    orderType: OrderStatisticsTypeEnum.ALL,
    storeId: '',
    staffShortId: '',
    memberShortId: null,
  };
  const model = ref({ ...initParams });

  /** 分页 */
  const pageVO = reactive({
    size: 30,
    current: 1,
    total: 0,
  });

  /** 获取时间 */
  function getMonthRange(value: DataRangeValue): [string, string] {
    let startDate;
    let endDate;

    switch (value) {
      case DataRangeValue.THIS_MONTH:
        startDate = dayjs().startOf("month");  // 本月第一天
        endDate = dayjs().endOf("month");      // 本月最后一天
        break;
      case DataRangeValue.LAST_MONTH:
        startDate = dayjs().subtract(1, "month").startOf("month");  // 上个月第一天
        endDate = dayjs().subtract(1, "month").endOf("month");      // 上个月最后一天
        break;
      default:
        startDate = dayjs().subtract(30, "year").startOf("year");  // 百年前
        endDate = dayjs().endOf("month");       // 本月最后一天
        break;
    }

    return [startDate.format("YYYY-MM-DD"), endDate.format("YYYY-MM-DD")];
  }

  /**
   * 判断时间区间属于哪个范围
   */
  function classifyTimeRange(start: string, end: string): DataRangeValue | null {
    const presetRanges = [
      DataRangeValue.THIS_MONTH,
      DataRangeValue.LAST_MONTH,
      DataRangeValue.ALL_MONTH,
    ].map(value => ({
      value,
      range: getMonthRange(value),
    }));

    let startTime = dayjs(start).format("YYYY-MM-DD");
    let endTime = dayjs(end).format("YYYY-MM-DD");

    for (const { value, range } of presetRanges) {
      const [presetStart, presetEnd] = range;
      if (startTime === presetStart && endTime === presetEnd) {
        return value;
      }
    }
    return null;
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    const { timeRef, type, orderType, storeId, staffShortId, memberShortId } = model.value;
    const [startTime, endTime] = timeRef;
    return {
      data: {
        startTime: dayjs(startTime).format("YYYY-MM-DD 00:00:00"),
        endTime: dayjs(endTime).format("YYYY-MM-DD 23:59:59"),
        type,
        orderType,
        storeId,
        staffShortId,
        memberShortId,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取订单统计 */
  async function getStoreOrderStatistics(type?:number) {
    const { current, size } = pageVO;

    try {
      const _params = getSearchParams();

      const { records, total } = await leaderboardByOrder(_params);
      current === 1 ? (ordersList.value = records) : ordersList.value.push(...records);
      console.log(ordersList.value);
      const nextCurrent = current + 1;
      const remaining = Number(total) - nextCurrent * size;
      Object.assign(pageVO, {
        current: nextCurrent,
        total: Number(total),
      });

      isFinishedRef.value = remaining <= 0;
    } catch (error) {
      createMessageError(`获取排行榜数据失败：${error}`);
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
    }
  }

  /** 加载分页数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getStoreOrderStatistics();
    }
  }

  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    console.log(1111);
    getStoreOrderStatistics(model.value.type);
  }

  /** 订单统计数据初始化 */
  async function initStoreOrderStatistics() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreOrderStatistics(model.value.type);
    isPageLoadingRef.value = false;
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    /** 监听 */
    watch(
      () => model.value.dataRangeRef,
      newVal => {
        if (newVal) {
          console.log("=====>newVal",newVal);
          model.value.timeRef = getMonthRange(newVal);
        }
      },
      {
        immediate: true,
      },
    );

    /** 监听时间范围变化尝试匹配预设值 */
    watch(
      () => model.value.timeRef,
      newVal => {
        if (newVal) {
          const [start, end] = newVal;
          const matchedPreset = classifyTimeRange(start, end);
          model.value.dataRangeRef = matchedPreset;

          initStoreOrderStatistics();
        }
      },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    model,
    dataRangeList,
    isPageLoadingRef,
    ordersList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onLoad,
    onRefresh,
    getStoreOrderStatistics,
    initStoreOrderStatistics,
  };
}
