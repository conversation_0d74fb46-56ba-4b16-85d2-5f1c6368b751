<template>
  <view class="tabs-wrapper">
    <JLoadingWrapper :show="props.isLoading">
      <!-- #ifdef MP-WEIXIN -->
      <van-tabs
        :style="{ '--tabs-line-height': '80rpx' }"
        :active="internalActive"
        color="#EF1115"
        title-active-color="#EF1115"
        title-inactive-color="#333333"
        line-width="56rpx"
        line-height="4rpx"
        swipeable
        :duration="0.3"
        @change="handleMpChange"
      >
        <van-tab
          v-for="item in tabs"
          :key="item.value"
          :name="item.value"
          :title="item.label"
          title-style="font-family: 'Source Han Sans CN', sans-serif; font-weight: 400; font-size: 28rpx;"
        >
          <slot name="tab" :tab="item" :active="internalActive" />
        </van-tab>
      </van-tabs>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <JTabs
        :style="{ '--van-tabs-line-height': '80rpx', height: '100%' }"
        v-model:active="internalActive"
        color="#EF1115"
        title-active-color="#EF1115"
        title-inactive-color="#333333"
        line-width="72rpx"
        line-height="4rpx"
        swipeable
      >
        <JTab v-for="item in tabs" :key="item.value" :name="item.value">
          <template #title>
            <text class="j-tab-title" :class="{ 'j-tab-active': internalActive === item.value }">
              {{ item.label }}
            </text>
          </template>
          <slot name="tab" :tab="item" :active="internalActive" />
        </JTab>
      </JTabs>
      <!-- #endif -->
    </JLoadingWrapper>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import JLoadingWrapper from '@/components/JLoadingWrapper/index.vue'

defineOptions({ name: 'JCrossPlatformTabs' })

interface TabItem {
  label: string
  value: string | number
}

/** props */
const props = withDefaults(
  defineProps<{
    tabs: TabItem[]
    active: string | number
    isLoading?: boolean
  }>(),
  {
    isLoading: false,
  },
)

/** emit */
const emit = defineEmits<{
  (e: 'update:active', value: string | number): void
  (e: 'change', value: string | number): void
}>()

const internalActive = computed({
  get: () => props.active,
  set: (val) => {
    emit('update:active', val)
    emit('change', val)
  },
})

function handleMpChange(e: any) {
  const value = e?.detail?.name ?? e
  emit('update:active', value)
  emit('change', value)
}
</script>

<style lang="scss">
.tabs-wrapper {
  width: 100%;
  height: 100vh;
  /*  #ifdef MP-WEIXIN */
  :deep(.van-tab) {
    height: 80rpx;
    flex: auto;
  }
  :deep(.van-tabs__line) {
    bottom: 8rpx;
  }
  /* #endif  */

  /* #ifdef H5 */
  :deep(.van-tab) {
    flex: auto;
  }
  :deep(.van-tabs__line) {
    bottom: 36rpx;
  }
  :deep(.van-tabs) {
    .van-tabs__wrap {
      height: 80rpx;
    }

    .van-tabs__content {
      height: calc(100% - 80rpx);
      .van-tab__panel {
        height: 100%;
      }
    }
  }
  /* #endif  */
  /* #ifdef H5 */
  .j-tab-title,
  .j-tab-active {
    font-family: 'Source Han Sans CN', sans-serif;
    font-weight: 400;
    font-size: 28rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .j-tab-title {
    color: #666666;
  }
  .j-tab-active {
    color: #ef1115;
  }
  /*  #endif  */
}
</style>
