import { ref,reactive } from 'vue';
import { getstaffSurveyStatData,getstaffStatDataPage } from "@/services/storeApi";
import {useMessages} from "@/hooks/useMessage"
const {createMessageError,createMessageSuccess} = useMessages()
const isPageLoadingRef = ref(false)
const verifiPageVO = {
    current:1,
    size:30,
    total:1
}
const listData = ref([])
const staffSurveyData = ref([])
export function ShopAssiatantList(params){
    
    /** 数据汇总 */
    const staffSurveyStatData = async()=>{
        let time = {
            dateStart:params.dateStart,
            dateEnd:params.dateEnd
        }
        let timeObj = params.dateStart != "" ? time : {}
        let datas = {
            ...timeObj,
            orderType:params.orderType,
            storeId: params.storeId,
        }
        let param = {
            data:{
                ...datas
            },
        }
        try {
            staffSurveyData.value = []
            let result = await getstaffSurveyStatData(param)
            staffSurveyData.value.push(result)
        } catch (error) {
            createMessageError("获取店员统计概览数据异常")
        }
        
    }
    /** 分页数据 */
    const loadData = async(flag) =>{
        if(flag){
            verifiPageVO.current = 1
            listData.value = []
        }
        let time = {
            dateStart:params.dateStart,
            dateEnd:params.dateEnd
        }
        let timeObj = params.dateStart != "" ? time : {}
        let datas = {
            ...timeObj,
            orderType: params.orderType,
            storeId: params.storeId,
        }
        isPageLoadingRef.value = true
        groupMgrListStatusReactive.isNextPageLoading = true
        let param = {
            data:{
                ...datas
            },
            pageVO: {
                current:verifiPageVO.current,
                size: verifiPageVO.size
            },
        }
        try {
            const {current,size,total,records} = await getstaffStatDataPage(param)
            verifiPageVO.current = Number(current)
            verifiPageVO.size = Number(size)
            verifiPageVO.total = Number(total)
            listData.value.push(...records)
            if(Number(verifiPageVO.current) * Number(verifiPageVO.size) >=  Number(verifiPageVO.total)){
                groupMgrListStatusReactive.isNextPageFinished = true
            }
        } catch (error) {
            createMessageError("获取店员统计数据异常")
        }finally{
            // groupMgrListStatusReactive.isNextPageFinished = true
            groupMgrListStatusReactive.isNextPageLoading = false
            groupMgrListStatusReactive.isPullLoading = false
            isPageLoadingRef.value = false
        }
    }
    const groupMgrListStatusReactive = reactive({
            isPullLoading:false,
            isNextPageLoading:false,
            isNextPageFinished:false
    }) 
    function onGroupMgrListRefresh(){
        groupMgrListStatusReactive.isPullLoading = true
        loadData(true)
    }
    function onGroupMgrListNextPageLoad(){
        if(Number(verifiPageVO.current) * Number(verifiPageVO.size) <  Number(verifiPageVO.total)){
            verifiPageVO.current++
            groupMgrListStatusReactive.isNextPageLoading = true
            loadData(false)
        }
    }
    return {
        staffSurveyStatData,
        staffSurveyData,
        loadData,
        listData,
        verifiPageVO,
        isPageLoadingRef,
        groupMgrListStatusReactive,
        onGroupMgrListNextPageLoad,
        onGroupMgrListRefresh
    }
}