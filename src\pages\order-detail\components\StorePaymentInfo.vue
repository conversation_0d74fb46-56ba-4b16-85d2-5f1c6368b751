<template>
  <view class="store_payment_info">
    <view class="store_payment_info_title">支付信息</view>
    <!-- 商品总额 -->
    <view class="store_payment_info_item">
      <text class="store_payment_info_item_label">商品总额</text>
      <!-- 普通订单 -->
      <text v-if="isNormalProduct" class="store_payment_info_item_value">
        {{ `￥${Number((orderInfoRef?.goodsAmount ?? 0) / 100).toFixed(2)}` }}
      </text>
      <!-- 福利券订单 -->
      <text v-if="isCouponProduct" class="store_payment_info_item_value">
        {{ `${orderInfoRef?.totalCoupons}张${firstOrderItem?.couponName}` }}
      </text>
      <!-- 纯积分订单 -->
      <text v-if="isIntegralProduct && isIntegralOrder" class="store_payment_info_item_value">
        {{ orderInfoRef?.totalPoints || 0 }}积分
      </text>
      <!-- 积分订单 -->
      <text v-if="isIntegralProduct && !isIntegralOrder" class="store_payment_info_item_value">
        {{ orderInfoRef?.totalPoints || 0
        }}{{
          orderInfoRef?.totalPoints
            ? '积分+￥' + Number((orderInfoRef?.goodsAmount ?? 0) / 100).toFixed(2)
            : '积分'
        }}
      </text>
    </view>
    <!-- 运费 -->
    <view class="store_payment_info_item">
      <text class="store_payment_info_item_label">邮费</text>
      <text class="store_payment_info_item_value">
        {{ `￥${Number((orderInfoRef?.shippingFee ?? 0) / 100).toFixed(2)}` }}
      </text>
    </view>
    <!-- 现金券 -->
    <view class="store_payment_info_item" v-if="isNormalProduct">
      <text class="store_payment_info_item_label">现金劵</text>
      <text class="store_payment_info_item_value">
        {{ `-￥${Number((orderInfoRef?.cashCouponAmt ?? 0) / 100).toFixed(2)}` }}
      </text>
    </view>
    <!-- 订单总额 -->
    <view class="store_payment_info_item">
      <text class="store_payment_info_item_label">订单总额</text>
      <text class="store_payment_info_item_value">
        {{ `￥${Number((orderInfoRef?.money ?? 0) / 100).toFixed(2)}` }}
      </text>
    </view>
    <!-- 支付方式 -->
    <view class="store_payment_info_item">
      <text class="store_payment_info_item_label">支付方式</text>
      <text class="store_payment_info_item_value">
        {{ StoreOrderPayTypeMap[orderInfoRef?.payType] }}
      </text>
    </view>
    <!-- 在线支付金额 -->
    <view v-if="isNormalProduct" class="store_payment_info_item">
      <text class="store_payment_info_item_label">在线支付金额</text>
      <text class="store_payment_info_item_value">
        {{ `￥${Number((orderInfoRef?.onlinePayment ?? 0) / 100).toFixed(2)}` }}
      </text>
    </view>
    <!-- 支付礼品券 -->
    <view v-if="isCouponProduct" class="store_payment_info_item">
      <text class="store_payment_info_item_label">支付礼品券</text>
      <text class="store_payment_info_item_value">
        {{ `${orderInfoRef?.totalCoupons}张${firstOrderItem?.couponName}` }}
      </text>
    </view>
    <!-- 支付状态 -->
    <view class="store_payment_info_item">
      <text class="store_payment_info_item_label">支付状态</text>
      <text class="store_payment_info_item_value">
        {{ StoreOrderPayStatusMap[orderInfoRef?.payStatus] }}
      </text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { toRefs, computed } from 'vue'
import {
  StoreOrderTypeEnum,
  StoreOrderStatusEnum,
  StoreOrderPayTypeEnum,
  StoreOrderPayStatusEnum,
} from '@/enum'

defineOptions({ name: 'StorePaymentInfo' })

/** props */
const props = defineProps<{
  orderInfo: {
    type?: StoreOrderTypeEnum
    status?: StoreOrderStatusEnum
    code?: string
    pickupType?: number
    money?: number
    goodsAmount?: number
    shippingFee?: number
    payType?: StoreOrderPayTypeEnum
    payStatus?: StoreOrderPayStatusEnum
    afterSaleState?: number
    onlinePayment?: number
    totalPoints?: number
    totalCoupons?: number
    cashCouponAmt?: number
    orderItemDTOList?: Array<{
      type?: 1 | 2 | 3
      orderId?: string
      productImgPath?: string
      productFrontName?: string
      specName?: string
      price?: number
      count?: number
      exchangePoints?: number
      exchangePrice?: number
      couponName?: string
      exchangeCount?: number
    }>
  }
}>()

const { orderInfo: orderInfoRef } = toRefs(props)

/** 支付方式 */
const StoreOrderPayTypeMap: Record<StoreOrderPayTypeEnum, string> = {
  [StoreOrderPayTypeEnum.UNPAID]: '未支付',
  [StoreOrderPayTypeEnum.ONLINE]: '在线支付',
  [StoreOrderPayTypeEnum.LOGISTICS]: '物流代收',
  [StoreOrderPayTypeEnum.DEPOSIT]: '支付定金',
  [StoreOrderPayTypeEnum.INTEGRAL]: '纯积分支付',
  [StoreOrderPayTypeEnum.INTEGRAL_AND_CASH]: '积分+现金',
  [StoreOrderPayTypeEnum.COUPON]: '福利券兑换',
}

/** 支付状态 */
const StoreOrderPayStatusMap: Record<StoreOrderPayStatusEnum, string> = {
  [StoreOrderPayStatusEnum.UNPAID]: '未支付',
  [StoreOrderPayStatusEnum.DEPOSIT]: '支付定金',
  [StoreOrderPayStatusEnum.FULL]: '已支付全款',
  [StoreOrderPayStatusEnum.OFFLINE]: '线下支付',
}

/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
  return orderInfoRef.value?.orderItemDTOList?.[0]
})

/** 是否普通商品 */
const isNormalProduct = computed(() => {
  return orderInfoRef.value?.type == StoreOrderTypeEnum.NORMAL
})

/** 是否纯积分支付订单 */
const isIntegralOrder = computed(() => {
  return orderInfoRef.value?.payType == StoreOrderPayTypeEnum.INTEGRAL
})

/** 是否积分商品 */
const isIntegralProduct = computed(() => {
  return orderInfoRef.value?.type == StoreOrderTypeEnum.INTEGRAL
})

/** 是否福利券商品 */
const isCouponProduct = computed(() => {
  return orderInfoRef.value?.type == StoreOrderTypeEnum.COUPON
})
</script>

<style lang="scss" scoped>
.store_payment_info {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  margin-bottom: 16rpx;
  .store_payment_info_title {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 24rpx;
  }
  .store_payment_info_item {
    height: 64rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .store_payment_info_item_label {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .store_payment_info_item_value {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
