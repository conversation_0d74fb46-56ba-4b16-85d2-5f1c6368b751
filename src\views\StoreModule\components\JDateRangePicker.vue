<template>
  <div class="select-time-wrapper" @click="calendarSelectShowRef = true">
    <span class="title">{{`${_tempValReactive.startTime} ~ ${_tempValReactive.endTime}`}}</span>
    <img :src="pullDownSrc" alt="" :class="{'rotate-180': calendarSelectShowRef }" />
  </div>
  <VanCalendar
    teleport="body"
    v-model:show="calendarSelectShowRef"
    type="range"
    @confirm="onDateConfirm"
    :max-range="31"
    :min-date="new Date(calendarMinDate)"
    allow-same-day
    color="#EF1115"
    :default-date="[new Date(_tempValReactive.startTime),new Date(_tempValReactive.endTime)]"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from "vue";
import dayjs from "dayjs";
import { dataTimes } from '@/utils/dateUtils';
/** 静态资源 */
import pullDownSrc from "@/assets/storeImage/storeHome/pull-down.png";

defineOptions({ name: 'JDateRangePicker' });

/** props */
const props = defineProps<{
    value: [number, number] | null | [string, string];
}>();

/** emit */
const emit = defineEmits<{
    (e: 'update:value', val: [number, number] | null | [string, string]): void;
}>();

const _tempValReactive = reactive({
    startTime: dayjs(dataTimes['today'].start_time).format('YYYY-MM-DD'),
    endTime: dayjs(dataTimes['today'].end_time).format('YYYY-MM-DD'),
});

/** 最小区间 */
const calendarMinDate = computed(() => {
  const start = dayjs(_tempValReactive.startTime).startOf('day');
  const minDate = start.add(-365, 'day');
  return minDate.valueOf();
});

/** 时间选择器 */
const calendarSelectShowRef = ref(false);

/** 日期选择完成后触发 */
function onDateConfirm(selectedDates: Array<Date>) {
    // 格式化日期
    const startDate = dayjs(selectedDates[0]);
    const endDate = dayjs(selectedDates[1]);
    
    // 设置响应式对象属性
    _tempValReactive.startTime = startDate.format('YYYY-MM-DD');
    _tempValReactive.endTime = endDate.format('YYYY-MM-DD');
    
    
    // 关闭日历选择器
    calendarSelectShowRef.value = false;
    
    const dateRange = [startDate.valueOf(), endDate.valueOf()];
    // 触发事件
    emit('update:value', dateRange as [number, number]);
}

/** 监听 */
watch(() => props.value, (val) => {
    if (val) {
        _tempValReactive.startTime = dayjs(val[0]).format('YYYY-MM-DD');
        _tempValReactive.endTime = dayjs(val[1]).format('YYYY-MM-DD');
    }
}, {
    immediate: true,
});
</script>

<style lang="less" scoped>
.select-time-wrapper {
    height: 32px;
    background: #F8F8F8;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 13px;
        color: #333333;
        text-align: center;
        font-style: normal;
        text-transform: none;
    }
    img {
        width: 14px;
        height: 14px;
        transition: all 0.3s;
    }
}

.rotate-180 {
    transform: rotate(-180deg);
}
</style>
