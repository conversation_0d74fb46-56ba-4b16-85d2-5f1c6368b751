<template>
  <div class="member-management-container">
    <div class="member-card">
      <!-- 左侧头像区域 -->
      <div class="avatar-section">
        <van-image width="60px" height="60px" radius="8px" fit="cover" :src="data.img" />
      </div>

      <!-- 右侧信息区域 -->
      <div class="info-section">
        <!-- 顶部标题行（昵称+角色标签） -->
        <div class="title-row">
          <div class="nickname">{{ data.nickname }}</div>
          <div
            v-if="[StoreUserTypeEnum.OWNER, StoreUserTypeEnum.STAFF].includes(data.storeType)"
            class="role-tag"
            :class="data.storeType == StoreUserTypeEnum.OWNER ? 'manager' : 'staff'"
          >
            {{ data.storeType == StoreUserTypeEnum.OWNER ? "店长" : "店员" }}
          </div>
        </div>

        <!-- 会员编号 -->
        <div class="info-row">
          <div class="label">会员编号：</div>
          <div class="value" style="max-width: 140px">{{ data.shortId }}</div>
          <div class="copy-btn" @click.stop="handleCopy(data.shortId)">复制</div>
        </div>

        <!-- 积分余额 -->
        <div class="info-row">
          <div class="label">积分余额：</div>
          <div class="value">{{ data.availPoints }}</div>
        </div>

        <!-- 归属店员 -->
        <div class="info-row">
          <div class="label">归属店员编号：</div>
          <div class="value" style="max-width: 110px">{{ data.staffShortId }}</div>
          <div class="copy-btn" @click.stop="handleCopy(data.staffShortId)">复制</div>
        </div>

        <!-- 注册时间 -->
        <div class="info-row">
          <div class="label">注册时间：</div>
          <div class="value">{{ data.createTime }}</div>
        </div>

        <!-- 操作按钮区域 -->
        <div v-if="shouldShowOperations" class="operations-section" :class="{ 'operations-section-top': data?.storeType !== StoreUserTypeEnum.OWNER }">
          <!-- 操作按钮组 -->
          <div class="action-buttons">
            <!-- 设为店员按钮（仅店主可操作普通会员） -->
            <div v-if="data.storeType == StoreUserTypeEnum.CUSTOMER && isStoreOwner" class="action-btn" @click="handleSetAsStaff">设为店员</div> 

            <!-- 发放福利券按钮（店主可操作非店长） -->
            <div v-if="data.storeType !== StoreUserTypeEnum.OWNER && isStoreOwner" class="action-btn" @click="showWelfareVoucher = true">
              发放福利券
            </div>

            <!-- 加减积分按钮（可操作非店长） -->
            <div v-if="data.storeType !== StoreUserTypeEnum.OWNER && data.id !== staffId" class="action-btn" @click="showAddSubtractPoints = true">加减积分</div>

            <!-- 收货地址按钮（仅普通会员显示） -->
            <div v-if="data.storeType == StoreUserTypeEnum.CUSTOMER" class="action-btn" @click="navigateToShippingAddress">收货地址</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 发放福利券弹窗 -->
    <WelfareVoucherPopup v-model:show="showWelfareVoucher" @refresh="refresh" :userId="data.id" />

    <!-- 加减积分弹窗 -->
    <AddSubtractPoints
      v-model:show="showAddSubtractPoints"
      @refresh="refresh"
      :customerId="data.id"
      :unionId="data.unionId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
import { setAssistant } from "@/services/storeApi";
import { showConfirmDialog, showToast } from "vant";
import { useUserRole, useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { StoreAddressRouteTypeEnum, StoreUserTypeEnum } from "@/views/StoreModule/enums";
/** 相关组件 */
import WelfareVoucherPopup from "./WelfareVoucherPopup.vue";
import AddSubtractPoints from "./AddSubtractPoints.vue";

/** props */
const props = defineProps<{
  data: {
    id: string;               // 会员唯一ID
    shortId: string;          // 会员短ID
    staffShortId: string;     // 归属店员短ID
    createTime: string;       // 注册时间
    nickname: string;         // 会员昵称
    img: string;              // 头像URL
    staffId: string;          // 归属店员ID
    storeId: string;         // 店铺ID
    storeType: StoreUserTypeEnum; // 会员类型
    availPoints: number;      // 可用积分
    unionId: string;          // 联合ID
  };
}>();

/** emit */
const emit = defineEmits(["refresh"]);

const { createMessageSuccess, createMessageError } = useMessages();
const { isStoreOwner, isStoreStaff, storeId, staffId } = useUserRole();
const { routerPushByRouteName } = useRouterUtils();
const showWelfareVoucher = ref(false);     // 控制福利券弹窗显示
const showAddSubtractPoints = ref(false);  // 控制积分操作弹窗显示

/**
 * 是否显示操作按钮区域
 * 条件：店主或店员 且 是本店铺的会员
 */
const shouldShowOperations = computed(() => {
  return (isStoreOwner.value || isStoreStaff.value) && props.data.storeId == storeId.value;
});

/**
 * 复制文本到剪贴板
 * @param text 需要复制的文本内容
 */
const handleCopy = (text: string) => {
  try {
    copyText(text);
    showToast("复制成功");
  } catch (error) {
    showToast("复制失败");
  }
};

/**
 * 设置会员为店员
 */
const handleSetAsStaff = async () => {
  try {
    // 显示确认对话框
    await showConfirmDialog({
      title: "提示",
      message: "确认将该会员设置为店员",
      confirmButtonColor: "#EF1115",
    });

    // 调用API设置店员
    await setAssistant({
      storeId: storeId.value,
      customerId: props.data.id,
    });

    createMessageSuccess("设置成功");
    refresh();
  } catch (error) {
    // 忽略用户取消操作
    if (error !== "cancel") {
      createMessageError(`设置失败: ${error}`);
    }
  }
};

/**
 * 跳转到收货地址页面
 */
const navigateToShippingAddress = () => {
  routerPushByRouteName(
    RoutesName.StoreAddress,
    {
      customerId: props.data.id,
      routeType: StoreAddressRouteTypeEnum.MY_ADDRESS
    }
  );
};

/**
 * 刷新父组件数据
 */
const refresh = () => {
  emit("refresh");
};
</script>

<style scoped lang="less">
.member-management-container {
  width: 100%;
  font-size: 15px;
  margin-bottom: 10px;

  .member-card {
    display: flex;
    width: calc(100% - 24px); /* 左右各留12px边距 */
    background-color: white;
    margin: 0 auto;
    border-radius: 15px;
    padding: 16px 12px 12px;

    .avatar-section {
      flex-shrink: 0; /* 防止头像区域被压缩 */
      margin-right: 8px;
    }

    .info-section {
      flex-grow: 1; /* 信息区域填充剩余空间 */

      .title-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .nickname {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 16px;
          color: #333333;
          line-height: 18px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 8px;
        }

        .role-tag {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2px 8px;
          box-sizing: border-box;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 12px;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          border-radius: 99px;

          &.manager {
            background: rgba(#1677ff, 0.05);
            border: 1px solid #1677ff;
            color: #1677ff;
          }

          &.staff {
            background: rgba(#4be092, 0.05);
            color: #4be092;
            border: 1px solid #4be092;
          }
        }
      }

      .info-row {
        display: flex;
        line-height: 24px;
        margin-bottom: 4px;
        flex-wrap: wrap;

        .label {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .value {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .copy-btn {
          color: #1677ff;
          margin-left: 10px;
          cursor: pointer;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .operations-section {
        margin-top: 8px;
        padding-top: 12px;

        .action-buttons {
          display: flex;
          justify-content: flex-end;
          flex-wrap: wrap;
          gap: 8px;

          .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3px 10px;
            box-sizing: border-box;
            background: #f3f3f3;
            border-radius: 99px;
            cursor: pointer;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #EF1115;
            line-height: 22px;
            text-align: left;
            font-style: normal;
            text-transform: none;

            &:hover {
              background: #e9e9e9;
            }
          }
        }
      }
      .operations-section-top {
        border-top: 1px solid #EEEEEE;
      }
    }
  }
}
</style>
