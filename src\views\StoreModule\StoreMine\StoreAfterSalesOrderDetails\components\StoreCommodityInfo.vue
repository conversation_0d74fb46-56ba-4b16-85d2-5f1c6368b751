<template>
  <div class="store_commodity_info_wrapper">
    <div class="store_commodity_info_title">售后产品</div>
    <!-- 订单信息 -->
    <div class="orders-info">
      <img :src="firstOrderItem?.productImgPath ? firstOrderItem?.productImgPath : CouponSrc" alt="" />
      <div class="orders-info-text">
        <div class="orders-info-title">
          <p class="van-multi-ellipsis--l2">{{ firstOrderItem?.productFrontName }}</p>
          <div class="orders-info-right">
            <!-- 价格 -->
            <span v-if="[StoreOrderTypeEnum.INTEGRAL].includes(afterSalesInfoRef?.orderItemDTOList?.type)" class="price">
              {{firstOrderItem.exchangePoints || 0 }}{{firstOrderItem.exchangePrice ? '积分+￥' + (firstOrderItem.exchangePrice / 100) : '积分'}}
            </span>
            <span v-else class="price">{{ `¥ ${Number((firstOrderItem?.price ?? 0) / 100).toFixed(2)}` }}</span>
            <!-- 数量 -->
            <span class="count">{{`x ${firstOrderItem?.count ?? 0}`}}</span>
          </div>
        </div>
        <!-- 规格 -->
        <div class="specification">{{ firstOrderItem?.specName }}</div>
        <!-- 订单金额 -->
        <div class="order-amount">
            <span>{{afterSaleTypeMap[afterSalesInfoRef?.type]}}</span>
            <span class="prefix">￥</span>
            <span class="refund-amount">{{ `${Number((afterSalesInfoRef?.refundAmount ?? 0) / 100).toFixed(2)}` }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
import { StoreOrderTypeEnum, StoreAfterSaleTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import CouponSrc from "@/assets/storeImage/storeHome/coupon.png";

defineOptions({ name: 'StoreCommodityInfo' });

/** props */
const props = defineProps<{
    afterSalesInfo: {
        type?: StoreAfterSaleTypeEnum;
        refundAmount?: number;
        actualRefundAmount?: number;
        orderItemDTOList: {
            type?: 1 | 2 | 3;
            orderId?: string;
            productImgPath?: string;
            productFrontName?: string;
            specName?: string;
            price?: number;
            count?: number;
            exchangePoints?: number;
            exchangePrice?: number;
        }
    };
}>();

const { afterSalesInfo: afterSalesInfoRef } = toRefs(props);

/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
    return afterSalesInfoRef.value?.orderItemDTOList?.[0];
});

/** 售后类型 */
const afterSaleTypeMap = {
    [StoreAfterSaleTypeEnum.REFUND_RETURN]: '退货退款，退款金额',
    [StoreAfterSaleTypeEnum.REFUND]: '退款金额',
    [StoreAfterSaleTypeEnum.CANCEL_ORDER]: '仅取消订单',
};
</script>

<style lang="less" scoped>
.store_commodity_info_wrapper {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    .store_commodity_info_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 16px;
    }
    .orders-info {
        display: flex;
        gap: 8px;
        img {
            width: 64px;
            height: 64px;
            border-radius: 8px;
        }
        .orders-info-text {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            .orders-info-title {
                display: flex;
                align-items: center;
                gap: 2px;
                p {
                    flex: 1;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    font-size: 16px;
                    color: #333333;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    line-height: 22px;
                }
                .orders-info-right {
                    min-width: 88px;
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    .price {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 600;
                        font-size: 14px;
                        color: #333333;
                        text-align: right;
                        font-style: normal;
                        text-transform: none;
                    }
                    .count {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 13px;
                        color: #333333;
                        text-align: right;
                        font-style: normal;
                        text-transform: none;
                    }
                }
            }
            .specification {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .order-amount {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                line-height: 24px;
                text-align: right;
                font-style: normal;
                text-transform: none;
                .prefix {
                    font-size: 14px;
                }
                .refund-amount {
                    font-size: 16px;
                }
            }
        }
    }
}
</style>
