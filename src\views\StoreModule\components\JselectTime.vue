<template>
  <VanPopup
    v-model:show="showRef"
    round
    position="bottom"
    @close="emit('update:show', false)"
    :overlay-style="{ background: 'none' }"
  >
    <div class="select_time_wrapper">
      <VanCalendar
        :poppable="false"
        :show-confirm="false"
        :row-height="32"
        :show-title="false"
        :min-date="minDate"
        :max-date="maxDate"
        color="#ee0a24"
        @select="onSelectDate"
      >
        <template #footer>
          <div class="select_time_footer">
            <div class="select_time">
              <!-- 选择日期 -->
              <div class="left">
                <span>{{ selectedDate ? selectedDateStr : `选择日期` }}</span>
              </div>
              <!-- 选择时间 -->
              <div class="right" v-if="precision">
                <span @click="showDatePicker" :style="{ color: !selectedDate ? '#ccc' : '#999' }">
                  {{ selectedTime ? selectedTime : `选择时间` }}
                </span>
              </div>
            </div>
            <div class="select_btn">
              <VanButton type="danger" block round @click="handleConfirmSelectTime" style="width: 100%;height: 36px;">确 认</VanButton>
            </div>
          </div>
        </template>
      </VanCalendar>
    </div>
    <!-- 时间选择 -->
    <VanPopup
      v-if="precision"
      v-model:show="showTimePicker"
      :style="{ borderRadius: '8px', background: '#fff', padding: '24px 12px', width: '80vw' }"
      :overlay-style="{ background: 'rgba(0, 0, 0, 0.3)' }"
    >
      <div class="time_picker_wrapper">
        <!-- 标题 -->
        <div class="time_picker_title">选择时间</div>
        <VanPicker v-model="selectedTimePickerValue" :columns="columns" :visible-option-num="3" :show-toolbar="false" />
        <!-- footer -->
        <div class="time_picker_footer">
          <div class="left_btn">
            <!-- 清除 -->
            <span @click="handleClearTimePicker">清除</span>
          </div>
          <div class="right_btn">
            <!-- 取消 -->
            <span @click="showTimePickerFalse">取消</span>
            <!-- 确定 -->
            <span @click="handleConfirmtimePicker">确定</span>
          </div>
        </div>
      </div>
    </VanPopup>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import dayjs from "dayjs";
import { useBoolean } from "../hooks";
import { isArray } from "@/utils/isUtils";

defineOptions({ name: "JselectTime" });

/** props */
const props = withDefaults(defineProps<{
  show: boolean;
  precision?: boolean; // 是否精确选择到时分秒
}>(), {
  precision: true
});

/** emit */
const emit = defineEmits<{
  (e: 'update:show', show: boolean): void;
  /** 选择时间 */
  (e: 'selectTime', selectTime: { date: string, time: string | null }): void;
}>();

const showRef = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

/** 当前时间 */
const now = dayjs();
const { bool: showTimePicker, setTrue: showTimePickerTrue, setFalse: showTimePickerFalse } = useBoolean();

/** 选择日期 */
const selectedDate = ref<Date | null>(null);
const selectedDateStr = computed(() => selectedDate.value ? dayjs(selectedDate.value).format("YYYY-MM-DD") : "");

/** 选择时间 */
const selectedTime = ref<string | null>(null);

/** 时间选择器相关 start */
const hours = Array.from({ length: 24 }, (_, i) => ({
  text: `${i}`.padStart(2, '0'),
  value: `${i}`.padStart(2, '0')
}));

const minutes = Array.from({ length: 60 }, (_, i) => ({
  text: `${i}`.padStart(2, '0'),
  value: `${i}`.padStart(2, '0')
}));

const seconds = Array.from({ length: 60 }, (_, i) => ({
  text: `${i}`.padStart(2, '0'),
  value: `${i}`.padStart(2, '0')
}));
/** 时间选择器相关 end */

/** 选择时间 */
const selectedTimePickerValue = ref([]);
const columns = computed(() => [hours, minutes, seconds]);

/** 当前日期的12个月前 */
const minDate = now.subtract(12, "month").toDate();
/** 当前日期的2个月后 */
const maxDate = now.add(1, "month").toDate();

/** 日期选择 */
function onSelectDate(date: Date | Array<Date>) {
  if (!isArray(date)) {
    selectedDate.value = date;
  }
}

function showDatePicker() {
  if (!selectedDate.value) return;
  showTimePickerTrue();
}

/** 时间选择 */
function handleConfirmtimePicker() {
  showTimePickerFalse();
  selectedTime.value = selectedTimePickerValue.value.join(':');
}

function handleClearTimePicker() {
  showTimePickerFalse();
  selectedTime.value = null;
}

/** 确认选择时间 */
function handleConfirmSelectTime() {
  // 如果没有选择日期，则使用当天日期
  const date = selectedDate.value ? dayjs(selectedDate.value) : dayjs();
  
  let result;
  if (props.precision && selectedTime.value) {
    // 精确到时分秒模式，且用户选择了时间
    result = {
      date: date.format("YYYY-MM-DD"),
      time: selectedTime.value,
    };
  } else {
    // 仅日期模式，或用户未选择时间
    result = {
      date: date.format("YYYY-MM-DD"),
      time: null,
    };
  }
  
  // 触发事件，传递 { date, time } 对象
  emit('selectTime', result);
  
  // 关闭弹窗
  emit('update:show', false);
}

/** 监听 */
watch(() => showTimePicker.value, (val) => {
  if (val) {
    selectedTimePickerValue.value = [
      now.format("HH"),  // 当前小时（24小时制）
      now.format("mm"),  // 当前分钟
      ...(props.precision ? [now.format("ss")] : [])   // 当前秒（如果precision为true）
    ];
  }
});

watch(() => props.show, (val) => {
  if (val) {
    selectedDate.value = null;
    selectedTime.value = null;
  }
});
</script>

<style lang="less" scoped>
.select_time_wrapper {
  height: 74vh;
  :deep(.van-calendar__footer) {
    padding-left: 0px;
    padding-right: 0px;
  }
  .select_time_footer {
    .select_time {
      width: 100%;
      height: 36px;
      border-top: 1px solid #F8F8F8;
      border-bottom: 1px solid #F8F8F8;
      margin-top: 12px;
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      .left,
      .right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
      }
    }
    .select_btn {
        width: 100%;
        padding: 0px 12px;
        box-sizing: border-box;
    }
    :deep(.van-button__text) {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 24px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}

.time_picker_wrapper {
  .time_picker_title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .time_picker_footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 12px;
    margin-top: 32px;
    .right_btn,
    .left_btn {
        display: flex;
        align-items: center;
        gap: 24px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #1677FF;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
  }
}
</style>
