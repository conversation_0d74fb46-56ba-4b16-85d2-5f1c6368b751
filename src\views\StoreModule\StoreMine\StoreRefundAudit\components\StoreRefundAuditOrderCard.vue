<template>
  <div class="wrapper">
    <div class="after-orders-header">
      <div class="order-code">
        <span>{{ `售后单号：${orderInfoRef?.recordNo}` }}</span>
        <img :src="CopySrc" alt="" @click.stop="handleCopyID(orderInfoRef?.recordNo)" />
      </div>
      <!-- 状态 -->
      <span class="after-orders-status">{{ afterSalesStatusLabels[orderInfoRef?.state] }}</span>
    </div>
    <!-- 订单信息 -->
    <div class="after-orders-info">
      <img :src="firstOrderItem?.productImgPath ? firstOrderItem?.productImgPath : CouponSrc" alt="" />
      <div class="after-orders-info-text">
        <div class="after-orders-info-title">
          <p class="van-multi-ellipsis--l2">{{ firstOrderItem?.productFrontName }}</p>
          <div class="after-orders-info-right">
            <span class="price">{{`¥ ${Number((firstOrderItem?.price ?? 0) / 100).toFixed(2)}`}}</span>
            <!-- 数量 -->
            <span class="count">{{`x ${firstOrderItem?.count}`}}</span>
          </div>
        </div>
        <!-- 规格 -->
        <div class="specification">{{ firstOrderItem?.specName }}</div>
        <!-- 订单金额 -->
        <div class="order-amount">
          <span>{{afterSaleTypeMap[orderInfoRef?.type]}}</span>
          <span class="prefix">￥</span>
          <span class="refund-amount">{{`${Number((orderInfoRef?.refundAmount ?? 0) / 100).toFixed(2)}`}}</span>
        </div>
      </div>
    </div>
    <!-- footer -->
    <div class="footer">
      <!-- 同意 -->
      <div v-if="isPendingMerchantAndRefundReturn || isPendingMerchantAndReturn" class="agree-btn" @click.stop="handleAgree">同意</div>
      <!-- 收货并退款 -->
      <div v-if="isPendingMerchantReceive" class="receive-btn" @click.stop="handleReceiveAndRefund">收货并退款</div>
      <!-- 拒绝 -->
      <div v-if="isPendingMerchantAndRefundReturn || isPendingMerchantAndReturn || isPendingMerchantReceive" class="reject-btn" @click.stop="handleReject">拒绝</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, computed } from "vue";
import { afterSalesStatusLabels } from "@/views/StoreModule/constants";
import { copyText } from "@/utils/clipboardUtils";
import { StoreAfterSaleTypeEnum, AfterSaleStatusEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
/** 静态资源 */
import CopySrc from "@/assets/image/member/copy.png";
import CouponSrc from "@/assets/storeImage/storeHome/coupon.png";

defineOptions({ name: 'StoreAfterSalesOrderCard' });

/** props */
const props = defineProps<{
    orderInfo: {
        type?: StoreAfterSaleTypeEnum;
        recordNo?: string;
        state?: AfterSaleStatusEnum; // 售后状态
        refundAmount?: number; // 退款金额
        orderItemDTOList?: Array<{
            type?: 1 | 2 | 3;
            orderId?: string;
            productImgPath?: string;
            productFrontName?: string;
            specName?: string;
            price?: number;
            count?: number;
            exchangePoints?: number;
            exchangePrice?: number;
        }>;
    };
}>();

/** emit */
const emit = defineEmits<{
    /** 同意 */
    (e: 'agree'): void;
    /** 拒绝 */
    (e: 'reject'): void;
    /** 收货并退款 */
    (e: 'receiveAndRefund'): void;
}>();

const { orderInfo: orderInfoRef } = toRefs(props);
const { createMessageSuccess, createMessageError } = useMessages();

/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
    return orderInfoRef.value?.orderItemDTOList?.[0];
});

/** 售后类型 */
const afterSaleTypeMap = {
    [StoreAfterSaleTypeEnum.REFUND_RETURN]: '退货退款，退款金额',
    [StoreAfterSaleTypeEnum.REFUND]: '退款金额',
    [StoreAfterSaleTypeEnum.CANCEL_ORDER]: '仅取消订单',
};

/** 是否待商家受理且退货退款 */
const isPendingMerchantAndRefundReturn = computed(() => {
    return orderInfoRef.value?.state == AfterSaleStatusEnum.PENDING_MERCHANT && orderInfoRef.value?.type == StoreAfterSaleTypeEnum.REFUND_RETURN;
});

/** 是否待商家受理且仅退款 */
const isPendingMerchantAndReturn = computed(() => {
    return orderInfoRef.value?.state == AfterSaleStatusEnum.PENDING_MERCHANT && orderInfoRef.value?.type == StoreAfterSaleTypeEnum.REFUND;
});

/** 是否待商家收货 */
const isPendingMerchantReceive = computed(() => {
    return orderInfoRef.value?.state == AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT;
});

/** 复制ID */
function handleCopyID(data) {
    try {
        copyText(data);
        createMessageSuccess('复制售后单号成功');
    }
    catch (e) {
        createMessageError('复制售后单号失败');
    }
}

/** 拒绝 */
function handleReject() {
    emit('reject');
}

/** 同意 */
function handleAgree() {
    emit('agree');
}

/** 收货并退款 */
function handleReceiveAndRefund() {
    emit('receiveAndRefund');
}
</script>

<style lang="less" scoped>
.wrapper {
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .after-orders-header {
        display: flex;
        align-items: center;
        gap: 4px;

        .order-code {
            display: flex;
            align-items: center;

            span {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 12px;
                color: #333333;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }

            img {
                width: 16px;
                height: 16px;
                margin-left: 4px;
            }
        }

        .after-orders-status {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #FF6864;
            text-align: right;
            font-style: normal;
            text-transform: none;
            margin-left: auto;
        }
    }

    .after-orders-info {
        display: flex;
        gap: 8px;

        img {
            width: 64px;
            height: 64px;
            border-radius: 8px;
        }

        .after-orders-info-text {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;

            .after-orders-info-title {
                display: flex;
                gap: 4px;

                p {
                    flex: 1;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    font-size: 16px;
                    color: #333333;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    line-height: 22px;
                }

                .after-orders-info-right {
                    min-width: 88px;
                    display: flex;
                    flex-direction: column;
                    gap: 8px;

                    .price {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 600;
                        font-size: 15px;
                        color: #333333;
                        text-align: right;
                        font-style: normal;
                        text-transform: none;
                    }

                    .count {
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 13px;
                        color: #333333;
                        text-align: right;
                        font-style: normal;
                        text-transform: none;
                    }
                }
            }

            .specification {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }

            .order-amount {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 15px;
                color: #333333;
                text-align: right;
                font-style: normal;
                text-transform: none;
                .prefix {
                    font-size: 14px;
                }
                .refund-amount {
                    font-size: 16px;
                }
            }
        }
    }

    .footer {
        width: 100%;
        margin-top: 6px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 8px;

        .agree-btn,
        .receive-btn,
        .reject-btn {
            min-height: 32px;
            border-radius: 999px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .agree-btn {
            width: 80px;
            background: #ECF5FF;
            color: #4DA4FF;
        }
        .receive-btn {
            width: 114px;
            background: #ECF5FF;
            color: #4DA4FF;
        }
        .reject-btn {
            width: 80px;
            background: #FFF4F4;
            color: #EF1115
        }
    }
}
</style>
