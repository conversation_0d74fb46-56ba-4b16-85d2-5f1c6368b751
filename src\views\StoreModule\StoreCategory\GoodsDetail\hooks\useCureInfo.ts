import { ref, computed, watch } from "vue";
import type { Ref,StyleValue } from "vue";
export default function (goodsInfo: Ref<any>, curSkuInfo: Ref<any>) {
    const showExpressTip = ref<boolean>(false);
    const tagList = computed(() => {
        let list = goodsInfo.value.tag ? goodsInfo.value.tag.split(",") : [];
        return list.map((item, index) => {
            return { name: item, id: index + 1 };
        });
    });
    const customTagStyle = computed<StyleValue>(() => {
        return {
            padding:'2px 4px',
            'border-radius':'4px',
            'background-color': 'transparent !important',
            fontSize:'8px'
        }
    })
    const expressTagList = ref([
        {
            isShow: false,
            name: "支持物流代收",
            id: 1,
        },
        {
            isShow: false,
            name: "支持定金支付",
            id: 2,
        },
    ]);
    watch(
        () => curSkuInfo.value,
        () => {
            expressTagList.value.forEach((item) => {
                switch (item.id) {
                    case 1:
                        item.isShow = curSkuInfo.value.isCashOnDelivery == 1;
                        break;
                    case 2:
                        item.isShow = curSkuInfo.value.isDownPayment == 1;
                        break;
                    default:
                        break;
                }
            });
        },{
           deep:true 
        }
    );
    return {
        tagList,
        expressTagList,
        showExpressTip,
        customTagStyle,
    };
}
