<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <style>
      /* ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      ::-webkit-scrollbar-track {
          background: transparent;
      }

      ::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 5px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.4);
      } */
      body{
        margin: 0;
        -webkit-text-size-adjust: none !important;
        text-size-adjust: none !important;
        -moz-text-size-adjust:none !important;
      }
       .app-loading {
        width: 100vw;
        height: 100vh;
        position: relative;
      }
      .version{
        position: absolute;
        bottom: 15px;
        right: 15px;
        font-size: 14px;
        color: #999;
      }
      @keyframes antSpinMove {
        to {
          opacity: 1
        }
      }
      @keyframes antRotate {
        to {
          transform: rotate(405deg)
        }
      }
      .spin{
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-size: 14px;
        font-variant: tabular-nums;
        line-height: 1.5;
        list-style: none;
        font-feature-settings: "tnum";
        color: #FF4D00;
        text-align: center;
        vertical-align: middle;
        opacity: 0;
        /* transition: transform .3s cubic-bezier(.78,.14,.15,.86); */
        display: inline-block;
        opacity: 1;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      @keyframes opacityAnimate {
        0% {
          opacity: 1;
        }
        100%{
          opacity: 0.3;
        }
      }
      .opacity-animate{
        animation: opacityAnimate 0.5s linear infinite alternate;
      }
      .copyright{
        position: absolute;
        bottom: 5%;
        width: 100vw;
        text-align: center;
        font-size: 12px;
        color: #999;
      }
    </style>
    <title></title>

    <!-- 调试工具 -->
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
    <script>
      // VConsole 默认会挂载到 `window.VConsole` 上
      let vConsole = new window.VConsole();
         
      // 结束调试后，可移除掉
      vConsole.destroy();
    </script> -->
    
    <script>
      const flag = '<%= flag %>';
      if(flag === 'true'){
        const origin = location.origin
        const timestamp = '<%= timestamp %><%= UAT_VERSION %>'
        const url = `${origin}/assets/index-${timestamp}.js`
        function setErrorMsg(code,msg){
          const _codeDom = document.getElementById('error-load-code')
          _codeDom.append(`[${code}]`)
          const _msgDom = document.getElementById('error-load-msg')
          _msgDom.append(msg)
        }
        const xhr = new XMLHttpRequest()
        xhr.open('GET',url,true);
        xhr.onload = (e)=>{
          const response = e.target
          if(response.status == 200){
            const contentType = e.target.getResponseHeader('content-type')
            if(contentType == 'text/html'){
              setErrorMsg(timestamp,`加载异常，请截图之后联系客服`)
            }
          }
          else{
            setErrorMsg(response.status,`网络请求异常，请稍后再试`)
          }
        };
        xhr.send()
      }
    </script>
    <script>
      (function() {
        if (typeof window.WeixinJSBridge == "object" && typeof window.WeixinJSBridge.invoke == "function") {
          handleFontSize();
        } else {
          if (document.addEventListener) {
            document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
          } else if (document.attachEvent) {
            document.attachEvent("WeixinJSBridgeReady", handleFontSize);
            document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
          }
        }
        function handleFontSize() {
          window.WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 2 });
          window.WeixinJSBridge.on('menu:setfont', function () {
          window.WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 2 });
          });
        }
      })();
    </script>
    <script type="module" src="/player/easyplayer-pro.js"></script>
  </head>
  <body>
    <div id="app">
      <!-- <section class="app-loading">
        <section class="spin">
          <img class='opacity-animate' src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsSAAALEgHS3X78AAAEHUlEQVRoge1aTUgVURT+iqQ08CfJieznSdHGIhfRQqK0RY0QqKtqlbSwRQttUBiCUGkzkE3a0kU8V+VKbVGTi1KIFrWoCFtE1jMymsRSqSyEjDNzn76nT9/MnTvje/q+3fzde7+55zv33HPuhrm5OawnbFxXbDOE1wHWHeFNKTAG75CVCgAVMe30wdBfJWo3vb20rJQB6ABwIsHTThh64+Kb6UlYVvIZ0QtJ3lxCOv0Iy0orACKR5/CLEhh6JHqRPhqWlRo2q3tdfknaDkcvUp+wrITYgBPp1AlCse+kLmFbp2S+DSKbTUhYUq2Oom5+kjo2NSR0875AVhoZWac65ScsqZb5LPZ+1ZKKbnIWpmb9AL+IRvXmVqeOERdpSSrKVnD1dD/CZl8sSKey0gfgiZ9kkWCGa5K8TybWIqmoY7Pd56l3W6dkvi2e2nEB3liaZqFXUjEoqfFe0DFkhX5aJEiyEOClaan4KKnoZI4tub5tnbZ6WGY8QdRuqYHpe0nsOg9bp2Gm01UhC8HrMOn7Voy+B627Czp1Ew76Bj8Cj8M0i5KK/vDbjgdVwFW/Pa8b+BNpTY4BL/uq68Y/VTcVAvUFQF6KpBrEDmN2Bnh+Fxi4CYyPWLfaJ4AjH4B700J74oa4GR5+BLwbAmb/LHk0/Q9o+Ar0TAHNhUB5TgDMloF3wt/e27P6+0fSV5/NALWfgbO5NvHdWZ57dw1+wr++20SZ6bpBzzTw8KetbSIeJPg0TE5poJ2LbBRk5qTvk6PpQPjpnYRa5cHwX+DGhJCmHME94bE3jvTqBl1im1sR7glPfhE+CDLvoJAptSRF/k7hg8gN8Le776r4EJBTIHQQ9WKbWxF8//bYRSBri5ABlG4Odi3mI5xfDJxqArbv4+6YzJg2Fo8D3kfxR1pbtwGVl12FllGsZmjp3V0U7QfOXANKTyc18/JsoHcXcHvH6pCF0N0SET5wnPbBQORF3CMy3+tFwLlcYb1xQ2wCICsbOHp+gfj4CNZ2AiAKcmqVl/vDB/dcai7EaKqQhU+EXwOoNDXUVN1v7AKsakYbgCkf+nINkYSJ0BVTQ9l8xpJg6JMw9FZGvNt3RkkgijAl4kOmZhWsE4Oq8IZOKdxKAEN+kHECr06LBl5naog4eNeGoQ9apVi71NIRdK6ad4YpT1FraqhwRTYWhh5m1fk2zjFwYTHhZNVA0mmbqVnm661yiDh9lwDoF8psGcQRZlX+5RxLN9Op+Pqwre8apm9fs1xLNGxqqJNUy0yDP/Jg6zvk55GH1D2nJe5QSxuTjYXUTfHY+m5k+ha2jKV+TsvWN8mrllPfcatI+iTxDJ1OyIY4wtTB2Iv0y1raegw5DFM7Y89ZInN8OF1hHzwtixl9ePHMri3CLpCpPKx1ZAivaQD4D7nzNbYzD3POAAAAAElFTkSuQmCC" alt="">
          <p id="error-load-code" style="margin:0px"></p>
          <p id="error-load-msg" style="margin-top:0px"></p>
         </section>
        </section> -->
        <!-- <div class="copyright">
          <div><%= companyName %></div>
          <div><%= companyTel %></div>
        </div> -->
        <!-- <div class="copyright" style="display: none;">
          <div>售后服务 <%= companyTel %></div>
        </div> -->
    </div>
    <script>
      //const displayFlag = window.localStorage.getItem('flag')
      //if(!displayFlag){
      //  document.getElementsByClassName('copyright')[0].remove()
      //}
      //else{
      //  document.getElementsByClassName('copyright')[0].style.display = 'block'
      //}
    </script>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
