<template>
    <div class="main-layout-wrapper"
        :style="{
            height: `calc(100vh - env(safe-area-inset-bottom)${isQWDesktop?' - 30px':''})`
        }"
    >
        <transition name="fade" mode="out-in">
        <router-view 
            :class="`main-content-bg ${isShowTabRef?'hasTab':'noTab'}`"
            v-slot="{ Component }"
        >

            <keep-alive :include="['MainLayout',...keepAliveRouteListRef]">
              <component :is="Component" :key="$route.fullPath" />
            </keep-alive>
        </router-view>
        </transition>
        <!-- <RouterView :class="`main-content-bg ${isShowTabRef?'hasTab':'noTab'}`"></RouterView> -->
        <van-tabbar v-show='isShowTabRef' v-model="activeMenuKeyRef" @change="onTabChange" :fixed="!isQWDesktop" :active-color="isStoreMode()?'#EF1115':'#1677ff'">
            <template v-for='menu in menuListRef' :key="menu.id">
                <van-tabbar-item v-if='menu.show' :name="menu.id">
                    <span>{{menu.title}}</span>
                    <template #icon="props">
                        <img :src="props.active ? menu.icon.active : menu.icon.inactive" />
                    </template>
                </van-tabbar-item>
            </template>
        </van-tabbar>
       
    </div>
    <div v-if='isQWDesktop' style="display: flex;height: 30px;width: 100%;background-color: #fff;">
        <div style="cursor:pointer;width: 50%;line-height: 30px; text-align: center;" @click='router.back()'>
            <van-icon name="arrow-left"/>
        </div>
        <div style="cursor:pointer;width: 50%;line-height: 30px; text-align: center;" @click='router.back()'>
            <van-icon name="arrow"/>
        </div>
    </div>
</template>
<script lang="ts">
    export default {
        name:'MainLayout'
    }
</script>
<script setup lang="ts">
import { RouterView,useRouter } from 'vue-router';
import { useMenu } from '@/hooks/useMenu';
import { routesMap } from '@/router/maps';
import { computed } from 'vue';
import { useActiveRoute } from "@/hooks/useActiveRoute";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { isQWDesktopEnv } from '@/utils/envUtils';
import { isStoreMode } from "@/utils/envUtils";
const isQWDesktop = isQWDesktopEnv()
const router = useRouter();
const {keepAliveRouteListRef} = useKeepAliveRoute()
const {menuListRef,activeMenuKeyRef} = useMenu();   
const { activeRouteName } = useActiveRoute();
function onTabChange(routeName: string){
    router.push(routesMap[routeName])
}
const isShowTabRef = computed<boolean>(()=>{
    const flag = menuListRef.value.filter(menu=>menu.show).map(menu=>menu.id).includes(activeRouteName.value as string)
    return flag
})
menuListRef.value.sort((a, b) => a.level - b.level)
</script>
<style scoped lang="less">
    @import "@/styles/defaultVar.less";
    .main-layout-wrapper{
        width: 100vw;
    }
    .main-content-bg {
        background-color: @blank-background-color;
        width: 100%;
        overflow: auto;
        box-sizing: border-box;
        &.hasTab{
            height: calc(100% - var(--van-tabbar-height));
        }
        &.noTab{
            height: 100% ;
        }
    }
</style>