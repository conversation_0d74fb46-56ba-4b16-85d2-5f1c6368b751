import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";
import { 
  OrderStatusEnum, 
  StoreOrderDetailRouteTypeEnum, 
  StoreLogisticsRouteTypeEnum,
  StoreAfterSaleDetailRouteTypeEnum,
  StoreAddressRouteTypeEnum
} from "@/views/StoreModule/enums";

export const StoreMine = {
  [RoutesName.StoreMine]: {
    path: "mine",
    component: () => import("@/views/StoreModule/StoreMine/index.vue"),
    meta: {
      isMenu: true,
      title: "我的",
      level: 3,
      icon: {
        active: "/icons/storeTabbar/mine-active.png",
        inactive: "/icons/storeTabbar/mine.png",
      },
    },
  },
  [RoutesName.StoreMyOrders]: {
    path: "myOrders",
    component: () => import("@/views/StoreModule/StoreMine/StoreMineOrder/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "我的订单",
    },
    props: (route: RouteLocation) => ({
      orderType: route.query.orderType ?? OrderStatusEnum.ALL,
    }),
  },
  [RoutesName.StoreShopOrders]: {
    path: "shopOrders",
    component: () => import("@/views/StoreModule/StoreMine/StoreShopOrders/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "店铺订单",
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreOrderStatistics]: {
    path: "orderStatistics",
    component: () => import("@/views/StoreModule/StoreMine/StoreOrderStatistics/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "订单统计",
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreOrderDetail]: {
    path: "orderDetail",
    component: () => import("@/views/StoreModule/StoreMine/StoreOrderDetail/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "订单详情",
    },
    props: (route: RouteLocation) => ({
      routeType: route.query.routeType ?? StoreOrderDetailRouteTypeEnum.MY_ORDER,
      orderCode: route.query.orderCode ?? "",
    }),
  },
  [RoutesName.StoreApplyRefund]: {
    path: "accountSetting",
    component: () => import("@/views/StoreModule/StoreMine/StoreApplyRefund/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "申请退款",
    },
    props: (route: RouteLocation) => ({
      orderCode: route.query.orderCode ?? "",
    }), 
  },
  [RoutesName.StoreAccountSetting]: {
    path: "accountSetting",
    component: () => import("@/views/StoreModule/StoreMine/StoreAccount/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "账号设置",
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreAfterSaleDetail]: {
    path: "afterSaleDetail",
    component: () => import("@/views/StoreModule/StoreMine/StoreAfterSalesOrderDetails/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "售后详情",
    },
    props: (route: RouteLocation) => ({
      routeType: route.query.routeType ?? StoreAfterSaleDetailRouteTypeEnum.MY_AFTER_SALE,
      recordNo: route.query.recordNo ?? "",
    }),
  },
  [RoutesName.MyDealer]: {
    path: "myDealer",
    component: () => import("@/views/StoreModule/StoreMine/StoreMyDealer/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "我的经销商",
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreManagement]: {
    path: "storeManagement",
    component: () => import("@/views/StoreModule/StoreMine/StoreManagement/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "店铺管理",
    },
    props: (route: RouteLocation) => ({
      
    })
  },
  [RoutesName.StoreSignup]: {
    path: "signup/:applyType",
    component: () => import("@/views/StoreModule/StoreMine/StoreSignup/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "注册",
    },
    props: (route: RouteLocation) => ({
      state: route.query.state ?? "",
    })
  },
  [RoutesName.StoreAddress]: {
    path: "address",
    component: () => import("@/views/StoreModule/StoreCategory/StoreAddressManager/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "收货地址",
    },
    props: (route: RouteLocation) => ({
      routeType: route.query.routeType ?? StoreAddressRouteTypeEnum.ORDER_ADDRESS,
      customerId: route.query.customerId ?? ""
    })
  },
  [RoutesName.StoreAddressAdd]: {
    path: "addressEdit",
    component: () => import("@/views/StoreModule/StoreCategory/StoreShippingAddressForm/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "收货地址",
    },
    props: (route: RouteLocation) => ({
      type: route.query.type ?? "",
      id: route.query.id ?? "",
      customerId: route.query.customerId ?? ""
    })
  },
  [RoutesName.StoreReturnOrder]: {
    path: "returnOrder",
    component: () => import("@/views/StoreModule/StoreMine/StoreReturnOrder/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "退货订单",
    },
    props: (route: RouteLocation) => ({
      
    })
  },
  [RoutesName.StoreRefundAudit]: {
    path: "refundAudit",
    component: () => import("@/views/StoreModule/StoreMine/StoreRefundAudit/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "退款审核",
    },
    props: (route: RouteLocation) => ({
      
    })
  },
  [RoutesName.StoreFillLogistics]: {
    path: "fillLogistics",
    component: () => import("@/views/StoreModule/StoreMine/StoreFillLogistics/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "填写物流信息",
    },
    props: (route: RouteLocation) => ({
      routeType: route.query.routeType ?? StoreLogisticsRouteTypeEnum.FILL_LOGISTICS,
      afterSalesOrderId: route.query.afterSalesOrderId ?? ""
    })
  },
  [RoutesName.StoreFillLogisticsNo]: {
    path: "fillLogisticsNo",
    component: () => import("@/views/StoreModule/StoreMine/StoreFillLogisticsNo/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "物流信息",
    },
    props: (route: RouteLocation) => ({
      recordNo: route.query.recordNo ?? ""
    })
  },
  [RoutesName.StoreDataExport]: {
    path: "dataExport",
    component: () => import("@/views/StoreModule/StoreMine/StoreDataExport/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "数据导出",
    },
    props: (route: RouteLocation) => ({
    })
  },
  [RoutesName.StoreExportRecord]: {
    path: "data-export-record",
    component: () => import("@/views/StoreModule/StoreMine/StoreExportRecord/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "导出记录",
    },
    props: (route: RouteLocation) => ({
    })
  },
  [RoutesName.StoreFileDownload]: {
    path: "fileDownload",
    component: () => import("@/views/StoreModule/StoreMine/StoreFileDownload/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "文件下载",
    },
    props: (route: RouteLocation) => ({
      fileName: route.query.fileName ?? "",
      fileUrl: route.query.fileUrl ?? ""
    })
  },
  [RoutesName.StoreWallet]: {
    path: "wallet",
    component: () => import("@/views/StoreModule/StoreMine/StoreWallet/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "钱包",
    },
    props: (route: RouteLocation) => ({
    })
  },
  [RoutesName.StoreWithdrawRecord]: {
    path: "withdraw-record",
    component: () => import("@/views/StoreModule/StoreMine/StoreWithdrawRecord/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "提现记录",
    },
    props: (route: RouteLocation) => ({
    })
  },
  [RoutesName.StoreWithdraw]: {
    path: "withdraw",
    component: () => import("@/views/StoreModule/StoreMine/StoreWithdraw/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "提现",
    },
    props: (route: RouteLocation) => ({
    })
  },
  [RoutesName.StoreBankCard]: {
    path: "bank-card",
    component: () => import("@/views/StoreModule/StoreMine/StoreBankCard/index.vue"),
    meta: {
      isMenu: false,
      isShow: false,
      title: "银行卡信息",
    },
    props: (route: RouteLocation) => ({
      id: route.query.id ?? ""
    })
  },
};
