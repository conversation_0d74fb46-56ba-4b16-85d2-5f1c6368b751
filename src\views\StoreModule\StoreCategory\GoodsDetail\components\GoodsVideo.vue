<template>
  <div class="video-box">
    <video
      :src="src"
      ref="videoRef"
      v-show="isPlay"
      webkit-playsinline="true"
      crossorigin="anonymous"
      playsinline="true"
      :controls="false"
    ></video>
    <img :src="poster" class="poster-img" v-show="!isPlay" />
    <div class="opt-box" @click.stop="handleClickMask">
      <div class="play-icon" @click.stop="changePlayStatus">
        <van-icon name="play-circle-o" v-if="!isPlay" size="60px" color="#fff" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onUnmounted, onMounted } from "vue";
const props = withDefaults(defineProps<{
    src: string,
    poster: string,
    autoplay?: boolean
}>(), {
    src: '',
    poster: '',
    autoplay: false
})
const emits = defineEmits<{
    (event: 'clickMask', currentTime: number): void
}>()
const videoRef = ref<HTMLVideoElement>(null)
const isPlay = ref<boolean>(false)
const mediaReactive = reactive({
    currentTime: 0,
    duration: 0,
})

const changePlayStatus = () => {
    isPlay.value = !isPlay.value
    if (isPlay.value) {
        videoRef.value.play()
    } else {
        videoRef.value.pause()
    }
}
const handleClickMask = () => {
    if (!isPlay.value) return
    emits('clickMask', mediaReactive.currentTime)
}
const timeupdateChange = (e) => {
    const currentTime = e.target.currentTime
    mediaReactive.currentTime = currentTime
}
const loadedmetadata = (e) => {
    console.log(e);
    mediaReactive.duration = e.target.duration
    mediaReactive.currentTime = e.target.currentTime || 0
    props.autoplay && play()
}
const ended = (e) => {
    init()
}
const seek = (time: number) => {
    videoRef.value.currentTime = time
}
const play = () => {
    isPlay.value = true
    videoRef.value.play()
}
const pause = () => {
    isPlay.value = false
    videoRef.value.pause()
}
const getCurrentTime = () => {
    return mediaReactive.currentTime
}
const init = () => {
    seek(0)
    pause()
}
const initEvent = () => {
    videoRef.value.addEventListener('timeupdate', timeupdateChange)
    videoRef.value.addEventListener('loadedmetadata', loadedmetadata)
    videoRef.value.addEventListener('ended', ended)
}
const removeEvent = () => {
    videoRef.value?.removeEventListener('timeupdate', timeupdateChange)
    videoRef.value?.removeEventListener('loadedmetadata', loadedmetadata)
    videoRef.value?.removeEventListener('ended', ended)
}
onMounted(() => {
    initEvent()
})
onUnmounted(() => {
    removeEvent()
})
defineExpose({
    init,
    seek,
    play,
    pause,
    getCurrentTime
})
</script>

<style scoped lang="less">
.video-box {
    height: 100%;
    width: 100%;
    position: relative;

    .poster-img {
        width: 100%;
        height: 100%;
    }

    video {
        width: 100%;
        height: 100%;
    }

    .opt-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;

        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .opt-bottom {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 10px;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(0, 0, 0, 0.5);

            .time-box {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #fff;
                flex: 1;

                .silder-line {
                    flex: 1;
                    box-sizing: border-box;
                }
            }
        }
    }
}
</style>
