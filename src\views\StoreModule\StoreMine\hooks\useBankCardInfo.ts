import { ref, computed } from "vue";
import { storeBankCardApi } from "@/services/storeApi";

export default function useBankCardInfo() {
  /** 银行卡信息 */
  const bankCardInfo = ref({});

  /** 获取银行卡信息 */
  async function getBankCardInfo() {
    // TODO: 获取银行卡信息
    const resp = await storeBankCardApi();
    if (resp) {
      bankCardInfo.value = resp;
    }
    try {
    } catch (error) {
      console.log("获取银行卡信息失败：" + error);
    }
  }

  function isEmptyObject(obj) {
    return Object.keys(obj).length === 0;
  }

  /** 是否存在银行卡信息 */
  const hasBankCardInfo = computed(() => {
    return !isEmptyObject(bankCardInfo.value);
  });

  return {
    bankCardInfo,
    getBankCardInfo,
    hasBankCardInfo
  };
}
