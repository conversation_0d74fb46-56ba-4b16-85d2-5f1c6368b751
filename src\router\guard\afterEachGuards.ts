import NProgress from "nprogress";
import { RoutesName } from "@/enums/routes";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { transformMinioSrc } from "@/utils/fileUtils";
import { getHtmlTextByHtmlString } from "@/utils/domUtils";

const doneProgress = () => {
  NProgress.done();
};

const changeBroswerTitileByRoute = to => {
  const stateCacheStorage = createCacheStorage(CacheConfig.State);
  const _stateCache = stateCacheStorage.get();
  // if(to.name == RoutesName.ExamOnboarding){
  //   document.title = `${_stateCache.courseDto.frontTitle}`;
  //   const meta = document.createElement('meta');
  //   meta.name = "description";
  //   meta.content = `${getHtmlTextByHtmlString(_stateCache.courseDto.shareDesc)}`;
  //   document.head.insertBefore(meta, document.head.firstChild);
  //   const img = document.createElement('img');
  //   // 设置 img 的源地址
  //   img.src = transformMinioSrc(`${_stateCache.courseDto.img}`);
  //   // 设置样式使其隐藏
  //   img.style.display = 'none';
  //   // 将 img 元素添加到 document 的 head 中
  //   document.head.insertBefore(img, document.head.firstChild);
  // }
  // else{
  //   const { title } = to.meta;
  //   document.title = `${title}`;
  // }
  const { title } = to.meta;
  document.title = `${title}`;
};

export const afterEachGuardsList = [doneProgress, changeBroswerTitileByRoute];
