import { RoutesName } from "@/enums/routes";
import { acceptStoreBelong, storeUSerInfoRefresh } from "@/services/storeApi";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { parseUrlParams } from "@/utils/http/urlUtils";

/**会员绑定门店 */
export async function bindStoreBelong() {
  try {
    const params = parseUrlParams(location.search);
    const userStore = useUserStoreWithoutSetup();
    await acceptStoreBelong(params.state as string);
    const resp = await storeUSerInfoRefresh();
    const { ...userInfo } = resp;
    userStore.setStoreUserInfo(userInfo);
    return true;
  } catch (e) {
    return false;
  }
}
