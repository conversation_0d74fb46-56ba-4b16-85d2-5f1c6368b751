<template>
  <div class="store_merchant_return_info">
    <div class="store_merchant_return_info_title">商家退货信息</div>
    <!-- 门店名称 -->
    <div class="store_merchant_return_info_item">
      <span class="store_merchant_return_info_item_label">门店名称</span>
      <span class="store_merchant_return_info_item_value">{{ storeInfoRef?.storeName }}</span>
    </div>
    <!-- 店长 -->
    <div class="store_merchant_return_info_item">
      <span class="store_merchant_return_info_item_label">店长</span>
      <span class="store_merchant_return_info_item_value">{{ storeInfoRef?.contactName }}</span>
    </div>
    <!-- 联系电话 -->
    <div class="store_merchant_return_info_item">
      <span class="store_merchant_return_info_item_label">联系电话</span>
      <span class="store_merchant_return_info_item_value">{{ storeInfoRef?.contactPhone }}</span>
    </div>
    <!-- 收货地址 -->
    <div class="store_merchant_return_info_item">
      <span class="store_merchant_return_info_item_label">收货地址</span>
      <span class="store_merchant_return_info_item_value">{{ formattedAddress }}</span>
    </div>
    <!-- footer -->
    <div v-if="isAllowLogistics" class="footer">
      <p class="count_down">{{ `倒计时：${secondsToDHMS(counts)}` }}</p>
      <!-- 填写物流单号 -->
      <div class="fill_logistics_number" @click="handleFillLogisticsNumber">填写物流单号</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, computed, onMounted, onUnmounted } from "vue";
import dayjs from "dayjs";
import { secondsToDHMS } from "@/views/StoreModule/utils";
import { useCountDown, useGetStoreInfo } from "@/views/StoreModule/hooks";
import { CustomerRoleOperationEnum } from "@/views/StoreModule/enums";
import { isArray } from "@/utils/isUtils";

defineOptions({ name: 'StoreMerchantReturnInfo' });

/** props */
const props = defineProps<{
  afterSalesInfo: {
    action?: Array<number>;
    returnsDeadline?: string;
    belongStoreId?: string;
  };
}>();
const { afterSalesInfo: afterSalesInfoRef } = toRefs(props);

/** emit */
const emit = defineEmits<{
  /** 填写物流单号 */
  (e: 'fillLogisticsNumber'): void;
}>();

/** 订单归属门店 */
const { storeInfo: storeInfoRef, getStoreInfoByStoreId } = useGetStoreInfo();

/** 倒计时 秒 */
const secondsRemaining = computed(() => {
  return dayjs(afterSalesInfoRef.value?.returnsDeadline).diff(dayjs(), "second");
});

/** 退货收货地址 */
const formattedAddress = computed(() => {
  const { province = '', city = '', area = '', addressDetail = '' } = storeInfoRef.value || {};
  const address = `${province}${city}${area}${addressDetail}`.trim();
  return address || '暂无地址';
});

/** 倒计时 */
const { counts, start, stop } = useCountDown(secondsRemaining.value);

function handleFillLogisticsNumber() {
  emit('fillLogisticsNumber');
}

/** 是否允许填写物流单号 */
const isAllowLogistics = computed(() => {
  if (isArray(afterSalesInfoRef.value?.action) && afterSalesInfoRef.value?.action.includes(CustomerRoleOperationEnum.SUBMIT_SHIPPING_INFO)) {
      return true;
  }
  return false;
});

onMounted(async () => {
  await getStoreInfoByStoreId(afterSalesInfoRef.value?.belongStoreId);
  start();
});

onUnmounted(() => {
  stop();
});
</script>

<style lang="less" scoped>
.store_merchant_return_info {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    .store_merchant_return_info_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 16px;
    }
    .store_merchant_return_info_item {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;
        .store_merchant_return_info_item_label {
            width: 80px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_merchant_return_info_item_value {
            max-width: 260px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }
    .footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 12px;
      .count_down {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 13px;
        color: #FF6864;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
      .fill_logistics_number {
        width: 96px;
        min-height: 32px;
        height: 100%;
        border-radius: 999px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: #EF1115;
        color: #FFFFFF;
      }
    }
}
</style>
