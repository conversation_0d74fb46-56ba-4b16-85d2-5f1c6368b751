import { ref, reactive, watch } from "vue";
import type { Ref } from "vue";
import { useMessages } from "@/hooks/useMessage";
import {
  getStoreNewProductList,
} from "@/services/storeApi";
import { isNullOrUnDef } from "@/utils/isUtils";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
interface PageVo {
  current: number;
  size: number;
  total: number;
}
interface GoodsProps {
  modal?: Ref<any>;
  type?:StoreGoodsEnum;
}
export default function (props: GoodsProps) {
  const message = useMessages();
  const { storeUserInfo } = useUserStoreWithoutSetup();
  const { modal,type } = props;
  const pageVo = reactive<PageVo>({
    current: 1,
    size: 30,
    total: 0,
  });
  const finished = ref<boolean>(false);
  const goodsLoading = ref<boolean>(false);
  const goodsList = ref<any[]>([]);
  const getGoodsList = async () => {
    try {
      goodsLoading.value = true;
      const params: any = {
        data: {
          storeId:storeUserInfo.storeId,
          type: modal.value.type,
          cateId: modal.value.cateId,
        },
        pageVO: {
          current: pageVo.current,
          size: pageVo.size,
        },
      };

      const { records, total } = await getStoreNewProductList(params);
      goodsLoading.value = false;
      if (pageVo.current == 1) {
        goodsList.value = records;
      } else {
        goodsList.value.push(...records);
      }
      pageVo.total = Number(total);
      //加载完成
      if (pageVo.current * pageVo.size >= pageVo.total) {
        finished.value = true;
      }
    } catch (e) {
      message.createMessageError(`获取失败：${e}`);
    } finally {
      goodsLoading.value = false;
    }
  };

  const loadGoodsData = () => {
    if (pageVo.current * pageVo.size < pageVo.total && !goodsLoading.value) {
      pageVo.current++;
      getGoodsList();
    }
  };
  const reloadGoodsData = () => {
    goodsList.value = [];
    pageVo.current = 1;
    pageVo.total = 0;
    getGoodsList();
  };
  return {
    goodsList,
    getGoodsList,
    loadGoodsData,
    reloadGoodsData,
    finished,
    goodsLoading,
  };
}
