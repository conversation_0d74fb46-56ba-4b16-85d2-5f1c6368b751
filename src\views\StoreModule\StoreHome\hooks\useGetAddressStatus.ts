import { useBoolean } from "@/views/StoreModule/hooks";
import { createCacheStorage } from '@/utils/cache/storageCache';
import { CacheConfig } from '@/utils/cache/config';
import { getInviteAddressStatus } from "@/services/storeApi";

export default function useGetAddressStatus() {
    /** 判断是否填写过地址 */
    const { bool: has_filled_address, setTrue: setHasFilledAddress, setFalse: setHasUnFilledAddress } = useBoolean();

    /** 获取邀请填写收货地址的状态 */
    async function getAddressStatus() {
        try {
            const res = await getInviteAddressStatus();
            if (res) {
                const hasShowFillAddressFormStorage = createCacheStorage(CacheConfig.StoreHasShowFillAddressForm);
                const _Info = hasShowFillAddressFormStorage.get();
                if (_Info && _Info['hasDoNotFillAddressForm']) {
                    setHasUnFilledAddress();
                } else {
                    setHasFilledAddress();
                }
            }
        } catch (error) {
            console.log("获取邀请填写收货地址的状态", error);
            setHasUnFilledAddress();
        }
    }

    return {
        has_filled_address,
        setHasFilledAddress,
        setHasUnFilledAddress,
        getAddressStatus
    }
}