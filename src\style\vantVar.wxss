page {
  --primary-color: #4da4ff;
  --primary-color-gradient: linear-gradient(90deg, #4e96ff 0%, #4ed5ff 100%);
  --primary-color-fill-color: #f5faff;

  --error-color: #ff3b2f;
  --error-color-gradient: linear-gradient(90deg, #ff782f 0%, #ff3b2f 100%);
  --error-color-fill-color: #ffeded;

  --warming-color: #ff4d00;
  --warming-color-fill-color: #14100e;

  --success-color: #00b42a;
  --success-color-fill-color: #e5f7e9;

  --button-default-height: 40px;
  --button-primary-background-color: var(--primary-color-gradient);
  --button-primary-border-color: var(--primary-color-gradient);
  --sidebar-selected-border-color: var(--primary-color-gradient);

  --button-warning-background-color: var(--error-color-gradient);
  --button-warning-border-color: var(--error-color-gradient);

  --button-danger-background-color: var(--error-color);
  --button-danger-border-color: var(--error-color);

  --step-size: 16rpx;
  --step-circle-size: var(--step-size);

  --overlay-background-color: rgba(0, 0, 0, 0.4);
}

.van-cell--required:before {
  top: 20px;
}
.van-field__label {
  margin-left: 10rpx;
  box-sizing: border-box;
}
.van-cell__title {
  margin-right: 0rpx !important;
}
.van-popup {
  z-index: 1000000 !important;
}
