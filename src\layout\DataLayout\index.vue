<template>
    <div class="body">
        <div v-if='props.isShowHeader' class="headerBox" ref="headerDomRef">
            <div class="header">
                <slot name="header"></slot>
            </div>
        </div>
        <div class="centerContent" ref="contentDomRef">
            <div v-if="selectDate" class="selectDate">
                <div v-if="props.fixPatch" v-for="(date, key) in patchOptions" @click="handlerSelectDate(date)" :key="key">
                    <div :class="['dateTag', searchDateName == date.name ? 'active' : null]">
                        <span>{{ date.name }}</span>
                    </div>
                </div>
                <template v-else>
                    <div v-show="!props.isSelectTime" v-for="(date, key) in dateTypeList" @click="handlerSelectDate(date)" :key="key">
                        <div :class="['dateTag', searchDateName == date.name || (!searchDateName && key == 'all') ? 'active' : null]">
                            <span>{{ date.name }}</span>
                        </div>
                    </div>
                    <div v-show="props.isSelectTime" v-for="(date, key) in tagOptionsList" @click="handlerSelectDate(date)" :key="key">
                        <div :class="['dateTag', searchDateName == date.label || (!searchDateName && date.key == 'today') ? 'active' : null]">
                            <span>{{ date.label }}</span>
                        </div>
                    </div>
                </template>
            </div>
            <div style="display:flex;justify-content:space-evenly;padding-bottom:10px" v-if="timeSelect">
                        <JTag 
                            style='width:45%' 
                            size="small" 
                            :type="_tempValReactive.isCustomeTime ? 'primary':'inactive'"
                            @click="calendarSelectShowRef = true"
                        >
                            <van-icon size="16" name="calendar-o" />
                            <span style="margin-left:10px">{{ _tempValReactive.startTime }}</span>
                        </JTag>

                        <JTag  
                            style='width:45%;' 
                            size="small" 
                            :type="_tempValReactive.isCustomeTime ? 'primary':'inactive'"
                            @click="calendarSelectShowRef = true"
                        >
                            <van-icon size="16" name="calendar-o" />
                            <span style="margin-left:10px">{{ _tempValReactive.endTime }}</span>
                        </JTag>

                </div>
                    <van-calendar
                    teleport="body"
                    v-model:show="calendarSelectShowRef" 
                    type="range" 
                    @confirm="onDateConfirm" 
                    :max-range="31"
                    :min-date="new Date(calendarMinDate)"
                    allow-same-day
                    :default-date="[new Date(_tempValReactive.startTime),new Date(_tempValReactive.endTime)]"
                />
            <div class="content-wrapper" ref="wrapperRef">
                <slot name="content"></slot>
            </div>
        </div>
        <div v-if='props.isShowFooter' class="footer-wrapper" ref="footerDomRef">
            <slot name="footer"></slot>
        </div>
    </div> 
</template>

<script setup lang="ts">
import { nextTick, ref, reactive, computed, type VNodeRef, watch } from "vue";
import {dataTimes, DataTypeValueEnum, DateTimeNameEnum, dateTypeList,type DateTypeObject } from '@/utils/dateUtils';
import dayjs from "dayjs";
const tagOptionsList = [
    // {
    //     key:'all',
    //     label:'全部',
    //     value:'5'
    // },
    {
        key:'today',
        label:'今日',
        value:'1'
    },
    {
        key:'yesterday',
        label:'昨日',
        value:'2'
    },
    {
        key:'this_month',
        label:'本月',
        value:'10'
    },
    {
        key:'custom',
        label:'自定义',
        // value:''
    }
   
]
/** 临时用于会员详情页，后面优化 */
 const patchOptions = {
    all: {
        name:DateTimeNameEnum.all,
        value: DataTypeValueEnum.all,
    },
  today: {
    name:DateTimeNameEnum.today,
    value: DataTypeValueEnum.today,
  },
  yesterday: {
    name: DateTimeNameEnum.yesterday,
    value: DataTypeValueEnum.yesterday,
  },
  ereyesterday: {
    name: DateTimeNameEnum.ereyesterday,
    value: DataTypeValueEnum.ereyesterday,
  },
  last_7_day: {
    name: DateTimeNameEnum.last_7_day,
    value: DataTypeValueEnum.last_7_day,
  },
  
};
const searchDateName = ref(null);
const headerDomRef = ref< VNodeRef | null>(null)
const contentDomRef = ref< VNodeRef | null>(null)
const footerDomRef = ref< VNodeRef | null>(null)
const calendarSelectShowRef = ref(false)
const timeSelect = ref(false)
const wrapperRef = ref< VNodeRef | null>(null)
const props = withDefaults(
    defineProps<{
        selectDate?: boolean,
        isShowHeader?: boolean,
        isShowFooter?: boolean,
        isSelectTime?: boolean,
        /** 临时用于会员详情页，后面优化 */
        fixPatch?:boolean
    }>(),
    {
        selectDate: true,
        isShowHeader: true,
        isShowFooter:false,
        isSelectTime:false,
        fixPatch:false
    }
);

const _tempValReactive = reactive({
    value:'',
    startTime:'',
    endTime:'',
    isCustomeTime:false
})
    nextTick(()=>{
    let footerHeight = 0
    let headerHeight = 0
    if(props.isShowHeader){
        const headerDom = headerDomRef.value as HTMLElement;
        headerHeight = headerDom.offsetHeight;
    }
    if(props.isShowFooter){
        const footerDom = footerDomRef.value as HTMLElement;
        footerHeight = footerDom.offsetHeight;
    }
    contentDomRef.value.style.height =  `calc(100% - ${headerHeight}px - ${footerHeight}px)`;
})


const emit = defineEmits<{
    (e:"changeDate",date:any):void
}>();

function onDateConfirm(valueList:Array<Date>){
    _tempValReactive.startTime = dayjs(valueList[0]).format('YYYY-MM-DD')
    _tempValReactive.endTime = dayjs(valueList[1]).format('YYYY-MM-DD')
    let start_time = dayjs(valueList[0]).format('YYYY-MM-DD 00:00:00')
    let end_time = dayjs(valueList[1]).format('YYYY-MM-DD 23:59:59')
    calendarSelectShowRef.value = false
    _tempValReactive.isCustomeTime = true
    let date = {
        value:'11',
        startTime:start_time,
        endTime:end_time
    }
    emit('changeDate', date);
}
function handlerSelectDate(date){
    searchDateName.value = date.label || date.name;
    if(searchDateName.value == '自定义'){
        wrapperRef.value.style.height =  `calc(100% - 52px - 38px)`;
        timeSelect.value = true;
        _tempValReactive.isCustomeTime = false
        _tempValReactive.startTime = dayjs(dataTimes['today'].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes['today'].end_time).format('YYYY-MM-DD')
        date.startTime =  _tempValReactive.startTime;
        date.endTime =  _tempValReactive.endTime;
    }else{
        timeSelect.value = false;
        emit('changeDate', date);
        wrapperRef.value.style.height =  `calc(100% - 52px)`;
    }
    // emit('changeDate', date);
}
const calendarMinDate = computed(()=>{
   return dayjs(_tempValReactive.startTime).startOf('day').add(-365,'day').valueOf()
})
watch(()=>props.fixPatch,(newVal)=>{
    if(newVal){
        nextTick(()=>{
            try{
                handlerSelectDate(patchOptions.all)
            }
            catch(e){

            }
            
        })
    }
},{immediate:true})
</script>

<style scoped lang="less">
.body{
    width: 100%;
    height: 100%;
}
.headerBox {
    background: url("@/assets/image/system/account/accountDetailBg.jpg");
    background-size: cover;
    background-position: center;
    box-sizing: border-box;
    width: 100%;
    padding: 24px 0px 0px;

    .header {
        // background-color: rgba(255, 255, 255, 0.6);
        // border-radius: 8px 8px 0px 0px;
    }
}

.centerContent {
    background: #fff;
    width: 100%;
    // height: 100%;
    border-radius: 16px 16px 0px 0px;
    // padding-bottom: 50px;
}

.selectDate {
    display: flex;
    padding: 16px 12px 12px 12px;
    border-radius: 4px;
    justify-content: space-evenly;
}

.dateTag {
    text-align: center;
    height: 24px;
    width: 64px;
    background: #F8F8F8;
    color: #999999;
    line-height: 24px;
    font-size: 12px;
    border-radius: 4px;
}

.active {
    background: #1677FF;
    color: #fff;
}

.content-wrapper {
    height: calc(100% - 52px);
    overflow: auto;
}
.footer-wrapper{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    box-sizing: border-box;
    background: #fff;
}
</style>