<template>
  <view class="address_wrapper">
    <view class="address_info_container">
      <!-- 图片 -->
      <image :src="addressSrc" alt="" class="address_info_container_img" />
      <view class="address_info">
        <view class="address_info_top">
          <text class="name">{{ addressInfoRef?.name ?? '-' }}</text>
          <text class="phone">{{ addressInfoRef?.mobile ?? '-' }}</text>
        </view>
        <p class="address">{{ addressStr }}</p>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from 'vue'
/** 静态资源 */
import addressSrc from '@/static/images/storeUser/address.png'

defineOptions({ name: 'StoreDeliveryInfo' })
/** props */
const props = defineProps<{
  addressInfo?: {
    id?: string
    name?: string
    mobile?: string
    companyId?: string
    company?: string
    provinceId?: string
    province?: string
    cityId?: string
    cityName?: string
    areaId?: string
    area?: string
    townId?: string
    town?: string
    address?: string
    isDefault?: 0 | 1
    csWxNickname?: string
  }
}>()

const { addressInfo: addressInfoRef } = toRefs(props)

const addressStr = computed(() => {
  return `${addressInfoRef.value?.province + addressInfoRef.value?.cityName + addressInfoRef.value?.area + addressInfoRef.value?.town + addressInfoRef.value?.address}`
})
</script>

<style lang="scss" scoped>
.address_wrapper {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  .address_info_container {
    flex: 1;
    display: flex;
    gap: 16rpx;
    .address_info_container_img {
      width: 64rpx;
      height: 64rpx;
    }
    .address_info {
      display: flex;
      flex-direction: column;
      gap: 8rpx;
      .address_info_top {
        display: flex;
        align-items: center;
        gap: 8rpx;
        .name,
        .phone {
          font-family:
            Source Han Sans CN,
            Source Han Sans CN;
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .address {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 36rpx;
        color: #666666;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
