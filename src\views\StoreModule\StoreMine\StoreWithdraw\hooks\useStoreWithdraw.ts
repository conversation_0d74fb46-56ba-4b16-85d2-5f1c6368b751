import { ref } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import { isArray } from "@/utils/isUtils";
import { uploadBase64ImgApi, storeWithdrawApi } from "@/services/storeApi";

export default function useStoreWithdraw() {
  const router = useRouter();
  const { createMessageError, createMessageSuccess } = useMessages();
  const isPageLoadingRef = ref(false);
  const isUploadingRef = ref(false);

  /** 表单参数 */
  const initparams = {
    /** 提现金额 */
    amount: null,
    /** 发票 */
    invoiceImageList: [],
    /** 收款账户ID：即银行卡信息ID */
    csBankInfoAccountId: null,
  };
  const formValue = ref({ ...initparams });
  const tempInvoiceImgVOList = ref([]);

  /** 文件读取完成后的回调函数 */
  async function handleAfterRead(file) {
    try {
      isUploadingRef.value = true;

      // 校验图片数量是否超过9张
      if (formValue.value.invoiceImageList.length >= 9) {
        createMessageError("最多只能上传9张发票");
        return;
      }

      if (isArray(file)) {
        // 如果是数组，遍历每个文件并依次上传
        for (const singleFile of file) {
          // 检查是否已达到9张限制
          if (formValue.value.invoiceImageList.length >= 9) {
            createMessageError("最多只能上传9张发票");
            break;
          }
          await handleSingleFile(singleFile);
        }
      } else {
        // 如果不是数组，直接处理单个文件
        await handleSingleFile(file);
      }
    } catch (error) {
      createMessageError("上传图片失败：" + error);
    } finally {
      isUploadingRef.value = false;
    }
  }

  /** 上传单个文件 */
  async function handleSingleFile(file) {
    try {
      file.status = "uploading";
      file.message = "上传中...";
      if (file.content) {
        const base64Data = file.content;
        // 去掉可能的数据URL前缀
        const pureBase64 = base64Data.replace(/^data:image\/\w+;base64,/, "");
        let _params = {
          fileName: removeExtension(file?.file?.name ?? "未知文件"),
          base64: pureBase64,
        };
        const imgFilePath = await uploadBase64ImgApi(_params);
        if (imgFilePath) {
          formValue.value.invoiceImageList.push({
            img: removeExtension(file?.file?.name ?? "未知文件"),
            path: imgFilePath,
          });
          file.status = "done";
        }
      }
    } catch (error) {
      file.status = "failed";
      file.message = "上传失败";
    }
  }

  /** 删除图片 */
  function handleDeleteUploader(field, { index }) {
    // 实际存储的文件数据中删除
    formValue.value.invoiceImageList.splice(index, 1);
  }

  function removeExtension(filename) {
    return filename.replace(/\.(png|jpg|jpeg|gif|webp)$/i, "");
  }

  function formatDecimal(value, max) {
    // 去除非数字和点的字符
    value = value.replace(/[^\d.]/g, "");

    // 处理以点开头的情况（禁止 ".1"、".01"）
    if (value.startsWith(".")) {
      value = "0" + value;
    }

    // 处理前导零（禁止 "01"、"002"，但允许 "0.1"、"0.01"）
    if (/^0+[1-9]/.test(value)) {
      // 情况："01" → "1"，"002" → "2"
      value = value.replace(/^0+/, "");
    } else if (/^0+\./.test(value)) {
      // 情况："000.5" → "0.5"（允许 0.xxx）
      value = "0" + value.slice(value.indexOf("."));
    } else if (/^0+$/.test(value)) {
      // 情况："000" → "0"（纯零保留一个）
      value = "0";
    }

    // 保留最多两位小数
    const parts = value.split(".");
    if (parts.length > 1) {
      parts[1] = parts[1].slice(0, 2);
      value = parts.join(".");
    }

    // 转换为数字进行范围检查
    const numValue = parseFloat(value || 0);

    if (numValue >= max) {
      return max.toString();
    }

    // 返回处理后的值
    return value;
  }

  /** 验证表单 */
  function validateForm() {
    // 1. 提现金额必填
    if (!formValue.value.amount || formValue.value.amount === 0) {
      showToast("请输入提现金额");
      return false;
    }
    // 2. 收款账户必填
    if (!formValue.value.csBankInfoAccountId) {
      showToast("请添加银行卡");
      return false;
    }

    return true;
  }

  /** 获取提现参数 */
  function getWithdrawParams() {
    return {
      amount: Number(formValue.value.amount) * 100,
      csBankInfoAccountId: formValue.value.csBankInfoAccountId,
      invoiceImageList: formValue.value.invoiceImageList.map(item => item.path),
    };
  }

  /** 确认提现 */
  async function handleComfirmWithdraw() {
    try {
      // 先验证表单
      if (!validateForm()) {
        return;
      }
      if (isUploadingRef.value) {
        showToast("图片上传中，请稍后");
        return;
      }

      let _params = getWithdrawParams();
      await storeWithdrawApi(_params);
      showToast("申请已提交，等待审核");

      router.back();
    } catch (error) {
      createMessageError("提现失败：" + error);
    }
  }

  return {
    formValue,
    isPageLoadingRef,
    isUploadingRef,
    tempInvoiceImgVOList,
    handleAfterRead,
    handleDeleteUploader,
    formatDecimal,
    handleComfirmWithdraw,
  };
}
