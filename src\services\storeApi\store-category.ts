import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
const enum ProductApiEnum {
  detail = "/applet/product/manage/detail",
  countPrice = "/applet/cartItem/countPrice",
  category = "/applet/product/cate/list",
  page = "/applet/product/manage/storesProductPage",
  dosageForm = "/product/manage/get/dosageForm",
}
//积分商品
const enum IntegralGoodsEnum {
  page = "/applet/pointProduct/page/store/search",
  pointProduct = "/applet/pointProduct/get",
  pointSift = '/applet/pointSift/list',
  getAvailPoints = '/applet/pointRecord/getAvailPoints',
}
//福利卷
const enum WelfareEnum {
  cate = "/applet/couponProductCate/list",
  detail = "/applet/couponProduct/detail",
  pageSearch = "/applet/couponProduct/pageSearch",
  page = "/applet/couponProduct/page",
}

//商品详情
export async function queryGoodsDetail(id: string) {
  return defHttp.get({
    url: getStoreApiUrl(ProductApiEnum.detail),
    params: { id },
  });
}
//积分商品详情
export async function queryIntergralDetail(pointProductId: string) {
  return defHttp.get({
    url: getStoreApiUrl(IntegralGoodsEnum.pointProduct),
    params: { pointProductId },
  });
}
export async function queryCategoryList(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(ProductApiEnum.category),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}

export async function queryGoodslist(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(ProductApiEnum.page),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}


export async function queryDosageForm(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(ProductApiEnum.dosageForm),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/**分页条件查询积分商品 */
export async function queryIntergralList(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(IntegralGoodsEnum.page),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}
/**分页条件查询积分商品 */
export async function queryPointSiftList(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(IntegralGoodsEnum.pointSift),
    params,
  });
}
/**获取积分 */
export async function getAvailPoints(params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(IntegralGoodsEnum.getAvailPoints),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}
export async function queryWelfareSearch(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(WelfareEnum.pageSearch),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}


/**分页条件福利券商品 */
export async function queryWelfarePage(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(WelfareEnum.page),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/**福利商品分类列表 */
export async function queryWelfareCate(params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(WelfareEnum.cate),
    params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/**福利券商品详情 */
export async function queryWelfareDetail(id:string) {
  return defHttp.post({
    url: getStoreApiUrl(WelfareEnum.detail),
    params: { data:id },
    requestConfig:{
      skipCrypto: true,
    }
  });
}
