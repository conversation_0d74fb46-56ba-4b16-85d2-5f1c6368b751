<template>
  <view
    class="text-16rpx p-x-8rpx p-y-4rpx rounded-8rpx border-2rpx border-solid border-#eeeeee whitespace-nowrap"
    :class="`${props.type}-active`"
    :style="{ ...props.customStyle }"
  >
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
defineOptions({ name: 'JTag' })

type TagType = 'primary' | 'success' | 'error' | 'default'
const props = withDefaults(
  defineProps<{
    customStyle?: object
    type?: TagType
  }>(),
  {
    customStyle: () => ({}),
    type: 'primary',
  },
)
</script>

<style lang="scss" scoped>
.primary-active {
  border-color: var(--primary-color);
  background-color: var(--primary-color-fill-color);
  color: var(--primary-color);
}

.success-active {
  border-color: var(--success-color);
  background-color: var(--success-color-fill-color);
  color: var(--success-color);
}

.error-active {
  border-color: var(--error-color);
  background-color: var(--error-color-fill-color);
  color: var(--error-color);
}

.default-active {
  border-color: transparent;
  background-color: #f8f8f8;
  color: #000000;
}
</style>
