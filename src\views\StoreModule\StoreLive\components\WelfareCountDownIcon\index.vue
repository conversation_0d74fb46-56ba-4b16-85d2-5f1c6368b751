<template>
  <div
    class="welfare-btn"
    v-bind="$attrs"
    v-if="show"
  >
    <div class="welfare-count-down-box" v-if="props.type == 'countDown'" @click="handleClick('countDown')">
      <div class="timer" v-if="countDownTimeSecond && countDownTimeSecond > 0">{{ formattedCountDownTime }}</div>
      <div class="text" v-else>{{ props.endTimeText || "" }}</div>
    </div>
    <div class="welfare-box" v-if="props.type == 'normal'" @click="handleClick('normal')">
      <img :src="welfareIconBtn" alt="" srcset="" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted, watch, type Ref } from "vue";
import welfareIconBtn from "@/assets/store0602Image/welfareIconBtn.png";
import { useUserStore } from "@/stores/modules/user";
import type { LiveCouponInfoResponse } from "@/services/storeApi";
import { isNullOrUnDef } from "@/utils/isUtils";

type BtnType = "normal" | "countDown"
const props = withDefaults(defineProps<{
  show: boolean,
  /* 倒计时开始时间 */
  countDownTime: number | null,
  /* 倒计时结束时的文本 */
  endTimeText: string,
  /* 挂件类型 */
  type: BtnType,
  liveCouponData: LiveCouponInfoResponse
}>(), {
  countDownTime: null,
  endTimeText: "已领取",
  type: "normal",
  show: false,
});


const emits = defineEmits<{
  "click": [
    type: BtnType,
    countDownTimeSecond: Ref<number>,
  ],
  "countDownEnd": [
    liveCouponInfo: LiveCouponInfoResponse
  ],
}>();

const userStore = useUserStore();
// 剩余的总秒数
const countDownTimeSecond = ref(props.countDownTime ?? 0);
let intervalId = null;

// 开始倒计时的方法
const startCountDown = () => {
  if (intervalId) clearInterval(intervalId);

  if (countDownTimeSecond.value > 0) {
    intervalId = setInterval(() => {
      if (countDownTimeSecond.value > 0) {
        countDownTimeSecond.value--;
      } else {
        // 倒计时结束后执行的操作
        clearInterval(intervalId);
        emits("countDownEnd", props.liveCouponData);
      }
    }, 1000);
  }
};

// 格式化倒计时时间为 xx分:xx秒
const formattedCountDownTime = computed(() => {
  const minutes = Math.floor(countDownTimeSecond.value / 60);
  const seconds = countDownTimeSecond.value % 60;
  return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
});

onMounted(() => {
  if (!props.countDownTime || props.countDownTime <= 0) {
    return;
  }
  // 已领取的时长福利券，无需显示倒计时
  if (props.liveCouponData.receiveFlag == 1) {
    countDownTimeSecond.value = 0;
    return;
  }
  const _userStoreInfoCache = userStore.storeUserInfo;
  let _liveWatchTime = Number(_userStoreInfoCache.liveWatchTime ?? 0);
  if (_liveWatchTime >= props.countDownTime) {
    countDownTimeSecond.value = 0;
    return;
  } else {
    countDownTimeSecond.value = props.countDownTime - _liveWatchTime;
    startCountDown();
  }
});

/* 组件卸载时清除定时器 */
onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId);
  }
});

const handleClick = (type: BtnType) => {
  emits("click", type, countDownTimeSecond);
};
</script>

<style scoped lang="less">
.welfare-btn {
  color: #FFF9DD;
  font-size: 10px;
  position: fixed;
  z-index: 100;
  overflow: hidden;


  .welfare-count-down-box {
    width: 50px;

    background-size: 100% auto;
    background-repeat: no-repeat;
    background-image: url("@/assets/store0602Image/welfareCountDownBtn.png");
    position: relative;
    padding-bottom: 100%;

    .timer, .text {
      position: absolute;
      box-sizing: border-box;
      padding: 0 4px;
      bottom: 6px;
      text-align: center;
      width: 46px;
      height: 14px;
      font-weight: 500;
      font-size: 9px;
      color: #FFF9DD;
      line-height: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .welfare-box {
    width: 50px;
    height: 50px;

    img {
      width: 100%;
      height: 100%
    }
  }
}
</style>
