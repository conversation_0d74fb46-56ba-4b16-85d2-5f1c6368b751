import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 组织申请 */
export const enum storeSignupApiEnum {
  // 门店端-添加组织申请
  addStoreSignup = "/h5/structureApply/add",
  // 生成组织架构申请二维码
  getStructureApply = "/H5/QrCode/getStructureApply",
  // 门店端-扫组织申请码后回显申请记录
  getStructureApplyInfo = "/h5/structureApply/st/signup",
}

/**
 * @description 门店端-添加组织申请
 */
export function addStoreSignupApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeSignupApiEnum.addStoreSignup),
    params: { data: _params },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 生成组织架构申请二维码
 */
export function getStructureApplyApi(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeSignupApiEnum.getStructureApply),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 门店端-扫组织申请码后回显申请记录
 */
export function getStructureApplyInfoApi(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeSignupApiEnum.getStructureApplyInfo),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}