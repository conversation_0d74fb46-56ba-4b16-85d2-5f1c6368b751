import { ref } from "vue"
import { MessageEventEnum, useWindowMessage } from "./useWindowMessage"

const dpDomainRef = ref<string>('')
const {pushEventMap} = useWindowMessage()


export function useSystem(){
    function init(){
        pushEventMap(MessageEventEnum.getDpDomain,(resp)=>{
            if(resp && resp.result == 'success'){
                dpDomainRef.value = resp.resp || ''
            }
        })
    }
    return {
        init,
        dpDomainRef
    }
}