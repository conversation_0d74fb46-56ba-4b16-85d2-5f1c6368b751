<template>
    <div class="store_order_info">
        <div class="store_order_info_title">订单信息</div>
        <!-- 订单编号 -->
        <div class="store_order_info_item">
            <span class="store_order_info_item_label">订单编号</span>
            <span class="store_order_info_item_value">{{ orderInfoRef?.code ?? `-` }}</span>
        </div>
        <!-- 微信昵称 -->
        <!-- <div class="store_order_info_item">
            <span class="store_order_info_item_label">微信昵称</span>
            <span class="store_order_info_item_value">-</span>
        </div> -->
        <!-- 创建时间 -->
        <div class="store_order_info_item">
            <span class="store_order_info_item_label">创建时间</span>
            <span class="store_order_info_item_value">{{ orderInfoRef?.createTime ?? `-` }}</span>
        </div>
        <!-- 支付时间 -->
        <div class="store_order_info_item">
            <span class="store_order_info_item_label">支付时间</span>
            <span class="store_order_info_item_value">{{ orderInfoRef?.payTime ?? `-` }}</span>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { toRefs } from "vue";
import { StoreOrderTypeEnum, StoreOrderStatusEnum, StoreOrderPayTypeEnum, StoreOrderPayStatusEnum } from "@/views/StoreModule/enums";

defineOptions({ name: 'StoreOrderInfo' });

/** props */
const props = defineProps<{
    orderInfo: {
        type?: StoreOrderTypeEnum;
        status?: StoreOrderStatusEnum;
        code?: string;
        pickupType?: number;
        money?: number;
        goodsAmount?: number;
        shippingFee?: number;
        payType?: StoreOrderPayTypeEnum;
        payStatus?: StoreOrderPayStatusEnum;
        afterSaleState?: number;
        payTime?: string;
        createTime?: string;
        orderItemDTOList?: Array<{
            type?: 1 | 2 | 3;
            orderId?: string;
            productImgPath?: string;
            productFrontName?: string;
            specName?: string;
            price?: number;
            count?: number;
            exchangePoints?: number;
            exchangePrice?: number;
        }>;
    };
}>();

const { orderInfo: orderInfoRef } = toRefs(props);
</script>

<style lang="less" scoped>
.store_order_info {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 10px 12px;
    box-sizing: border-box;
    margin-bottom: 12px;
    .store_order_info_title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 12px;
    }
    .store_order_info_item {
        height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .store_order_info_item_label {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .store_order_info_item_value {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: right;
            font-style: normal;
            text-transform: none;
        }
    }
}
</style>