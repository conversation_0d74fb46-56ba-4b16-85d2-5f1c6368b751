<wxs src="./index.wxs" module="computed" />

<view class="van-picker custom-class">
  <include wx:if="{{ toolbarPosition === 'top' }}" src="./toolbar.wxml" />

  <view wx:if="{{ loading }}" class="van-picker__loading">
    <loading color="#1989fa"/>
  </view>

  <view
    class="van-picker__columns"
    style="{{ computed.columnsStyle({ itemHeight, visibleItemCount }) }}"
    catch:touchmove="noop"
  >
    <picker-column
      class="van-picker__column"
      wx:for="{{ computed.columns(columns) }}"
      wx:key="index"
      data-index="{{ index }}"
      custom-class="column-class"
      value-key="{{ valueKey }}"
      initial-options="{{ item.values }}"
      default-index="{{ item.defaultIndex || defaultIndex }}"
      item-height="{{ itemHeight }}"
      visible-item-count="{{ visibleItemCount }}"
      active-class="active-class"
      bind:change="onChange"
    />
    <view class="van-picker__mask" style="{{ computed.maskStyle({ itemHeight, visibleItemCount }) }}" />
    <view
      class="van-picker__frame van-hairline--top-bottom"
      style="{{ computed.frameStyle({ itemHeight }) }}"
    />
  </view>

  <include wx:if="{{ toolbarPosition === 'bottom' }}" src="./toolbar.wxml" />
</view>
