<template>
  <view class="store-address-wrapper">
    <image :src="storeAvatar" alt="门店头像" class="store-address-avatar" />
    <!-- 门店信息 -->
    <view class="store-info">
      <view class="store-name">
        <text class="store-name-text">{{ storeEntityRef?.storeName }}</text>
        <a
          class="member-phone"
          @click.stop
          :href="
            storeEntityRef?.contactPhone
              ? `tel:${storeEntityRef?.contactPhone}`
              : 'javascript:void(0)'
          "
        >
          <image :src="phoneSrc" alt="" class="store-name-ico" />
        </a>
      </view>
      <!-- 营业时间 -->
      <view class="store-time">
        <image :src="timeSrc" alt="" class="store-ico" />
        <text class="store-text">
          {{
            storeEntityRef?.businessHours
              ? `营业时间 ${storeEntityRef?.businessHours}`
              : '暂无营业时间'
          }}
        </text>
      </view>
      <!-- 地址 -->
      <view class="store-address">
        <image :src="addressSrc" alt="" class="store-ico" />
        <text class="store-text">{{ formattedAddress }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from 'vue'
/** 静态资源 */
import storeSrc from '@/static/images/storeUser/store.png'
import addressSrc from '@/static/images/storeHome/orderDetails/location.png'
import timeSrc from '@/static/images/storeHome/orderDetails/time.png'
import phoneSrc from '@/static/images/storeHome/orderDetails/phone.png'

defineOptions({ name: 'StoreAddress' })

/** props */
const props = defineProps<{
  storeEntity: {
    storeName: null
    storeAvatar: null
    province: ''
    city: ''
    area: ''
    addressDetail: ''
    contactPhone: null
    businessHours: null
  }
}>()

const { storeEntity: storeEntityRef } = toRefs(props)

/** 门店地址 */
const formattedAddress = computed(() => {
  const { province = '', city = '', area = '', addressDetail = '' } = storeEntityRef.value || {}
  const address = `${province}${city}${area}${addressDetail}`.trim()
  return address || '暂无地址'
})

/** 门店头像 */
const storeAvatar = computed(() => {
  const { storeAvatar } = storeEntityRef.value || {}
  return storeAvatar || storeSrc
})
</script>

<style lang="scss" scoped>
.store-address-wrapper {
  width: 100%;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  margin-bottom: 16rpx;
  .store-address-avatar {
    width: 112rpx;
    height: 112rpx;
    border-radius: 8rpx;
  }
  .store-info {
    margin-left: 16rpx;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
    .store-name {
      display: flex;
      align-items: center;
      gap: 8rpx;
      .store-name-text {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 40rpx;
      }
      .store-name-ico {
        width: 36rpx;
        height: 36rpx;
      }
    }
    .store-time,
    .store-address {
      display: flex;
      align-items: center;
      gap: 8rpx;
      .store-ico {
        width: 32rpx;
        height: 32rpx;
      }
      .store-text {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
