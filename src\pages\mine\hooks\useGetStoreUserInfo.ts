import { ref } from 'vue'
import { StoreUserTypeEnum } from '@/enum'
import { getStoreUserInfo } from '@/services/api'

interface StoreMemberInfo {
  appId?: string
  createTime?: string
  fromType?: number
  id?: string
  shortId?: string
  img?: string
  isDistributor?: number
  isEnableComment?: number
  isVirtualAccount?: number
  lastLoginTime?: string
  nickname?: string
  openId?: string
  status?: number
  type?: StoreUserTypeEnum
  unionId?: string
  updateTime?: string
}

/** 用户Id */
const customerIdRef = ref('')
const storeMemberInfo = ref<StoreMemberInfo>({})

export default function useGetStoreUserInfo() {
  /** 获取门店信息 */
  async function getStoreMemberInfo() {
    try {
      const resp = await getStoreUserInfo()
      if (resp) {
        storeMemberInfo.value = resp
        customerIdRef.value = resp.id
      }
    } catch (error) {
      console.log('获取门店信息' + error)
    }
  }

  return {
    getStoreMemberInfo,
    storeMemberInfo,
    customerIdRef,
  }
}
