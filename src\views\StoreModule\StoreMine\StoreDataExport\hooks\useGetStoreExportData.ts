import { ref } from "vue";
import { uuid } from "@/views/StoreModule/utils";
import { StoreDataExportEnum } from "@/views/StoreModule/enums";

export default function useGetStoreExportData() {
  const isPageLoadingRef = ref(false);

  /** 数据导出 */
  const storeExportDataList = ref([
    {
      id: uuid(),
      type: StoreDataExportEnum.A01_MEMBER_DATA,
      title: "A01-导出会员数据",
      description: "会员数据包含会员积分数据",
    },
    {
      id: uuid(),
      type: StoreDataExportEnum.A02_MEMBER_CONSUMPTION,
      title: "A02-导出会员消费数据",
      description: "在此时间段内支付成功的所有订单，不包含退款订单",
    },
    {
      id: uuid(),
      type: StoreDataExportEnum.B01_COUPON_CLAIM_STATS,
      title: "B01-导出福利券领取时间统计数据",
      description: "需提前知晓福利券分类id",
    },
    {
      id: uuid(),
      type: StoreDataExportEnum.B02_COUPON_USE_STATS,
      title: "B02-导出福利券使用时间统计数据",
      description: "需提前知晓福利券分类id",
    },
    {
      id: uuid(),
      type: StoreDataExportEnum.C01_DAILY_VIEWING_TIME,
      title: "C01-导出会员每日观看时长数据",
      description: "在此时间段内的会员观看时长数据",
    },
    {
      id: uuid(),
      type: StoreDataExportEnum.D01_PAID_ORDERS_WITHOUT_REFUNDS,
      title: "D01-导出已支付不含退款订单数据",
      description: "在此时间段内支付成功的所有订单，不包含退款订单",
    },
    {
      id: uuid(),
      type: StoreDataExportEnum.D02_PAID_ORDERS_WITH_REFUNDS,
      title: "D02-导出已支付包含退款订单数据",
      description: "在此时间段内支付成功的所有订单，包含退款订单",
    },
    {
      id: uuid(),
      type: StoreDataExportEnum.D03_REFUNDED_ORDERS,
      title: "D03-导出已退款订单数据",
      description: "在此时间段内的已退款订单",
    },
  ]);

  return {
    isPageLoadingRef,
    storeExportDataList,
  };
}
