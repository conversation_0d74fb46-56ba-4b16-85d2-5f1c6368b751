<template>
  <VanPullRefresh v-model="isRefresh" @refresh="onRefresh" :disabled="isPullRefreshDisabled">
    <JLoadingWrapper :show="isPageLoadingRef">
      <div class="store_mine_wrapper" @scroll="onScroll">
        <!-- 用户信息 -->
        <div class="store_mine_user_info">
          <VanImage
            round
            :width="isSamllMobile ? 48 : 68"
            :height="isSamllMobile ? 48 : 68"
            fit="contain"
            :src="storeMemberInfo?.img || defaultAvatar"
            class="store_mine_user_avatar"
          />
          <div class="store_mine_user_info_right">
            <div class="store_mine_user_info_right_name">
              <span>{{ storeMemberInfo?.nickname }}</span>
              <!-- 店长标识 -->
              <div v-if="isStoreOwner" class="store_mine_user_info_owner">店长</div>
              <!-- 店员标识 -->
              <div v-if="isStoreStaff" class="store_mine_user_info_staff">店员</div>
            </div>
            <div class="store_mine_user_info_right_serial">
              {{ `用户编号：${storeMemberInfo?.shortId ?? '-'}` }}
              <img :src="copySrc" alt="" @click.stop="handleCopyID(storeMemberInfo?.shortId)" />
            </div>
          </div>
        </div>
        <!-- 用户福利券信息 -->
        <div class="store_mine_user_coupon_info">
          <div class="store_mine_user_coupon_info_item" @click="handleToCoupon">
            <div class="store_mine_user_coupon_info_item_name">福利券(张)</div>
            <div class="store_mine_user_coupon_info_item_num">{{ storeMineCouponAndPointsRef.unUseNum }}</div>
          </div>
          <div class="store_mine_user_coupon_info_item" @click="handleToIntegral">
            <div class="store_mine_user_coupon_info_item_name">积分余额</div>
            <div class="store_mine_user_coupon_info_item_num">{{ storeMineCouponAndPointsRef.availPoints }}</div>
          </div>
        </div>
        <!-- 门店地址 -->
        <StoreAddress
          :storeInfo="storeInfo"
          style="background: linear-gradient(180deg,#FFF3EA 0%,#FFFFFF 100%);margin-bottom: 12px;"
        />
        <!-- 我的订单 -->
        <BannerContainer style="margin-bottom: 12px;">
          <template #title>
            <span class="title">我的订单</span>
          </template>
          <template #headerRight>
            <div class="header-right" @click="handleToOrderList(OrderStatusEnum.ALL)">
              <span>全部</span>
              <img :src="headerRightSrc" alt="" />
            </div>
          </template>
          <div class="store_mine_order" style="justify-content: space-between;">
            <div
              v-for="item in mineOrderOptions"
              :key="item.key"
              class="store_mine_order_item"
              style="width: 25%;"
              @click="item.onClick"
            >
              <VanBadge :content="item.value" max="99" :show-zero="false" color="#EF1115">
                <img :src="item.icon" alt="" />
              </VanBadge>
              <span>{{ item.label }}</span>
            </div>
          </div>
        </BannerContainer>
        <!-- 店铺管理 -->
        <BannerContainer v-if="isStoreOwner || isStoreStaff" style="margin-bottom: 12px;">
          <template #title>
            <div style="display: flex;align-items: center;gap: 6px;">
              <span class="title">店铺管理</span>
              <StoreSelectOrderType v-model:value="storeOrderTypeRef" />
            </div>
          </template>
          <template #headerRight>
            <StoreSmallSelectTime v-if="isSamllMobile" v-model:value="storeSelectTimeRef" />
            <StoreSelectTime v-else v-model:value="storeSelectTimeRef" />
          </template>
          <!-- 内容 -->
          <div class="store_management_container">
            <!-- 数据概览 -->
            <div class="store_management_container_data">
              <template v-for="item in orderOverview" :key="item.key">
                <div class="store_management_container_data_item">
                  <span class="data_title">{{ item.label }}</span>
                  <span 
                    class="data_num"
                    :class="getSizeClass(item.value)"
                  >
                    {{ item.value }}
                  </span>
                </div>
                <!-- 竖线 -->
                <div class="store_management_container_data_line"></div>
              </template>
            </div>
            <!-- 选项 -->
            <div class="store_mine_management">
              <template v-for="item in storeManagementOptions" :key="item.key">
                <div v-if="item.isShow" class="store_mine_management_item" style="width: 25%;" @click="item.onClick">
                  <VanBadge :content="item.value" max="99" :show-zero="false" color="#EF1115">
                    <img :src="item.icon" alt="" />
                  </VanBadge>
                  <span>{{ item.label }}</span>
                </div>
              </template>
            </div>
          </div>
        </BannerContainer>
        <!-- 区域管理 -->
        <BannerContainer v-if="isAreaManager" style="margin-bottom: 12px;">
          <template #title>
            <span class="title">区域管理</span>
          </template>
          <div class="store_mine_district">
            <div
              v-for="item in mareaManagementOptions"
              :key="item.key"
              class="store_mine_district_item"
              style="width: 25%;"
              @click="item.onClick"
            >
              <img :src="item.icon" alt="" />
              <span>{{ item.label }}</span>
            </div>
          </div>
        </BannerContainer>
        <!-- 经销商管理 -->
        <BannerContainer v-if="isDistributor" style="margin-bottom: 12px;">
          <template #title>
            <span class="title">经销商管理</span>
          </template>
          <div class="store_mine_district">
            <div
              v-for="item in dealerManagementOptions"
              :key="item.key"
              class="store_mine_district_item"
              style="width: 25%;"
              @click="item.onClick"
            >
              <img :src="item.icon" alt="" />
              <span>{{ item.label }}</span>
            </div>
          </div>
        </BannerContainer>
        <!-- 更多 -->
        <BannerContainer style="margin-bottom: 12px;">
          <template #title>
            <span class="title">更多</span>
          </template>
          <!-- <template #headerRight>
          <div class="header-right">
            <span>全部</span>
            <img :src="headerRightSrc" alt="" />
          </div>
        </template> -->
          <div class="store_mine_more">
            <div
              v-for="item in moreOptions"
              :key="item.key"
              class="store_mine_more_item"
              style="width: 25%;"
              @click="item.onClick"
            >
              <img :src="item.icon" alt="" />
              <span>{{ item.label }}</span>
            </div>
          </div>
        </BannerContainer>
      </div>
      <!-- 会员邀请码 -->
      <StoreMemberCode v-model:show="isShowStoreMemberCodeRef" :qrCodeUrl="qrCodeUrlRef" />
    </JLoadingWrapper>
  </VanPullRefresh>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onActivated, nextTick } from "vue";
import { showToast } from "vant";
import { useRouterUtils, useUserRole, useGetStoreInfo } from "@/views/StoreModule/hooks";
import { copyText } from "@/utils/clipboardUtils";
import { useBoolean } from "@/views/StoreModule/hooks";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { useUserStore } from '@/stores/modules/user';
import useStoreManagement from "./hooks/useStoreManagement";
import useGetStoreUserInfo from "./hooks/useGetStoreUserInfo";
import {
  OrderStatusEnum,
  StoreCouponRouteTypeEnum,
  StoreIntegralRouteTypeEnum,
  StoreUserTypeEnum,
  StoreAddressRouteTypeEnum,
  StoreFunctionEnum,
  KeepAliveRouteNameEnum
} from "@/views/StoreModule/enums";
import { RoutesName } from "@/enums/routes";
import { getMyOrderStat, getMyCouponAndIntegral } from "@/services/storeApi";
/** 相关静态资源 */
import defaultAvatar from "@/assets/image/system/account/defaultAvatar.jpg";
import copySrc from "@/assets/storeImage/storeMine/copy.png";
import headerRightSrc from "@/assets/storeImage/storeMine/headerRight.png";
import WAIT_PAY from "@/assets/storeImage/storeMine/WAIT_PAY.png";
import WAIT_DELIVER from "@/assets/storeImage/storeMine/WAIT_DELIVER.png";
import WAIT_RECEIVE from "@/assets/storeImage/storeMine/WAIT_RECEIVE.png";
import REFUND from "@/assets/storeImage/storeMine/REFUND.png";
import couponStoreSrc from "@/assets/storeImage/storeMine/couponStore.png";
import integralStoreSrc from "@/assets/storeImage/storeMine/pointStore.png";
import settingSrc from "@/assets/storeImage/storeMine/setting.png";
import addressIconSrc from "@/assets/storeImage/storeMine/address-icon.png";
import walletIconSrc from "@/assets/storeImage/storeMine/icon-wallet.png";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreAddress from "@/views/StoreModule/components/StoreAddress.vue";
import BannerContainer from "@/views/StoreModule/components/BannerContainer.vue";
import StoreSelectTime from "./components/StoreSelectTime.vue";
import StoreSelectOrderType from "./components/StoreSelectOrderType.vue";
import StoreSmallSelectTime from "./components/StoreSmallSelectTime.vue";
import StoreMemberCode from "./components/StoreMemberCode.vue";

defineOptions({ name: KeepAliveRouteNameEnum.STORE_MINE });

const { bool: isRefresh, setTrue: refresh, setFalse: unRefresh } = useBoolean();
const isPullRefreshDisabled = ref(false); // 是否禁用下拉刷新
const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom, _findIndex } = useKeepAliveRoute();
const { routerPushByRouteName } = useRouterUtils();
const { storeId, staffId } = useUserRole();
const { storeInfo, getStoreInfoByStoreId } = useGetStoreInfo();
const userStore = useUserStore();
const { getStoreMemberInfo, storeMemberInfo } = useGetStoreUserInfo();
const {
  storeManagementOptions,
  mareaManagementOptions,
  dealerManagementOptions,
  orderOverview,
  isShowStoreMemberCodeRef,
  qrCodeUrlRef,
  isStoreOwner,
  isStoreStaff,
  isStoreMember,
  isAreaManager,
  isDistributor,
  isStore,
  storeSelectTimeRef,
  storeOrderTypeRef,
  isPageLoadingRef,
  getOrderOverview,
  getStoreAfterSaleOrderCount,
} = useStoreManagement();
/** 个人中心 */
const storeMineCouponAndPointsRef = ref({
  availPoints: 0,
  unUseNum: 0,
});

const isSamllMobile = computed(() => {
  if (window.innerWidth <= 320) {
    return true;
  }
  return false;
});

/** 复制订单ID */
function handleCopyID(data){
  try{
    copyText(data);
    showToast('复制成功！');
  }
  catch(e){
    showToast('复制失败！');
  }
};

/** 我的订单 */
const mineOrderOptions = ref([
  {
    label: '待付款',
    key: OrderStatusEnum.WAIT_PAY,
    icon: WAIT_PAY,
    fieldName: 'pendingPaymentCount',
    value: 0,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreMyOrders, { orderType: OrderStatusEnum.WAIT_PAY });
    }
  },
  {
    label: '待发货/提货',
    key: OrderStatusEnum.WAIT_DELIVER,
    icon: WAIT_DELIVER,
    fieldName: 'toBeShippedCount',
    value: 0,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreMyOrders, { orderType: OrderStatusEnum.WAIT_DELIVER });
    }
  },
  {
    label: '待收货',
    key: OrderStatusEnum.WAIT_RECEIVE,
    icon: WAIT_RECEIVE,
    fieldName: 'toBeReceivedCount',
    value: 0,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreMyOrders, { orderType: OrderStatusEnum.WAIT_RECEIVE });
    }
  },
  {
    label: '退款/售后',
    key: OrderStatusEnum.REFUND,
    icon: REFUND,
    fieldName: 'afterSale',
    value: 0,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreMyOrders, { orderType: OrderStatusEnum.REFUND });
    }
  },
]);

function handleToOrderList(orderType?: OrderStatusEnum) {
  routerPushByRouteName(RoutesName.StoreMyOrders, { orderType })
}

/** 跳转我的福利券 */
function handleToCoupon() {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
  routerPushByRouteName(RoutesName.StoreCoupon, { type: StoreCouponRouteTypeEnum.MY_COUPON, userId: storeMemberInfo.value?.id ?? null});
}

function handleToIntegral() {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
  routerPushByRouteName(RoutesName.StoreIntegral, { type: StoreIntegralRouteTypeEnum.MY_INTEGRAL});
}

/** 更多 */
const moreOptions = [
  {
    label: '福利券商城',
    key: 'coupon',
    icon: couponStoreSrc,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreWelfareVoucherMall);
    }
  },
  {
    label: '积分商城',
    key: 'integral',
    icon: integralStoreSrc,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreIntegralMall);
    }
  },
  {
    label: '收货地址',
    key: 'address',
    icon: addressIconSrc,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreAddress, { routeType: StoreAddressRouteTypeEnum.MY_ADDRESS });
    }
  },
  {
    label: '钱包',
    key: 'wallet',
    icon: walletIconSrc,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreWallet);
    }
  },
  {
    label: '账号设置',
    key: 'setting',
    icon: settingSrc,
    onClick: () => {
      pushKeepAliveRoute(KeepAliveRouteNameEnum.STORE_MINE);
      routerPushByRouteName(RoutesName.StoreAccountSetting);
    }
  },
];

/** 字体大小 */
const getSizeClass = (value) => {
  const num = Number(value);
  if (num >= ********) return 'size-ssm';
  if (num >= 1000000) return 'size-sm';
  if (num >= 100000) return 'size-md';
  if (num >= 10000) return 'size-lg';
  return 'size-xl';
};

/** 获取我的订单状态数量 */
async function getOrderStatusCount() {
  try {
    const resp = await getMyOrderStat();

    mineOrderOptions.value.forEach(option => {
      if (resp && resp.hasOwnProperty(option.fieldName)) {
        option.value = resp[option.fieldName];
      }
    });

  } catch (error) {
    console.error("获取订单状态数量失败: ", error);
  }
}

/** 我的-福利券张数&积分余额 */
async function queryMyCouponAndIntegral() {
  try {
    const resp = await getMyCouponAndIntegral();
    if (resp) {
      Object.assign(storeMineCouponAndPointsRef.value, resp);
    }
  } catch (error) {
    console.log("获取我的福利券张数和积分余额失败: ", error);
  }
}

/** 滚动触发 */
function onScroll(event: Event) {
  const target = event.target as HTMLElement;
  const scrollTop = target.scrollTop;
  isPullRefreshDisabled.value = scrollTop > 10;

  scrollEventHandler(event, KeepAliveRouteNameEnum.STORE_MINE);
}

/** 页面下拉刷新 */
async function onRefresh() {
  refresh();
  await onInit();
  showToast('刷新成功');
  unRefresh();
}

/** 页面初始化 */
async function onInit() {
  try {
    /** 获取门店用户信息 */
    await getStoreMemberInfo();
    /** 获取门店信息 */
    await getStoreInfoByStoreId(storeId.value);
    /** 我的-福利券张数&积分余额 */
    await queryMyCouponAndIntegral();
    if(userStore.storeUserInfo.type != StoreUserTypeEnum.CUSTOMER){
      await getOrderOverview();
    }
    /** 店长 */
    if (userStore.storeUserInfo.type == StoreUserTypeEnum.OWNER) {
      await getStoreAfterSaleOrderCount();
    }
    await getOrderStatusCount();
  } catch (error) {
    console.log("页面初始化：" + error);
  }
}

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName('store_mine_wrapper')[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.STORE_MINE);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.STORE_MINE);
  });
});

/** 组件挂载 */
onMounted(async () => {
  isPageLoadingRef.value = true;
  await onInit();
  isPageLoadingRef.value = false;
});
</script>

<style lang="less" scoped>
.store_mine_wrapper {
  width: 100%;
  height: 100%;
  background: url("@/assets/storeImage/storeMine/storeMineBg.png") no-repeat center;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  padding: 12px;
  box-sizing: border-box;
  overflow-y: auto;
  .store_mine_user_info {
    display: flex;
    align-items: center;
    padding: 12px 8px;
    gap: 8px;
    .store_mine_user_avatar {
      border: 2px solid #FFFFFF;
    }
    .store_mine_user_info_right {
      display: flex;
      flex-direction: column;
      gap: 4px;
      .store_mine_user_info_right_name {
        height: 26px;
        display: flex;
        align-items: center;
        gap: 8px;
        span {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 18px;
          color: #FFFFFF;
          line-height: 26px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 26px;
        }
        .store_mine_user_info_owner {
          width: 40px;
          height: 22px;
          background: linear-gradient( 270deg, #1677FF 0%, #16C1FF 100%);
          border-radius: 4px;
          color: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 12px;
          color: #FFFFFF;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .store_mine_user_info_staff {
          width: 40px;
          height: 22px;
          background: linear-gradient( 270deg, #FF8922 0%, #FFE416 100%);
          border-radius: 4px;
          color: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 12px;
          color: #FFFFFF;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .store_mine_user_info_right_serial {
        height: 22px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 22px;
        display: flex;
        align-items: center;
        gap: 4px;
        img {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
  .store_mine_user_coupon_info {
    width: 96%;
    height: 76px;
    min-height: 76px;
    background: url("@/assets/storeImage/storeMine/storeCouponBg.png") no-repeat center;
    background-size: 100% 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .store_mine_user_coupon_info_item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .store_mine_user_coupon_info_item_name {
        height: 22px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 22px;
      }
      .store_mine_user_coupon_info_item_num {
        height: 24px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 24px;
        text-align: right;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
      }
    }
  }
  .title {
    height: 24px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    line-height: 24px;
  }
  .header-right {
    display: flex;
    align-items: center;
    span {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #666666;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    img {
      width: 14px;
      height: 14px;
    }
  }
  .store_management_container {
    display: flex;
    flex-direction: column;
    .store_management_container_data {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      margin: 12px 0px 6px 0px;
      box-sizing: border-box;
      border-radius: 4px;
      box-shadow: rgba(0, 0, 0, 0.1) 0px -3px 3px 0px;
      .store_management_container_data_item {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        min-width: 80px;
        height: 68px;
        padding: 0px 6px;
        box-sizing: border-box;
        .data_title {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .data_num {
          height: 28px;
          font-family: DIN Pro, DIN Pro;
          font-weight: 500;
          font-size: 18px;
          color: #333333;
          line-height: 28px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 28px;
        }
        .size-ssm { font-size: 11px; } 
        .size-sm { font-size: 12px; } 
        .size-md { font-size: 13px; }
        .size-lg { font-size: 14px; }
        .size-xl { font-size: 18px; }
      }
      .store_management_container_data_line:not(:nth-child(8n)) {
        width: 2px;
        height: 28px;
        background: #EEEEEE;
      }
    }
  }
  .store_mine_district,
  .store_mine_more,
  .store_mine_management,
  .store_mine_order {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .store_mine_district_item,
    .store_mine_more_item,
    .store_mine_management_item,
    .store_mine_order_item {
      min-width: 0;
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 8px 0;
      cursor: pointer;
      &:hover {
        opacity: 0.9;
        background: #F5F5F5;
        transition: all 0.2s ease;
        border-radius: 8px;
      }
      :deep(.van-badge--top-right) {
        top: 6px;
        right: 2px;
      }
      img {
        width: 32px;
        height: 32px;
        object-fit: contain;
      }
      span {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}
</style>
