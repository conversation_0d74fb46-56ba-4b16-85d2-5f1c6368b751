<template>
  <view class="j-loading-wrapper">
    <!-- #ifdef MP-WEIXIN -->
    <van-overlay :show="props.show">
      <view class="loading-wrapper">
        <van-loading color="#1677FF" size="80rpx" />
      </view>
    </van-overlay>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <JOverlay :show="props.show">
      <view class="loading-wrapper">
        <JLoading color="#1677FF" size="80rpx" />
      </view>
    </JOverlay>
    <!-- #endif -->
    <view
      :class="[{ 'content-opacity': props.show }, 'j-loading-wrapper-content']"
      :id="props.contentId"
    >
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

defineOptions({
  name: 'JLoadingWrapper',
})

type JLoadingWrapperProps = {
  show: boolean
  contentId?: string
}

const props = withDefaults(defineProps<JLoadingWrapperProps>(), {
  show: false,
})
</script>

<style lang="scss" scoped>
.j-loading-wrapper {
  height: 100%;
  width: 100%;
}
.loading-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.j-loading-wrapper-content {
  position: relative;
  height: 100%;
  width: 100%;
  opacity: 1;
  transition: opacity 0.3s linear;
  overflow: auto;
  &.content-opacity {
    opacity: 0.3;
  }
}
</style>
