<template>
  <VanPopup
    :show="showRef"
    @update:show="handleUpdateShow"
    round
    position="bottom"
    closeable
    safe-area-inset-bottom
    close-icon="close"
    teleport="body"
    style="height: auto;"
  >
    <div class="wrapper">
      <!-- header -->
      <div class="header-container">
        <span class="title">使用记录</span>
      </div>
      <div class="store_code_usage_records_contianer">
        <div class="store_code_usage_records_title">已使用</div>
        <!-- 使用时间 -->
        <div class="store_order_info_item">
          <span class="store_order_info_item_label">使用时间</span>
          <span class="store_order_info_item_value">{{ orderVerificationRef?.verificationTime ?? '-'}}</span>
        </div>
        <!-- 使用门店 -->
        <div class="store_order_info_item">
          <span class="store_order_info_item_label">使用门店</span>
          <span class="store_order_info_item_value">{{ storeInfoRef?.storeName ?? '-' }}</span>
        </div>
        <!-- 使用券码 -->
        <div class="store_order_info_item">
          <span class="store_order_info_item_label">使用券码</span>
          <span class="store_order_info_item_value store_code_line">{{ orderVerificationRef?.code ?? '-'}}</span>
        </div>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed, watch } from "vue";
import { StoreOrderDetailVerifyStatusEnum } from "@/views/StoreModule/enums";
import { getStoreBasicInfo } from "@/services/storeApi";

defineOptions({ name: 'StoreCodeUsageRecords' });

/** props */
const props = defineProps<{
  show: boolean;
  orderVerification: {
    id: string;
    code: string; // 核销码
    status: StoreOrderDetailVerifyStatusEnum; // 核销状态
    verificationTime?: string; // 核销时间
    storeId?: string; // 店铺id
  }
}>();

/** emit */
const emit = defineEmits<{
  (e: 'update:show', val: boolean): void;
}>();

const { orderVerification: orderVerificationRef } = toRefs(props);

const showRef  = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

function handleUpdateShow(val: boolean) {
  emit('update:show', val);
}

/** 门店基础信息 */
const storeInfoRef = ref({
  storeName: null,
  storeAvatar: null,
  province: '',
  city: '',
  area: '',
  addressDetail: '',
  contactPhone: null
});

/** 获取门店基础信息 */
async function getStoreInfoByStoreId() {
  // TODO: 获取门店基础信息
  try {
    const _params = { id: orderVerificationRef.value?.storeId };
    const resp = await getStoreBasicInfo(_params);
    if (resp) {
      Object.assign(storeInfoRef.value, resp);
    }
  } catch (error) {
    console.log(error);
  }
};

/** 监听 */
watch(() => props.show, (val) => {
  if (val && orderVerificationRef.value?.storeId) {
    getStoreInfoByStoreId();
  }
});
</script>

<style lang="less" scoped>
.wrapper {
  padding: 8px 12px 24px 12px;
  display: flex;
  flex-direction: column;
  .header-container {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
  .store_code_usage_records_contianer {
    display: flex;
    flex-direction: column;
    gap: 20px;
    .store_code_usage_records_title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 26px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .store_order_info_item {
      height: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .store_order_info_item_label {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .store_order_info_item_value {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
      .store_code_line {
        text-decoration-line: line-through;
      }
    }
  }
}
</style>
