<template>
  <div class="exception_wrapper">
    <img :src="exceptionInfo.imgSrc" :alt="exceptionInfo.notice" />
    <p class="title">{{ exceptionInfo.notice }}</p>
    <p v-if="exceptionInfo.subContent" class="sub-content">
      {{ countdown }}{{ exceptionInfo.subContent }}
    </p>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, computed, onMounted, ref, onBeforeUnmount } from "vue";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { OrgApplyStatusEnum } from "@/views/StoreModule/enums";
import { clearStorage } from "@/utils/cache/storageCache";
import { storeRefreshUserInfo } from "@/views/StoreModule/utils"
/** 静态资源 */
import applySrc from '@/assets/storeImage/storeMine/apply.png';

defineOptions({
  name: "ApplyException",
});

interface ExceptionInfo {
  imgSrc: string;
  notice: string;
  subContent?: string;
}

/** props */
const props = withDefaults(defineProps<{
  code: string;
  customNotice?: string;
  auditStatus?: OrgApplyStatusEnum;
}>(), { code: "5001" });

/** emits */
const emit = defineEmits<{
  (e: "goBack"): void; // 返回
}>();

const { code: exceptionCodeRef, customNotice: customNoticeRef } = toRefs(props);
const { routerPushByRouteName } = useRouterUtils();

const countdown = ref(5); // 倒计时秒数
let timer: number | null = null; // 定时器

/** 动态计算 exceptionMap，确保 customNoticeRef 变化时能更新 */
const exceptionMap = computed(() => ({
  "5001": {
    imgSrc: applySrc,
    notice: "提交成功，请等待管理员审核~",
    subContent: ""
  },
  "5002": {
    imgSrc: applySrc,
    notice: "待审核中，请等待管理员审核~",
    subContent: ""
  },
  "5003": {
    imgSrc: applySrc,
    notice: "审核通过，请前往店铺管理页面查看~",
    subContent: "s后自动跳转到门店首页"
  },
  "5004": {
    imgSrc: applySrc,
    notice: "审核失败，请重新提交申请~",
    subContent: "s后自动跳转到信息填写页面"
  },
  "5005": {
    imgSrc: applySrc,
    notice: customNoticeRef.value || "审核失败，请重新提交申请~",
    subContent: ""
  },
  "5006": {
    imgSrc: applySrc,
    notice: "此账号已有绑定门店，即将跳转门店首页~",
    subContent: "s后自动跳转到门店首页"
  },
}));

/** 动态获取异常信息 */
const exceptionInfo = computed(() => {
  return exceptionMap.value[exceptionCodeRef.value] || exceptionMap.value["5001"];
});

/** 开始倒计时 */
const startCountdown = () => {
  timer = window.setInterval(async () => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearTimer();
      if (exceptionCodeRef.value == "5003") {
        // 刷新用户信息
        const resp = await storeRefreshUserInfo();
        if (resp) {
          routerPushByRouteName(RoutesName.StoreHome);
        } else {
          clearStorage();
          routerPushByRouteName(RoutesName.StoreLogin);
        }
      }
      if (exceptionCodeRef.value == "5004") {
        emit("goBack");
      }
      // 此账号已有绑定门店，即将跳转门店首页
      if (exceptionCodeRef.value == "5006") {
        routerPushByRouteName(RoutesName.StoreHome);
      }
    }
  }, 1000);
};

/** 清除定时器 */
const clearTimer = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
};

/** 审核通过后自动跳转 */
onMounted(() => {
  const codes = ["5003", "5004", "5006"];
  if (codes.includes(exceptionCodeRef.value)) {
    startCountdown();
  }
});

/** 组件卸载前清除定时器 */
onBeforeUnmount(() => {
  clearTimer();
});
</script>

<style lang="less" scoped>
.exception_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 108px;
  box-sizing: border-box;
  background-color: #F8F8F8;

  img {
    width: 200px;
    height: 200px;
  }

  .title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .sub-content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666;
    line-height: 20px;
    text-align: center;
    max-width: 80%;
    margin-top: 4px;
  }
}
</style>