<template>
    <div class="warpper">
        <JLoadingWrapper :show="isPageLoadingRef">
            <div class="searchHeader">
            <div class="screen" @click="changeSelect">
                <span>{{ selectType }}</span>
                <span class="search-icon"></span>
            </div>
            <div style="display: flex;">
                <div style="margin-left: 5px;" v-for="(date, key) in tagOptionsList" @click="handlerSelectDate(date)" :key="key">
                    <div :class="['dateTag', searchDateName == date.label || (!searchDateName && date.value == 'today') ? 'active' : null]">
                        <span>{{ date.label }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="amount">
            <ShopListTable :fixed="true" v-for="data in staffSurveyData" :listData="data"/>
        </div>
        <van-pull-refresh
            v-model="groupMgrListStatusReactive.isPullLoading" 
            @refresh="onGroupMgrListRefresh"
            class="detailList">
            <van-list
                :offset="50"
                v-if="listData.length"
                v-model:loading="groupMgrListStatusReactive.isNextPageLoading"
                @load="onGroupMgrListNextPageLoad"
                :finished="groupMgrListStatusReactive.isNextPageFinished"
            >
            <ShopListTable :fixed="false" v-for="data in listData" :listData="data"/>
            </van-list>
            <van-empty 
                v-else
                description="暂无数据" 
                :image="emptyImg" 
                :image-size="[200,200]"
            />
        </van-pull-refresh>
    </JLoadingWrapper>
    </div> 
    <van-popup v-model:show="showPickerPopup" destroy-on-close position="bottom">
        <van-picker
            :columns="columns"
            :model-value="pickerValue"
            @confirm="onConfirm"
            @cancel="showPickerPopup = false"
        />
    </van-popup>
</template>
<script setup lang="ts">
import { ref,reactive,computed,onMounted } from "vue";
import ShopListTable from './components/ShopListTable/index.vue'
import emptyImg from "@/assets/store0602Image/emptyImg.png"
import {ShopAssiatantList} from './hooks/index'
import dayjs from "dayjs";
import {dataTimes} from '@/utils/dateUtils';

/** props */
const props = defineProps<{
    storeId?: string; // 店铺id
}>();

const searchDateName = ref('');
const today = dayjs().format('YYYY-MM-DD');
const _tempValReactive = reactive({
    startTime:today,
    endTime:today,
})
const searchParams = reactive({
    dateStart: `${today} 00:00:00`,
    dateEnd: `${today} 23:59:59`,
    orderType: 1,
    storeId: props.storeId ? props.storeId : undefined,
})
const {
    loadData,
    staffSurveyStatData,
    staffSurveyData,
    listData,
    isPageLoadingRef,
    groupMgrListStatusReactive,
    onGroupMgrListNextPageLoad,
    onGroupMgrListRefresh
} = ShopAssiatantList(searchParams);
const showPickerPopup = ref(false)

const columns = [
      { text: '销售订单', value: '1' },
      { text: '福利券兑换', value: '4' },
      { text: '积分兑换', value: '3' },
];
const tagOptionsList = [
    {
        label: '今日',
        value: 'today'
    },
    {
        label:'昨日',
        value:'yesterday'
    },
    {
        label: '本月',
        value: 'this_month'
    },
    {
        label: '全部',
        value: 'all'
    },
]
onMounted(async()=>{
    await staffSurveyStatData()
    await loadData(true)
})
function handlerSelectDate(date){
    searchDateName.value = date.label || date.name;
    if(searchDateName.value == '今日'){
        _tempValReactive.startTime = dayjs(dataTimes[date.value].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes[date.value].end_time).format('YYYY-MM-DD')
    }else if(searchDateName.value == '昨日'){
        _tempValReactive.startTime = dayjs(dataTimes[date.value].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes[date.value].end_time).format('YYYY-MM-DD')
    }else if(searchDateName.value == '本月'){
        _tempValReactive.startTime = dayjs(dataTimes[date.value].start_time).format('YYYY-MM-DD')
        _tempValReactive.endTime = dayjs(dataTimes[date.value].end_time).format('YYYY-MM-DD')
    }
    else{
        _tempValReactive.startTime = ''
        _tempValReactive.endTime = ''
    }
    if(searchDateName.value == '全部'){
        searchParams.dateStart = `${_tempValReactive.startTime}`
        searchParams.dateEnd = `${_tempValReactive.endTime}`
    }else{
        searchParams.dateStart = `${_tempValReactive.startTime} 00:00:00`
        searchParams.dateEnd = `${_tempValReactive.endTime} 23:59:59`
    }
   
    staffSurveyStatData()
    loadData(true)
}
const changeSelect =()=>{
    showPickerPopup.value = true
}
const pickerValue = ref([]);
const  selectType = ref('销售订单')
const onConfirm = ({ selectedValues, selectedOptions }) => {
    selectType.value = selectedOptions[0]?.text;
    pickerValue.value = selectedValues;
    showPickerPopup.value = false;
    searchParams.orderType = selectedValues[0]
    staffSurveyStatData()
    loadData(true)
};
</script>
<style scoped lang="less">
.warpper{
    width: 100%;
    height: 100%;
    .searchHeader{
        display: flex;
        justify-content: space-between;
        padding: 15px;
        background-color: white;

        .screen{
            display:flex;
            align-items: center;
            font-size: 15px;
            .search-icon{
                margin-left: 4px;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 6px solid #666666;
            }
        }
        .dateTag {
            text-align: center;
            height: 24px;
            width: 50px;
            background: #F8F8F8;
            color: #999999;
            line-height: 24px;
            font-size: 12px;
            border-radius: 4px;
            font-weight: 600;
        }
        .active {
            background: #FFF4F4;
            color: #EF1115;
        }
    }
    .amount{
        width: 100%;
        background-color: white;
    }
    .detailList{
        width: 100%;
        height: calc(100% - 54px - 196px - 10px);
        margin-top: 10px;
        overflow-y: scroll;
    }

}
</style>