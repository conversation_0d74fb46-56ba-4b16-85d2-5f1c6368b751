import { storeUSerInfoRefresh } from "@/services/storeApi";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";

/**门店刷新 - 用户信息 */
export async function storeRefreshUserInfo() {
    try {
        const userStore = useUserStoreWithoutSetup();
        const resp = await storeUSerInfoRefresh();
        const { ...userInfo } = resp;
        userStore.setStoreUserInfo(userInfo);
        return true;
    }
    catch (e) {
        return false;
    }
}