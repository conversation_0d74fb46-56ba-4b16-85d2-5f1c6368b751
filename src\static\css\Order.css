/*说明内容文字黑*/
.desc-text1{
    font-size: 24rpx;
    line-height: 34rpx;
    color: #333333;
}
/*说明内容文字灰*/
.desc-text2{
    font-size: 24rpx;
    line-height: 34rpx;
    color: #666666;
}
/*说明内容文字灰*/
.desc-text3{
    font-size: 20rpx;
    line-height: 30rpx;
    color: #666666;
}
/*大号标题*/
.title-text1{
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
}
/*小号标题*/
.title-text2{
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
}
/*字体3*/
.title-text3{
    font-size: 28rpx;
    font-weight: 600;
    color: #666666;
}
/*地址图片*/
.site-images{
    display: flex;
    margin: 20rpx;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中（如果需要） */
}
/*垂直水平居中*/
.center{
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中（如果需要） */
}
/*不换行*/
.no-break{
    word-break: break-all;
    font-weight: bold;
    /*display: -webkit-box;*/
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}