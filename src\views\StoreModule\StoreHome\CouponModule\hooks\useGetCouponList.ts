import { ref } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { CouponStatusEnum } from "@/views/StoreModule/enums";
import { getWelfareCouponByUserId } from "@/services/storeApi";

export default function useGetCouponList(_params: {
  userId: string;
  useStatus: CouponStatusEnum
}) {
  const { createMessageSuccess, createMessageError } = useMessages();
  const isPageLoadingRef = ref(false);
  const model = ref({
    userId: _params.userId,
    useStatus: _params.useStatus
  });

  /** 福利券数据 */
  const couponList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(true);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);

  /** 加载数据 */
  function onLoad() {}

  /** 获取搜索参数 */
  function getSearchParams() {
    return {
      ...model.value
    }
  }

  /** 获取福利券 */
  async function getCouponList() {
    const _params = getSearchParams();
    
    isPageLoadingRef.value = true;
  
    try {
      const resp = await getWelfareCouponByUserId(_params);
      if (resp) {
        couponList.value = resp ?? [];
      }
    } catch (error) {
      createMessageError(`获取福利券失败：${error}`);
    } finally {
      isPageLoadingRef.value = false;
      refreshingRef.value = false;
    }
  }

  /** 刷新 */
  function onRefresh() {
    // 重新加载数据
    refreshingRef.value = true;
    getCouponList();
  }

  return {
    isPageLoadingRef,
    couponList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    model,
    onLoad,
    onRefresh,
    getCouponList
  };
}
