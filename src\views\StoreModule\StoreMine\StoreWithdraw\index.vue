<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh">
    <div class="store_withdraw_wrapper">
      <div class="store_withdraw_content">
        <!-- 提现金额 -->
        <div class="store_withdraw_title">
          <span style="color: #FF3E3E;">*</span>
          提现金额（￥）
        </div>
        <VanField
          v-model="formValue.amount"
          placeholder="请输入提现金额"
          type="number"
          :border="false"
          style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          :formatter="(value) => formatDecimal(value, accountBalance)"
        />
        <!-- 收款账户 -->
        <div class="store_withdraw_title" style="margin-top: 16px;">
          <span style="color: #FF3E3E;">*</span>
          收款账户
        </div>
        <!-- 添加银行卡 -->
        <div v-if="!hasBankCardInfo" class="store_withdraw_bank_container">
          <div class="store_withdraw_bank" @click="handleAddBankCard">
            <SvgIcon name="IonAdd" style="font-size: 18px;" />
            <span class="title">添加银行卡</span>
          </div>
        </div>
        <!-- 银行卡信息 -->
        <div
          v-else
          class="store_withdraw_bank_container"
          style="padding: 12px;display: flex;justify-content: space-between;"
        >
          <div class="store_withdraw_bank_left">
            <!-- 账户名 -->
            <div class="store_withdraw_bank_info">
              <span class="store_withdraw_bank_info_title">账户名：</span>
              <span class="store_withdraw_bank_info_content">{{ bankCardInfo['accountName'] ?? `-` }}</span>
            </div>
            <!-- 银行卡号 -->
            <div class="store_withdraw_bank_info">
              <span class="store_withdraw_bank_info_title">银行卡号：</span>
              <span class="store_withdraw_bank_info_content">{{ bankCardInfo['cardNumber'] ?? `-` }}</span>
            </div>
          </div>
          <div class="store_withdraw_bank_right" @click="handleEditBankCard(bankCardInfo['id'])">
            <span>修改</span>
            <VanIcon name="arrow" style="color:  #D9D9D9;font-size: 12px;" />
          </div>
        </div>
        <!-- 发票上传 -->
        <div
          class="store_withdraw_title"
          style="margin-top: 16px;display: flex;align-items: center;justify-content: space-between;"
        >
          <span>发票上传</span>
          <span style="font-size: 12px;">{{`最多可上传${formValue.invoiceImageList.length}/9张`}}</span>
        </div>
        <VanUploader
          v-model="tempInvoiceImgVOList"
          :after-read="handleAfterRead"
          @delete="handleDeleteUploader"
          :max-count="9"
          multiple
        />
      </div>
      <!-- footer -->
      <div class="footer">
        <VanRow justify="space-between" gutter="8">
          <VanCol span="24">
            <VanButton type="danger" @click="handleComfirmWithdraw" round block style="width: 100%;height: 36px;">确认提现</VanButton>
          </VanCol>
        </VanRow>
      </div>
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import useStoreWithdraw from "./hooks/useStoreWithdraw";
import useAccountBalance from "@/views/StoreModule/StoreMine/hooks/useAccountBalance";
import useBankCardInfo from "@/views/StoreModule/StoreMine/hooks/useBankCardInfo";
/**  相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

defineOptions({ name: "StoreWithdraw" });

const { routerPushByRouteName } = useRouterUtils();
const { accountBalance, getAccountBalance } = useAccountBalance();
const { bankCardInfo, getBankCardInfo, hasBankCardInfo } = useBankCardInfo();
const {
  isPageLoadingRef,
  formValue,
  tempInvoiceImgVOList,
  handleAfterRead,
  handleDeleteUploader,
  formatDecimal,
  handleComfirmWithdraw
} = useStoreWithdraw();

/** 添加银行卡 */
function handleAddBankCard() {
  routerPushByRouteName(RoutesName.StoreBankCard);
}

/** 修改银行卡 */
function handleEditBankCard(id: string) {
  routerPushByRouteName(RoutesName.StoreBankCard, { id: id });
}

onMounted(async () => {
  try {
    isPageLoadingRef.value = true;
    await getAccountBalance();
    await getBankCardInfo();

    if (hasBankCardInfo.value) {
      formValue.value.csBankInfoAccountId = bankCardInfo.value['id'];
    }
  } catch (error) {
    console.log("error", error);
  } finally {
    isPageLoadingRef.value = false;
  }
});
</script>

<style lang="less" scoped>
.store_withdraw_wrapper {
  width: 100%;
  height: 100%;
  padding: 12px;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .store_withdraw_content {
    flex: 1;
    .store_withdraw_title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin: 8px 0px;
    }
    .store_withdraw_bank_container {
      box-sizing: border-box;
      background: #FFFFFF;
      box-shadow: 0px 4px 16px -4px rgba(12,12,12,0.12);
      border-radius: 8px;
      .store_withdraw_bank {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        .title {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .store_withdraw_bank_left {
        display: flex;
        flex-direction: column;
        gap: 4px;
        .store_withdraw_bank_info {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          .store_withdraw_bank_info_title {
            color: #999999;
          }
          .store_withdraw_bank_info_content {
            color: #333333;
          }
        }
      }
      .store_withdraw_bank_right {
        display: flex;
        align-items: center;
        gap: 4px;
        span {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #1677FF;
          line-height: 22px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }

  .footer {
    box-sizing: border-box;
    margin-bottom: 12px;
    :deep(.van-button__text) {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }
  }
}
</style>
