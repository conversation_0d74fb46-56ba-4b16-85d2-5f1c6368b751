<template>
  <VanPopup
    :show="showRef"
    @update:show="handleUpdateShow"
    round
    position="bottom"
    closeable
    safe-area-inset-bottom
    close-icon="close"
    teleport="body"
  >
    <div class="wrapper">
      <StoreTitle :title="props.couponInfo?.categoryName ?? `福利券`" />
      <div class="content">
        <p class="text">{{`· ${props.couponInfo?.exchangeNote ?? `-`}。`}}</p>
        <p class="text">· 在门店营业时间内到店购物使用，或门店商城购物使用。</p>
        <p class="text">· 需本人到店使用。</p>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { CouponStatusEnum } from "@/views/StoreModule/enums";
/** 相关组件 */
import StoreTitle from "./StoreTitle.vue";

defineOptions({ name: 'RedeemInstructions' });

/** props */
const props = defineProps<{
  show: boolean;
  couponInfo: {
    categoryName?: string;
    description?: string;
    useStatus?: CouponStatusEnum;
    unUseNum?: number;
    useNum?: number;
    invalidNum?: number;
    exchangeNote?: string;
  };
}>();

/** emit */
const emit = defineEmits<{
  (e: 'update:show', val: boolean): void;
}>();

const showRef  = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

function handleUpdateShow(val: boolean) {
  emit('update:show', val);
}
</script>

<style lang="less" scoped>
.wrapper {
    height: 320px;
    padding: 40px 16px 16px 16px;
    .content {
        margin-top: 12px;
        .text {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #666666;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
    }
}
</style>
