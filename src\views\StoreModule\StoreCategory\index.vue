<template>
	<JLoadingWrapper :show="cateLoading">
		<div class="category-box">
			<view class="search-box">
				<div @click="handleSearch">
					<van-search shape="round" readonly placeholder="请输入商品名称搜索" />
				</div>
			</view>
			<div class="content-box">
				<div class="sidebar">
					<SideBar :value="tempCateInfo.patentId" @update:value="handleParentClick" :list="cateList">
					</SideBar>
				</div>
				<div class="content-detail">
					<van-list finished-text="" :loading="goodsLoading" @load="loadGoodsData" :finished="finished">
						<Collapse :title="tempCateInfo.parentName" :cate-id="tempCateInfo.childId"
							@update:cate-id="handleChildClick" :list="curChildrenCate"></Collapse>
						<div class="card-list" v-if="goodsList.length">
							<GoodsCard class="card-item" v-for="item in goodsList" @click="jumpDetail(item)" :key="item.id"
								:cardInfo='item' :type="StoreGoodsEnum.Goods" @chooseSku='chooseSku'>
							</GoodsCard>
						</div>
						<van-empty description="暂无商品" :image="goodsEmpty" v-else />
					</van-list>
				</div>
			</div>
		</div>
		<GoodsSkuModal v-model:show="show" :is-auto-computed-price="false" :is-show-init-cart-count="true" :state="skuState"
			:safeBottom='true' />
	</JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted } from "vue"
import { useGoodData, useCateData } from "./hooks"
import SideBar from "./components/SideBar.vue";
import GoodsCard from "./components/GoodsCard.vue";
import Collapse from "./components/Collapse.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import GoodsSkuModal from "./components/GoodsSkuModal/index.vue";
import { storeToRefs } from "pinia";
import goodsEmpty from "@/assets/storeImage/product/goodsEmpty.png";
import { useRouter, useRoute } from "vue-router"
import { RoutesName } from "@/enums/routes";
import { GoodsTypeEnum, StoreGoodsEnum } from "@/enums/storeGoods";
const router = useRouter()
const route = useRoute()
const initParams: any = {
	type: GoodsTypeEnum.COMMON_GOODS,
	cateId: null,
}
const modal = ref<{
	type: any,
	cateId: string,
}>({ ...initParams })
const { goodsList, loadGoodsData, goodsLoading, reloadGoodsData, finished } = useGoodData({
	modal,
})
const { cateList, curChildrenCate,cateLoading, handleChildClick, handleParentClick, tempCateInfo, initCateInfo } = useCateData({
	isAll: true,
	modal,
	callback: reloadGoodsData,
})
const show = ref<boolean>(false)
const skuState = ref<any>({})
//购买
const chooseSku = (info) => {
	//获取当前商品
	skuState.value = JSON.parse(JSON.stringify(info))
	show.value = true
}
const jumpDetail = (info: any) => {
	router.push({
		name: RoutesName.StoreDetail,
		query: {
			...route.query,
			id: info.id || info.productId,
			type: StoreGoodsEnum.Goods
		}
	})
}
const handleSearch = () => {
	router.push({
		name: RoutesName.StoreSearch,
		query: {
			...route.query,
			type: StoreGoodsEnum.Goods
		}
	})
}
onMounted(() => {
	// tempCateInfo.patentId = parentId
	// tempCateInfo.childId = id
	// existIntegral.value = isIntegral || 0
	initCateInfo()
})
</script>

<style scoped lang="less">
:deep(.van-cell) {
	background: none !important;
	padding: 0 !important;
}

.category-box {
	box-sizing: border-box;
	height: 100%;
	display: flex;
	flex-direction: column;

	.header-search-box {
		width: 100vw;
		box-sizing: border-box;

		.search-box {
			width: 100%;
			display: flex;
			align-items: center;

			.search-input {
				flex: 1;
				margin-left: 5px;
				height: 100%;
				border-radius: 22.5px;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				padding: 10px;
				background-color: #F8F8F8;

				img {
					width: 15px;
					height: 15px;
					margin-right: 5px;
				}

				.input-warp {
					color: #999;
					font-size: 14px;
					flex: 1;
				}
			}
		}
	}

	.content-box {
		flex: 1;
		overflow: auto;
		display: flex;

		.sidebar {
			height: 100%;
		}

		.content-detail {
			overflow: auto;
			width: calc(100vw - 112px);
			flex: 1;
			background-color: #fff;
			box-sizing: border-box;
			padding: 12px 12px 0 12px;
			display: flex;
			flex-direction: column;

			.detail-title {
				margin-bottom: 10px;
				color: #666666;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}

			.card-list {}
		}
	}
}

:deep(.card-box .van-image__img) {
	border-radius: 8rpx;
}
</style>