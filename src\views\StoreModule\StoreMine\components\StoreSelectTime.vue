<template>
  <div class="select-time-wrapper">
    <div
      v-for="item in storeDataRange"
      :key="item.value"
      class="select-time-item"
      :class="{ 'select-time-item-active': item.value === props.value }"
      @click="handleClick(item.value)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { StoreDataRangeEnum } from "@/views/StoreModule/enums";

defineOptions({ name: 'StoreSelectTime' });

/** props */
const props = withDefaults( defineProps<{
  value: StoreDataRangeEnum;
}>(), { value: StoreDataRangeEnum.TODAY });

/** emits */
const emits = defineEmits<{
  (e: 'update:value', value: StoreDataRangeEnum): void;
}>();

/** methods */
const handleClick = (value: StoreDataRangeEnum) => {
  emits('update:value', value);
};

const storeDataRange = [
  { label: '今日', value: StoreDataRangeEnum.TODAY },
  { label: '昨日', value: StoreDataRangeEnum.YESTERDAY },
  { label: '本月', value: StoreDataRangeEnum.THIS_MONTH },
  { label: '全部', value: StoreDataRangeEnum.ALL },
];
</script>

<style lang="less" scoped>
.select-time-wrapper {
    width: 160px;
    height: 24px;
    background: #FFF0DD;
    border-radius: 99px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    .select-time-item {
      height: 100%;
      border-radius: 99px;
      flex: 1;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #A98662;
      text-align: left;
      font-style: normal;
      text-transform: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }
}
.select-time-item-active {
  background: #EF1115 !important;
  color: #FFFFFF !important;
}
</style>
