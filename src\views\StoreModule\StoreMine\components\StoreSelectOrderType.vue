<template>
  <VanPopover v-model:show="showPopover" placement="bottom-start">
    <template #reference>
      <div style="display: flex;align-items: center;gap: 2px;">
        <span class="search-type-name">{{ searchTypeText }}</span>
        <img :src="searchUpIcon" alt="" class="search-type-icon" :class="{ 'rotate-180' : showPopover }" />
      </div>
    </template>
    <div class="search-type-list">
      <div
        v-for="item in searchTypeOptions"
        :key="item.value"
        class="search-type-item"
        @click="handleClickItem(item)"
        :class="{ 'active-type': props.value === item.value }"
      >
        {{ item.text }}
      </div>
    </div>
  </VanPopover>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { StoreOrderTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import searchUpIcon from "@/assets/storeImage/storeHome/searchUpIcon.png";

defineOptions({ name: 'StoreSelectOrderType' });

/** props */
const props = defineProps<{
    value: StoreOrderTypeEnum;
}>();

/** emits */
const emit = defineEmits<{
    (e: 'update:value', value: StoreOrderTypeEnum): void;
}>();

/** data */
const showPopover = ref(false);

const searchTypeText = computed(() => {
    const option = searchTypeOptions.find(item => item.value === props.value);
    return option ? option.text : '';
});

const searchTypeOptions = [
  {
    text: "普通订单",
    value: StoreOrderTypeEnum.NORMAL,
  },
  {
    text: "福利券订单",
    value: StoreOrderTypeEnum.COUPON,
  },
  {
    text: "积分订单",
    value: StoreOrderTypeEnum.INTEGRAL,
  },
];

/** methods */
const handleClickItem = (item: { text: string; value: StoreOrderTypeEnum }) => {
    emit('update:value', item.value);
    showPopover.value = false;
};
</script>

<style lang="less" scoped>
.search-type-name {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    text-align: right;
    font-style: normal;
    text-transform: none;
}
.search-type-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    transform: rotateX(180deg);
    transition: transform 0.3s ease;
}
.search-type-list {
    width: 80px;
    background: #FFFFFF;
    .search-type-item {
        height: 36px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
        text-align: right;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #EEEEEE;
        box-sizing: border-box;
    }
}
.active-type {
  color: #EF1115 !important;
}
.rotate-180 {
    transform: rotateX(0deg) !important;
}
</style>
