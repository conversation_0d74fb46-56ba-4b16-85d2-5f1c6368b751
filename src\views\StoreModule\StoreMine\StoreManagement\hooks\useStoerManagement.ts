import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import dayjs from "dayjs";
import { useMessages } from "@/hooks/useMessage";
import { StoreDataRangeEnum, StoreOrderTypeEnum, StoreManageEnum } from "@/views/StoreModule/enums";
import { formatCurrency, formatNumber } from "../utils";
import { storeOrderOverviewApi, storeOrderOverviewPageApi, storePendingListApi } from "@/services/storeApi";

export default function useStoerManagement() {
  const scope = effectScope();
  const { createMessageError } = useMessages();
  /** 页面Loading */
  const isPageLoadingRef = ref(false);

  /** tab 状态 */
  const activeTabRef = ref(StoreManageEnum.MY_STORE);
  /** tab */
  const tabOptionsList = [
    { label: "我的店铺", value: StoreManageEnum.MY_STORE },
    { label: "待审核", value: StoreManageEnum.PENDING_REVIEW },
  ];
  /** 点击tabs */
  function handleClickTab(value: StoreManageEnum) {
    activeTabRef.value = value;
  }

  /** 订单概览时间选择 */
  const storeSelectTimeRef = ref(StoreDataRangeEnum.TODAY);
  /** 订单类型 */
  const storeOrderTypeRef = ref(StoreOrderTypeEnum.NORMAL);
  /** 门店数据概览 */
  const storeOverviewRef = ref([]);
  /** 门店管理-店铺待审核列表 */
  const storePendingListRef = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 订单概览（汇总） */
  const shopOrderOverview = ref<
    Array<{
      label: string;
      key: string;
      value: number | string;
    }>
  >([
    {
      label: "销售订单数",
      key: "totalOrderNum",
      value: 0,
    },
    {
      label: "销售商品数",
      key: "totalProductNum",
      value: 0,
    },
    {
      label: "销售订单额",
      key: "totalOrderAmount",
      value: 0,
    },
    {
      label: "会员人数",
      key: "totalCsNum",
      value: 0,
    },
    {
      label: "退款订单数",
      key: "totalRefundOrderNum",
      value: 0,
    },
    {
      label: "退款商品数",
      key: "totalRefundProductNum",
      value: 0,
    },
    {
      label: "退款订单额",
      key: "totalRefundOrderAmount",
      value: 0,
    },
    {
      label: "新增会员数",
      key: "totalIncrCsNum",
      value: 0,
    },
  ]);

  /** 订单概览（各个店铺） */
  const shopOrderOverviewPage = ref<
    Array<{
      label: string;
      key: string;
      value: number | string;
    }>
  >([
    {
      label: "销售订单数",
      key: "orderNum",
      value: 0,
    },
    {
      label: "销售商品数",
      key: "productNum",
      value: 0,
    },
    {
      label: "销售订单额",
      key: "orderAmount",
      value: 0,
    },
    {
      label: "会员人数",
      key: "csNum",
      value: 0,
    },
    {
      label: "退款订单数",
      key: "refundOrderNum",
      value: 0,
    },
    {
      label: "退款商品数",
      key: "refundProductNum",
      value: 0,
    },
    {
      label: "退款订单额",
      key: "refundOrderAmount",
      value: 0,
    },
    {
      label: "新增会员数",
      key: "incrCsNum",
      value: 0,
    },
  ]);

  /** 获取门店订单概览参数 */
  function getOrderOverviewParams() {
    let dateStart = null;
    let dateEnd = null;
    const now = dayjs();

    switch (storeSelectTimeRef.value) {
      case StoreDataRangeEnum.TODAY:
        dateStart = now.startOf("day").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.endOf("day").format("YYYY-MM-DD 23:59:59");
        break;
      case StoreDataRangeEnum.YESTERDAY:
        dateStart = now.subtract(1, "day").startOf("day").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.subtract(1, "day").endOf("day").format("YYYY-MM-DD 23:59:59");
        break;
      case StoreDataRangeEnum.THIS_MONTH:
        dateStart = now.startOf("month").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.endOf("month").format("YYYY-MM-DD 23:59:59");
        break;
      case StoreDataRangeEnum.ALL:
        dateStart = null;
        dateEnd = null;
        break;
      default:
        // 默认情况下使用当天
        dateStart = now.startOf("day").format("YYYY-MM-DD 00:00:00");
        dateEnd = now.endOf("day").format("YYYY-MM-DD 23:59:59");
    }

    return {
      orderType: storeOrderTypeRef.value,
      dateStart,
      dateEnd,
    };
  }

  /** 获取订单概览 */
  async function getOrderOverview() {
    try {
      isPageLoadingRef.value = true;
      const _params = getOrderOverviewParams();
      const resp = await storeOrderOverviewApi(_params);

      if (resp) {
        // 遍历本地订单概览配置
        shopOrderOverview.value = shopOrderOverview.value.map(item => {
          const respValue = resp[item.key];

          // 处理金额字段
          if (item.key.includes("Amount")) {
            return {
              ...item,
              value: respValue ? formatCurrency(respValue) : "0.00",
            };
          } else {
            return {
              ...item,
              value: respValue ? formatNumber(respValue) : 0,
            };
          }
        });
      }

      isPageLoadingRef.value = false;
    } catch (error) {
      console.error("获取订单概览失败:", error);
      createMessageError("获取订单概览失败：" + error.message);
      isPageLoadingRef.value = false;
    }
  }

  /** 获取门店数据概览 */
  async function getStoreDataOverview() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = {
        data: getOrderOverviewParams(),
        pageVO: {
          current: pageVO.current,
          size: pageVO.size,
        },
      };
      const { records = [], total = 0 } = await storeOrderOverviewPageApi(_params);

      // 更新订单列表
      if (current === 1) {
        storeOverviewRef.value = records;
      } else if (records.length) {
        storeOverviewRef.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 获取门店管理-店铺待审核列表 */
  async function getStorePendingAuditList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = {
        data: {},
        pageVO: {
          current: pageVO.current,
          size: pageVO.size,
        },
      };
      const { records = [], total = 0 } = await storePendingListApi(_params);

      // 更新订单列表
      if (current === 1) {
        storePendingListRef.value = records;
      } else if (records.length) {
        storePendingListRef.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      if (activeTabRef.value === StoreManageEnum.MY_STORE) {
        getStoreDataOverview();
      } else {
        getStorePendingAuditList();
      }
    }
  }

  /** 初始化数据 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  async function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    if (activeTabRef.value === StoreManageEnum.MY_STORE) {
      await getOrderOverview();
      await initStoreDataOverview();
    } else {
      await initStorePendingAuditList();
    }
  }

  /** 门店数据概览初始化 */
  async function initStoreDataOverview() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreDataOverview();
    isPageLoadingRef.value = false;
  }

  /** 门店待审核数据初始化 */
  async function initStorePendingAuditList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStorePendingAuditList();
    isPageLoadingRef.value = false;
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    watch(
      () => [storeSelectTimeRef.value, storeOrderTypeRef.value],
      async val => {
        if (val) {
          await getOrderOverview();
          await getStoreDataOverview();
        }
      },
    );

    /** 监听tabs切换 */
    watch(
      () => activeTabRef.value,
      async val => {
        if (val) {
          if (val == StoreManageEnum.MY_STORE) {
            await getOrderOverview();
            await initStoreDataOverview();
          }
          if (val == StoreManageEnum.PENDING_REVIEW) {
            await initStorePendingAuditList();
          }
        }
      },
      { immediate: true },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    refreshingRef,
    storeSelectTimeRef,
    storeOrderTypeRef,
    shopOrderOverview,
    shopOrderOverviewPage,
    isPageLoadingRef,
    storeOverviewRef,
    getOrderOverview,
    isFinishedRef,
    onLoad,
    activeTabRef,
    tabOptionsList,
    handleClickTab,
    onRefresh,
    storePendingListRef,
  };
}
