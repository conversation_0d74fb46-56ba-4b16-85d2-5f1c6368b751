import { CacheConfig } from "./cache/config";
import { createCacheStorage } from "./cache/storageCache";

export function isProdEnv(): boolean {
  return import.meta.env.PROD;
}


export function isTestEnv(): boolean {
  return import.meta.env.PROD && import.meta.env.VITE_SYS_ENV == 'TEST';
}

export function isDevEnv(): boolean {
  return import.meta.env.DEV;
}
export function isIOSEnv():boolean{
  const UA = navigator.userAgent;
  return /\(i[^;]+;( U;)? CPU.+Mac OS X\)/i.exec(UA)?true:false;
}


export function isQWEnv(){
  const UA = navigator.userAgent;
  return /wxwork/i.exec(UA)?true:false;
}

export function isQWMobileEnv():boolean{
  const UA = navigator.userAgent;
  const isQW = isQWEnv()
  const isQWDesktop = /MailPlugin_Electron/i.exec(UA)?true:false;
  // return isQW
  return (isQW && !isQWDesktop)
}

export function isQWDesktopEnv():boolean{
  const UA = navigator.userAgent;
  const isQW = isQWEnv()
  const isQWDesktop = /MailPlugin_Electron/i.exec(UA)?true:false;
  // return isQW
  return (isQW && isQWDesktop)
}

export function isWXEnv(){
  const UA = navigator.userAgent;
  const isQwEnv = isQWEnv()
  return  !isQwEnv && /MicroMessenger/i.exec(UA)?true:false;
}

export function isWXDesktopEnv():boolean{
  const UA = navigator.userAgent;
  const isWX = isWXEnv()
  const isWXDesktop = /WindowsWechat/i.exec(UA)?true:false;
  // return isQW
  return (isWX && isWXDesktop)
}

export function isLoginQWEnv():boolean{
  const stateCache = createCacheStorage(CacheConfig.State);
  const _stateInfo = stateCache.get();
  return _stateInfo?.corpId && _stateInfo?.agentId
}
export function isInFrame(){
  return window.self !== window.top
}

export function isMobile():boolean{
  const UA = navigator.userAgent;
  return (/Mobi|Android/i.test(UA))
}
export function isMacOSEnv(){
  const UA = navigator.userAgent;
  return /Macintosh/i.exec(UA)?true:false;
}   

export function isWindowEnv(){
  const UA = navigator.userAgent;
  return /Windows NT/i.exec(UA)?true:false;
}

export function isStoreMode(){
  try{
    const pathNameArray = location.pathname.split('/').filter(item=>item)
    if(pathNameArray[0] === 'st'){
      return true
    }
    else return false
  }
  catch(e){
    return false
  }
}