<template>
    <div class="wrapper">
        <img :src="EmptyDataSrc" alt="" />
        <p class="title">暂无数据</p>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import EmptyDataSrc from "@/assets/storeImage/storeHome/EmptyData.png";

defineOptions({ name: 'EmptyData' });
</script>


<style lang="less" scoped>
.wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 24px;
    box-sizing: border-box;
    img {
        width: 200px;
        height: 200px
    }
    .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        text-align: center;
        font-style: normal;
        text-transform: none;
    }
}
</style>