<template>
  <div class="store_my_shop_container">
    <!-- 店铺合计 -->
    <StoreTitle title="店铺合计" style="margin-bottom: 12px;" />
    <BannerContainer
      style="background: linear-gradient(180deg, #FFE2E2 0%, #FFF4F4 13%, #FFFFFF 51%);margin-bottom: 12px;"
    >
      <!-- 数据概览 -->
      <div class="store_shop_container_data">
        <template v-for="item in shopOrderOverviewRef" :key="item.key">
          <div class="store_shop_container_data_item">
            <span class="data_title">{{ item.label }}</span>
            <span class="data_num">{{ item.value }}</span>
          </div>
          <div class="store_shop_container_data_line"></div>
        </template>
      </div>
    </BannerContainer>
    <!-- 门店数据概览 -->
    <BannerContainer
      v-for="item in storeOverviewRef"
      :key="item.staffId"
      style="background: #FFFFFF;margin-bottom: 12px;border-radius: 4px;"
    >
      <!-- 门店标题 -->
      <StoreTitle :title="item?.storeName" />
      <!-- 门店ID -->
      <div class="store_shortId">{{`ID：${item?.storeShortId}`}}</div>
      <!-- 门店数据概览 -->
      <div class="store_container_data">
        <template v-for="field in shopOrderOverviewPageRef" :key="field.key">
          <div class="store_container_data_item">
            <span class="data_title">{{ field.label }}</span>
            <span class="data_num">
              {{ (field.key === 'orderAmount' || field.key === 'refundOrderAmount') 
                ? (item[field.key] / 100).toFixed(2)
                : item[field.key]
              }}
            </span>
          </div>
          <div class="store_container_data_line"></div>
        </template>
      </div>
      <!-- footer -->
      <div class="footer">
        <div class="btn-group">
          <!-- 店员统计 -->
          <div class="shop_assistant_statistics" @click.stop="toShopAssistantStatistics(item)">店员统计</div>
          <!-- 销售统计 -->
          <div class="sales_statistics" @click.stop="toSalesStatistics(item)">销售统计</div>
        </div>
      </div>
    </BannerContainer>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs } from "vue";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
/** 相关组件 */
import StoreTitle from "@/views/StoreModule/components/StoreTitle.vue";
import BannerContainer from "@/views/StoreModule/components/BannerContainer.vue";

defineOptions({ name: "StoreMyShop" });

/** props */
const props = defineProps<{
  shopOrderOverview: Array<any>;
  shopOrderOverviewPage: Array<any>;
  storeOverview: Array<any>;
}>();

const { shopOrderOverview: shopOrderOverviewRef, storeOverview: storeOverviewRef, shopOrderOverviewPage: shopOrderOverviewPageRef } = toRefs(props);
const { routerPushByRouteName } = useRouterUtils();

/** 跳转店员统计 */
function toShopAssistantStatistics(storeInfo: { storeId: string }) {
  const { storeId } = storeInfo;
  routerPushByRouteName(RoutesName.StoreShopAssistantStatistics, { storeId: storeId });
}

/** 跳转销售统计 */
function toSalesStatistics(storeInfo: { storeId: string }) {
  const { storeId } = storeInfo;
  routerPushByRouteName(RoutesName.StoreSalesStatistics, { storeId: storeId });
}
</script>

<style lang="less" scoped>
.store_my_shop_container {
    .store_shop_container_data {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        box-sizing: border-box;
        .store_shop_container_data_item {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            min-width: 80px;
            height: 68px;
            padding: 0px 8px;
            box-sizing: border-box;
            .data_title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                line-height: 16px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .data_num {
                height: 28px;
                font-family: DIN Pro, DIN Pro;
                font-weight: 500;
                font-size: 20px;
                color: #333333;
                line-height: 28px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
        .store_shop_container_data_line {
            width: 1px;
            height: 32px;
            background: #FFE7E7;
        }
    }
    .store_shortId {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-top: 2px;
        margin-bottom: 4px;
    }
    .store_container_data {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        box-sizing: border-box;
        .store_container_data_item {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            min-width: 80px;
            height: 68px;
            padding: 0px 8px;
            box-sizing: border-box;
            .data_title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                line-height: 16px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .data_num {
                height: 28px;
                font-family: DIN Pro, DIN Pro;
                font-weight: 500;
                font-size: 20px;
                color: #333333;
                line-height: 28px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
        .store_container_data_line {
            width: 1px;
            height: 32px;
            background: #EEEEEE;
        }
    }
    .footer {
      width: 100%;
      background: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
      box-sizing: border-box;
      margin-top: 12px;
      .btn-group {
        display: flex;
        align-items: center;
        gap: 12px;
        .shop_assistant_statistics,
        .sales_statistics {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #EF1115;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          width: 96px;
          height: 36px;
          background: #F8F8F8;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }
    }
}
</style>
