<template>
  <div class="price-wrap" :style="customStyle">
    <!-- 前缀 -->
    <div class="price-prefix" :style="{ fontSize: `${props.preFontSize}px` }">￥</div>
    <!-- 整数 -->
    <div class="price-integer" :style="{ fontSize: `${props.integerFontSize}px` }">
      {{ partInfo.integerPart ?? 0 }}
    </div>
    <!-- 小数 -->
    <div class="price-decimal" :style="{ fontSize: `${props.decimalFontSize}px` }">
      {{ `.${partInfo.decimalPart ?? '00'}` }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

defineOptions({ name: 'StorePrice' });

/** props */
const props = withDefaults(
  defineProps<{
    price: number // 最小价格（其他商品）
    preFontSize?: number // 前缀字体大小
    integerFontSize?: number // 整数字体大小
    decimalFontSize?: number // 小数字体大小
    customStyle?: object // 自定义样式
  }>(),
  {
    preFontSize: 12,
    integerFontSize: 20,
    decimalFontSize: 12,
    customStyle: () => ({}),
  },
)

const customStyle = computed(() => {
  return {
    alignItems: 'baseline', // 修改为 baseline 或 flex-end
    ...props.customStyle,
  }
})

/** 价格 */
const _price = computed(() => {
  // 获取价格，默认为 0
  const price = props.price ?? 0
  // 计算并返回格式化后的价格
  return (price / 100).toFixed(2)
})

/** 价格整数与小数部分 */
const partInfo = computed(() => {
  const str = _price.value + ''
  const index = str.indexOf('.')
  return {
    integerPart: index > -1 ? str.substring(0, index) : str,
    decimalPart: index > -1 ? str.substring(index + 1) : '',
  }
})
</script>

<style lang="less" scoped>
.price-wrap {
  display: flex;
  color: #EF1115;
  font-family: DIN Pro, DIN Pro;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;

  .price-prefix, .price-integer, .price-decimal {
    display: block;
  }

  .price-integer {
    word-break: break-all;
  }
}
</style>
