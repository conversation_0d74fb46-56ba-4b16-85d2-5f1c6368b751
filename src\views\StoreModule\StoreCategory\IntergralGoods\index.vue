<template>
    <div class="intergral-goods">
        <view class="search-box" @click="handleSearch">
            <van-search shape="round" readonly placeholder="请输入商品名称搜索" />
        </view>
        <div class="content-warp">
            <div class="exchange-box">
                <div class="exchange-header">
                    <div class="header-left">
                        <div class="left-icon"></div>
                        <div class="left-name">我能兑</div>
                    </div>
                    <div class="header-right" v-if="exchangeList.length" @click="handleJumpExchange">
                        <div class="right-name">更多</div>
                        <van-icon name="arrow" size="16px" color="#666666" />
                    </div>
                </div>
                <div class="exchange-content">
                    <van-list :loading="exchangeLoadingRef" class="content-list" :offset="50" finished-text="没有更多了">
                        <div class="goods-list" v-if="exchangeList.length">
                            <goodsCard v-for="item in exchangeList" :key="item.productId"  @toExchange="toExchange"  @click="handleJumpDetail(item.productId)" :card-info="item"></goodsCard>
                        </div>
                        <van-empty description="暂无商品" :image="goodsEmpty" v-else />
                    </van-list>
                </div>
            </div>
            <div class="exchange-box">
                <div class="exchange-header">
                    <van-tabs v-model:active="tabActive" color="#EF1115" :ellipsis="false" background="transparent">
                        <van-tab :title="item.siftName" :name="item.id" v-for="item in tabList" :key="item.id"></van-tab>
                    </van-tabs>
                </div>
                <div class="exchange-content" v-if="tabList.length" style="max-height: 80vh;">
                    <van-list :loading="listLoadingRef" class="content-list" :finished="listFinishedRef"
                        :offset="50" finished-text="">
                        <div class="goods-list" v-if="dataList.length">
                            <goodsCard v-for="item in dataList" @toExchange="toExchange" :key="item.productId" @click="handleJumpDetail(item.productId)"
                                :card-info="item"></goodsCard>
                        </div>
                         <van-empty description="暂无商品" :image="goodsEmpty" v-else />
                    </van-list>
                </div>
            </div>
        </div>
    </div>
    <IntegralModal v-model:show="showIntegralModal" :state='stateInfo'>
    </IntegralModal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { useExchangeData } from "./hooks/useExchangeData";
import { useIntergralData } from "./hooks/useIntergralData";
import goodsCard from "./components/goodsCard.vue";
import { useRouter, useRoute } from "vue-router"
import goodsEmpty from "@/assets/storeImage/product/goodsEmpty.png";
import { RoutesName } from "@/enums/routes";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import IntegralModal from "../components/IntegralModal/index.vue";
const router = useRouter()
const route = useRoute()
const initParams = {
    minPoints: null,
    maxPoints: null,
}
const modal = ref({ ...initParams })
const {
    dataList,
    listLoadingRef,
    listFinishedRef,
    reloadData,
    getTabList,
    tabList
} = useIntergralData(modal)
const {
    exchangeList,
    exchangeLoadingRef,
    getPoints
} = useExchangeData({
    modal
})
const stateInfo = ref<any>({})
const tabActive = ref('')
const showIntegralModal = ref<boolean>(false)
const handleSearch = () => {
    router.push({
        name: RoutesName.StoreSearch,
        query: {
            ...route.query,
            type: StoreGoodsEnum.IntegralGoods
        }
    })
}
const handleJumpExchange = () => {
    router.push({
        name: RoutesName.StoreIntegralMallCanExchange,
        query: {
            ...route.query,
        }
    })
}
const handleJumpDetail = (id: string) => {
    router.push({
        name: RoutesName.StoreDetail,
        query: {
            ...route.query,
            id,
            type: StoreGoodsEnum.IntegralGoods
        }
    })
}
const toExchange = (info) => {
    stateInfo.value = JSON.parse(JSON.stringify(info))
    showIntegralModal.value = true
}
watch(()=>tabActive.value,(val)=>{
    if(val){
        const tabInfo = tabList.value.find((item: any) => item.id === val) || {}
        modal.value.minPoints = tabInfo.minPoints
        modal.value.maxPoints = tabInfo.maxPoints
        reloadData()
        return
    }
    modal.value = { ...initParams }
    reloadData()
})
onMounted(() => {
    getPoints()
    getTabList()
})
</script>

<style scoped lang="less">
@import url('@/styles/storeVar.less');
:deep(.van-cell){
	background: none !important;
	padding: 0 !important;
}
.intergral-goods {
    height: calc(100vh - env(safe-area-inset-bottom));
    display: flex;
    flex-direction: column;

    .search-box {}

    .content-warp {
        flex: 1;
        overflow-y: auto;
        box-sizing: border-box;
        padding: 12px;

        .exchange-box {
            .exchange-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .header-left {
                    display: flex;
                    align-items: center;

                    .left-icon {
                        width: 4px;
                        height: 16px;
                        background-color: @error-color;
                        border-radius: 90px;
                        margin-right: 8px;
                    }

                    .left-name {
                        font-size: 20px;
                        font-weight: bold;
                    }
                }

                .header-right {
                    display: flex;
                    align-items: center;
                    color: #666666;

                    .right-name {
                        margin-right: 7px;
                        font-size: 14px;
                    }

                    .right-icon {}
                }
            }

            .exchange-content {
                overflow: auto;
                .goods-list {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    /* 每行两个元素 */
                    gap: 10px;
                }
            }
        }
    }
}
</style>