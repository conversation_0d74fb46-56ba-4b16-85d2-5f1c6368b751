<template>
  <div class="search-wrapper">
    <div class="search">
      <div class="search-type">
        <VanPopover v-model:show="showPopover" placement="bottom-start">
          <template #reference>
            <span class="search-type-name">{{ searchTypeText }}</span>
          </template>
          <div class="search-type-list">
            <div
              v-for="item in searchTypeOptions"
              :key="item.value"
              class="search-type-item"
              @click="handleClickItem(item)"
              :class="{ 'active-type': props.searchTypeValue === item.value }"
            >
              {{ item.text }}
            </div>
          </div>
        </VanPopover>
        <img :src="searchUpIcon" alt="" class="search-type-icon" :class="{ 'rotate-180' : showPopover }" />
      </div>
      <div class="search-input">
        <!-- 绑定本地状态 -->
        <VanField
          v-model="localValue"
          clearable
          placeholder="请输入内容"
          @update:model-value="handleInput"
          enterkeyhint="search"
          @keyup.enter="searchEvent"
        />
      </div>
      <div class="search-btn" @click="handleSearch">
        <img :src="searchIcon" alt="" class="search-btn-icon" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import searchUpIcon from "@/assets/storeImage/storeHome/searchUpIcon.png";
import searchIcon from "@/assets/storeImage/storeHome/search.png";

defineOptions({ name: 'StoreSelectSearch' });

/** props */
const props = defineProps<{
    value: string;
    searchTypeValue: string | number;
    searchTypeOptions: Array<{
      text: string;
      value: string | number;
    }>;
}>();

/** emit */
const emit = defineEmits<{
    (e: 'update:value', value: string): void;
    (e: 'update:searchTypeValue', value: string | number): void;
    (e: 'search'): void;
}>();

const localValue = ref(props.value);
const showPopover = ref(false);
const searchTypeText = computed(() => {
  const option = props.searchTypeOptions.find(item => item.value === props.searchTypeValue);
  return option ? option.text : '';
});

const handleInput = (value: string) => {
  localValue.value = value;
  emit('update:value', value);
};

function handleClickItem(item: { text: string; value: string | number }) {
  emit('update:searchTypeValue', item.value);
  showPopover.value = false;
}

function handleSearch() {
  emit('search');
}

function searchEvent() {
  if (document.activeElement instanceof HTMLElement) {
    document.activeElement.blur();
    handleSearch();
  }
}

/** 监听 props.value 的变化，更新本地状态 */
watch(() => props.value, (newVal) => {
    localValue.value = newVal;
});
</script>

<style lang="less" scoped>
.search-wrapper {
    height: 48px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    box-sizing: border-box;
    .search {
        width: 100%;
        height: 32px;
        background: #F8F8F8;
        border-radius: 27px;
        display: flex;
        align-items: center;
        .search-type {
            min-width: 80px;
            display: flex;
            align-items: center;
            gap: 4px;
            border-right: 1px solid rgba(0,0,0,0.05);
            box-sizing: border-box;
            margin-left: 12px;
            margin-right: 4px;
            .search-type-icon {
                width: 16px;
                height: 16px;
                margin-right: 4px;
                transform: rotateX(180deg);
                transition: transform 0.3s ease;
            }
            .search-type-name {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
        .search-input {
            height: 100%;
            flex: 1;
            display: flex;
            align-items: center;
        }
        .search-btn {
            width: 32px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            .search-btn-icon {
                width: 16px;
                height: 16px;
            }
            &:hover {
                background: #e8e8e8;
                border-top-right-radius: 27px;
                border-bottom-right-radius: 27px;
            }
        }
    }
}
:deep(.van-cell) {
    padding: 0;
    background: #F8F8F8;
}
.search-type-list {
    width: 80px;
    background: #FFFFFF;
    .search-type-item {
        height: 36px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
        text-align: right;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #EEEEEE;
        box-sizing: border-box;
    }
}
.active-type {
  color: #EF1115 !important;
}

.rotate-180 {
    transform: rotateX(0deg) !important;
}
</style>
