<template>
  <div class="store_my_dealer_wrapper">
    <!-- 邀请经销商注册 -->
    <div class="my_dealer" @click="handleToDealerApplyQrCode">
      <SvgIcon name="IonAdd" style="font-size: 18px;" />
      <span class="title">邀请经销商注册</span>
    </div>
    <StayTuned />
    <!-- 经销商邀请码 -->
    <StoreApplyQrCode 
        v-model:show="isShowDealerApplyQrCode"
        :qrCodeType="QrCodeTypeEnum.DISTRIBUTOR_REGISTER"
        :qrCodeUrl="qrCodeUrlRef"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { showToast } from "vant";
import QRcode from "qrcode";
import { useMessages } from "@/hooks/useMessage";
import { QrCodeTypeEnum, OrgApplyTypeEnum } from "@/views/StoreModule/enums";
import { getStructureApplyApi } from "@/services/storeApi";
/** 相关组件 */
import StayTuned from "@/views/StoreModule/components/StayTuned.vue";
import StoreApplyQrCode from "../components/StoreApplyQrCode.vue";

defineOptions({ name: 'StoreMyDealer' });

const { createMessageSuccess, createMessageError } = useMessages();
const isShowDealerApplyQrCode = ref(false);
const qrCodeUrlRef = ref<string>('');

async function handleToDealerApplyQrCode() {
  try {
    const resp = await getStructureApplyApi();
    if (resp) {
      qrCodeUrlRef.value = await generateQRCode(resp);
      isShowDealerApplyQrCode.value = true;
    }
  } catch (error) {
    showToast({
      message: error,
      duration: 3000
    });
  } 
}

/** 生成二维码 */
async function generateQRCode(url: string): Promise<string | null> {
  try {
    const qrCodeDataUrl = await QRcode.toDataURL(url, {
      width: 156,
      height: 156,
      margin: 2,
    });
    return qrCodeDataUrl;
  } catch (err) {
    createMessageError("生成二维码失败：" + err);
    return null;
  }
}
</script>

<style lang="less" scoped>
.store_my_dealer_wrapper {
    width: 100%;
    height: 100vh;
    background-color: #F8F8F8;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .my_dealer {
        height: 48px;
        background: #FFFFFF;
        box-shadow: 0px 4px 16px -4px rgba(12,12,12,0.12);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        .title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            line-height: 24px;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }
}
</style>
