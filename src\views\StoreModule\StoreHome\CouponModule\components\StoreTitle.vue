<template>
    <div class="title-wrapper">
        <div class="line"></div>
        <div class="title">{{ props.title }}</div>
    </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'StoreTitle' });

/** props */
const props = defineProps<{
    title: string;
}>();
</script>


<style lang="less" scoped>
.title-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 30px;

    .line {
        width: 4px;
        height: 16px;
        background: #EF1115;
        border-radius: 99px;
    }

    .title {
        font-family: Source <PERSON> CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 20px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
</style>