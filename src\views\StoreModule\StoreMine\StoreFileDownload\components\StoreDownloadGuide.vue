<template>
  <VanPopup
    v-model:show="showRef"
    @close="handleClose"
    :style="popupStyle"
    :overlay-style="overlayStyle"
  >
    <div class="guide-container">
      <img :src="downloadGuide" alt="下载指引" class="guide-image" />
      <div class="guide-tip">
        <!-- 点击右上角的按钮 -->
        <div class="guide-tip-title">
          <span class="step-count">1</span>
          <span class="step-text">点击右上角的按钮</span>
        </div>
        <!-- 选择“在浏览器打开” -->
        <div class="guide-tip-title">
          <span class="step-count">2</span>
          <span class="step-text">选择“在浏览器打开”</span>
        </div>
      </div>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { computed } from "vue";
// 静态资源
import downloadGuide from "@/assets/storeImage/storeMine/downloadGuide.png";

defineOptions({
  name: "StoreDownloadGuide"
});

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['update:show']);

// 使用更简洁的计算属性写法
const showRef = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

const handleClose = () => {
  emit('update:show', false);
};

// 样式对象
const popupStyle = {
  width: '100vw',
  height: '100vh',
  background: 'none',
  padding: 0,
};

const overlayStyle = {
  background: 'rgba(0, 0, 0, 0.6)'
};
</script>

<style lang="less" scoped>
.guide-container {
  position: relative;
  width: 100%;
  height: 400px;
  padding: 12px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .guide-image {
    width: 46px;
    height: 60px;
    position: absolute;
    top: 24px;
    right: 52px;
  }

  .guide-tip {
    margin-top: 86px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 20%;
    .guide-tip-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .step-count {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background-color: #EF1115;
        border-radius: 50%;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 26px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .step-text {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 26px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}

// 响应式调整
@media (max-width: 375px) {
  .guide-container {
    .guide-image {
      right: 15%;
    }
  }
}
</style>
