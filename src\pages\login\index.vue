<template>
  <DefaultLayouts>
    <view
      class="login-wrapper"
      :style="{ 'background-image': `url(${LoginBackground})` }"
    >
      <!-- 加载商城信息 -->
      <van-loading
        v-if="isLoadingRef"
        type="spinner"
        class="absolute top-50% left-50% transform -translate-x-1/2 -translate-y-1/2"
      />
      <!-- 登录操作 -->
      <template v-else>
        <!-- Logo -->
        <view class="w-100% fixed top-354rpx">
          <van-image
            v-if="logoInfo.imgPath"
            round
            width="160rpx"
            height="160rpx"
            :src="logoInfo.imgPath"
          />
          <view v-if="logoInfo.name" class="mt-16rpx">{{ logoInfo.name }}</view>
        </view>
        <!-- 登录按钮 -->
        <view class="w-[calc(100%-144rpx)] fixed top-808rpx m-x-72rpx">
          <!-- #ifdef MP-WEIXIN -->
          <!-- msg_active 是否同意协议 -->
          <template v-if="msg_active">
            <van-button
              v-if="isNeedGetPhoneRef"
              :disabled="isErrorRef || isLoginDisabledComputed"
              color="#EF1115"
              open-type="getPhoneNumber"
              round
              block
              @getphonenumber="getPhoneHandler"
            >
              手机号码快捷登录
            </van-button>
            <van-button
              v-else
              :disabled="isErrorRef || isLoginDisabledComputed"
              color="#EF1115"
              round
              block
              @click="loginSuccess"
            >
              微信授权登录
            </van-button>
          </template>
          <van-button
            v-else
            :disabled="isErrorRef || isLoginDisabledComputed"
            color="#EF1115"
            round
            block
            @click="setShowAgreementTrue"
          >
            {{ isNeedGetPhoneRef ? '手机号码快捷登录' : '微信授权登录' }}
          </van-button>
          <!-- #endif -->
          <!-- #ifdef H5 -->
          <van-button
            color="#EF1115"
            round
            block
            @click="h5Login"
            :disabled="isLoginDisabledComputed"
          >
            一键登录
          </van-button>
          <!-- #endif -->

           <!-- 登录协议 -->
          <view
            class="text-28rpx text-#666666 font-400 mt-32rpx text-left line-height-33rpx not-italic normal-case"
          >
            <!-- #ifdef MP-WEIXIN -->
            <van-checkbox
              v-bind:value="msg_active"
              @change="msg_active = !msg_active"
              icon-size="28rpx"
              checked-color='#EF1115' 
            >
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <van-checkbox
              v-model="msg_active"
              icon-size="28rpx"
              checked-color='#EF1115' 
            >
            <!-- #endif -->   
              我已阅读、理解并同意
              <text style="color: #ff6864" @click.stop="toAgreement(SystemPDFKeyEnum.Agreement)">
                《用户服务协议》
              </text>
              和
              <text style="color: #ff6864" @click.stop="toAgreement(SystemPDFKeyEnum.Policy)">
                《隐私政策》
              </text>
            </van-checkbox>
          </view>
          <!-- 提示 -->
          <view
            v-if="showPopover"
            class="relative p-y-12rpx p-x-24rpx float-left right-16rpx bg-#333333 rounded-6rpx inline-block"
          >
            <view class="triangle"></view>
            <view class="text-24rpx text-#ffffff font-400 line-height-32rpx not-italic normal-case">
              请先勾选，同意后再登录
            </view>
          </view>
        </view>
        <van-dialog
          use-slot
          :show="showGetPhoneFailDialog"
          title="温馨提示"
          :show-confirm-button="false"
          :show-cancel-button="false"
          @close="setShowGetPhoneFailFalse"
        >
          <view class="text-#666666 pt-20rpx pb-48rpx p-x-20rpx">
            <view class="text-28rpx text-left m-x-20rpx mb-50rpx">
              暂时无法获取手机号快捷登录，将为您使用微信账户信息登录，你可以正常购物下单。
            </view>
            <van-button
              type="default"
              round
              @click="setShowGetPhoneFailFalse"
              custom-style="width: 40%; background-color: #F8F8F8; margin: 0 10% 0 5%"
            >
              取消
            </van-button>
            <van-button
              color="#EF1115"
              round
              @click="loginSuccess"
              custom-style="width: 40%; margin-right: 5%"
            >
              确定
            </van-button>
          </view>
        </van-dialog>
      </template>
    </view>
  </DefaultLayouts>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useLoading, useBoolean } from '@/hooks/common'
import useStoreLogoInfo from './hooks/useStoreLogoInfo'
import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user'
import { RoutesName } from '@/routes/enums/routeNameEnum'
import { navigateBack, switchTab } from '@/routes/utils/navigateUtils'
import { getLoginCode } from '@/utils/wxSdkUtils/account'
import { useRectInfo } from '@/hooks/common'
import { previewDocumentByUrl } from '@/utils/fileUtils'
import { SystemPDFKeyEnum } from '@/enum'
import LoginBackground from '@/static/images/storeLogin/loginBg.png'
import { getAgreementByKey, getUserPhoneByCode, accountLogin } from '@/services/api'
import { useMessages } from '@/hooks/common'
/** 静态资源 */
/** 相关组件 */
import DefaultLayouts from '@/layouts/default.vue'
import { CacheConfig } from '@/config/cacheConfig'
import { createCacheStorage } from '@/utils/cache/storage'

const userStore = useUserInfoStoreWithoutSetup()
const { loading: isLoadingRef, startLoading, endLoading } = useLoading()
const { getStoreLogo, logoInfo } = useStoreLogoInfo()
const { rectInfo } = useRectInfo()
/** 是否绑定手机号 */
const isNeedGetPhoneRef = ref(false)
/** 用户信息 */
let userInfo = null
/** 登录是否出现错误 */
let isErrorRef = ref(false)
/** 是否同意协议 */
const msg_active = ref(false)
/** 协议弹窗 */
const {
  bool: showAgreementDialog,
  setFalse: setShowAgreementFalse,
  setTrue: setShowAgreementTrue,
} = useBoolean()
/** 获取手机号失败弹窗 */
const {
  bool: showGetPhoneFailDialog,
  setFalse: setShowGetPhoneFailFalse,
  setTrue: setShowGetPhoneFailTrue,
} = useBoolean()
const isLoginDisabledComputed = computed(() => {
  return !msg_active.value
})
const { createMessageSuccess, createMessageError } = useMessages()
const customStyle = computed(() => {
  return `
  background-color: transparent;
  z-index: 200;
  position: fixed;
  width: 100vw;
  padding-top: ${rectInfo.value.top}px;
  top: -20rpx;`
})
const showPopover = computed(() => {
  return !msg_active.value
})
/** 导航回上一个页面 */
function _navigateBack() {
  navigateBack({
    fail: () => {
      switchTab({
        url: RoutesName.StoreHome,
      })
    },
  })
}

/** 获取手机号的处理逻辑 */
async function getPhoneHandler(e) {
  setShowAgreementFalse()
  const phoneCode = e.detail.code || ''
  const msg = e.detail.errMsg
  try {
    if (phoneCode) {
      uni.showLoading({
        title: '登录中...',
        mask: true,
      })
      userStore.setToken(userInfo.token)
      // 使用手机号验证码获取用户手机号
      let mobile = await getUserPhoneByCode(phoneCode)
      if (mobile) {
        userInfo.mobile = mobile // 保存用户手机号
      }
      loginSuccess()
    } else if (msg != 'getPhoneNumber:fail user deny') {
      // 获取手机号失败时的处理
      setShowGetPhoneFailTrue()
    }
  } catch (e) {
    userStore.setToken(null)
    setShowGetPhoneFailTrue()
    uni.hideLoading()
    uni.showToast({
      title: e,
      icon: 'none',
    })
  }
}
// h5登录
const h5Login = async () => {
  if(msg_active.value == false){
      createMessageError('请先阅读并勾选同意协议')
      return
  }
  // const storePathNameStorage = createCacheStorage(CacheConfig.StorePathName)
  // const pathName = storePathNameStorage.get()
  // const params = parseUrlParams(location.search)
  // const webAuthParams: WXAuthConfig = {
  //     type: QWAuthTypeEnum.WX,
  //     appid: storeInfo.appId,
  //     state: params.state as string
  //   }
  //   redirectToWebpageAuthorization(webAuthParams,pathName as string)
  //   return
}
/** 登录成功后的处理逻辑 */
function loginSuccess() {
  setShowGetPhoneFailFalse()
  userStore.setUserInfo(userInfo)
  userStore.setToken(userInfo.token)
  uni.hideLoading()
  uni.showToast({
    title: '登录成功!',
    icon: 'none',
    mask: true,
    complete: () => {
      userInfo = null
      setTimeout(() => {
        _navigateBack()
      }, 1000)
    },
  })
}

/** 查看协议的处理逻辑 */
const toAgreement = async (key) => {
  getAgreementByKey(key)
    .then(async (res) => {
      const srcEndWith = res.value
      if (!srcEndWith) throw '暂无内容'
      try {
        await previewDocumentByUrl(JSON.parse(srcEndWith).url)
      } catch (e) {
        throw e
      }
    })
    .catch((e) => {
      console.log(e, 'err')
      const text = e.errMsg || e
      uni.showToast({
        title: text,
        icon: 'none',
      })
    })
}

/** 页面显示回调 */
onLoad(() => {
  /** 获取商城logo信息 */
  getStoreLogo()
})

/** 页面显示回调 */
onShow(async () => {
  // #ifdef MP-WEIXIN
  // if (userStore.isLogin) {
  //   endLoading()
  //   _navigateBack()
  // } else {
  //     try {
  //       startLoading()
  //       // 获取登录码
  //       const _loginCode = await getLoginCode()
  //       // 使用登录码进行账号登录
  //       userInfo = await accountLogin({ code: _loginCode })
  //       // 检查用户是否绑定手机号
  //       if (userInfo.mobile) {
  //         isNeedGetPhoneRef.value = false
  //       } else {
  //         isNeedGetPhoneRef.value = true
  //       }
  //       isErrorRef.value = false
  //     } catch (error) {
  //       isErrorRef.value = true
  //       // 登录失败时的处理
  //       uni.showToast({
  //         title: `${error}，请刷新后重试`,
  //         icon: 'none',
  //       })
  //     } finally {
  //       isLoadingRef.value = false
  //     }
  // }
  // #endif
})
</script>

<style lang="scss" scoped>
.login-wrapper {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  background-color: #ffffff;
  background-repeat: no-repeat;
  background-size: 100vw;
  text-align: center;
  .loginImg {
    position: fixed;
    top: 354rpx;
    margin: 0 auto;
    width: 100vw;
  }
}
.triangle {
  position: absolute;
  top: -14rpx;
  width: 0;
  height: 0;
  border-bottom: 16rpx solid #333333;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
}
:deep(.checkbox-index--van-checkbox) {
  align-items: unset !important;
}
:deep(.checkbox-index--van-checkbox__label) {
  padding-left: 6rpx !important;
}
:deep(.checkbox-index--van-checkbox__icon--round) {
  margin-top: 4rpx;
}
:deep(.van-hairline--bottom:after) {
  border-bottom-width: 0;
}
</style>
