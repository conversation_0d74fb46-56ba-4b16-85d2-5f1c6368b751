import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import { OrderStatusEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { getMyOrderList, getMyAfterSaleOrderList } from "@/services/storeApi";

export default function useGetMineOrder(_params: { orderType: OrderStatusEnum }) {
  const scope = effectScope();
  const { createMessageSuccess, createMessageError } = useMessages();
  const isPageLoadingRef = ref(false);

  /** tab */
  const activeTabRef = ref(Number(_params.orderType));

  /** 我的订单状态 */
  const orderStatusList = [
    {
      label: "全部",
      value: OrderStatusEnum.ALL,
    },
    {
      label: "待付款",
      value: OrderStatusEnum.WAIT_PAY,
    },
    {
      label: "待发货/提货",
      value: OrderStatusEnum.WAIT_DELIVER,
    },
    {
      label: "待收货",
      value: OrderStatusEnum.WAIT_RECEIVE,
    },
    {
      label: "退款/售后",
      value: OrderStatusEnum.REFUND,
    },
  ];

  /** 我的订单数据 */
  const storeMineOrderList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      if (activeTabRef.value === OrderStatusEnum.REFUND) {
        getStoreMineAfterSaleOrderList();
      } else {
        getStoreMineOrderList();
      }
    }
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    const status = [OrderStatusEnum.ALL, OrderStatusEnum.REFUND].includes(activeTabRef.value) ? null : activeTabRef.value;
    return {
      data: {
        status,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取我的订单（不包含售后订单） */
  async function getStoreMineOrderList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getMyOrderList(_params);

      // 更新订单列表
      if (current === 1) {
        storeMineOrderList.value = records;
      } else if (records.length) {
        storeMineOrderList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 获取售后订单列表数据 */
  async function getStoreMineAfterSaleOrderList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getMyAfterSaleOrderList(_params);

      // 更新订单列表
      if (current === 1) {
        storeMineOrderList.value = records;
      } else if (records.length) {
        storeMineOrderList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    if (
      [
        OrderStatusEnum.ALL,
        OrderStatusEnum.WAIT_PAY,
        OrderStatusEnum.WAIT_DELIVER,
        OrderStatusEnum.WAIT_RECEIVE,
      ].includes(activeTabRef.value)
    ) {
      initStoreMineOrderList();
    } else if (activeTabRef.value == OrderStatusEnum.REFUND) {
      /** 退款/售后另外接口 */
      initStoreMineOrderRefundList();
    }
  }

  /** 数据初始化 */
  async function initStoreMineOrderList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreMineOrderList();
    isPageLoadingRef.value = false;
  }

  /** 售后订单数据初始化 */
  async function initStoreMineOrderRefundList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreMineAfterSaleOrderList();
    isPageLoadingRef.value = false;
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    /** 监听 */
    watch(
      () => activeTabRef.value,
      newVal => {
        if (
          [
            OrderStatusEnum.ALL,
            OrderStatusEnum.WAIT_PAY,
            OrderStatusEnum.WAIT_DELIVER,
            OrderStatusEnum.WAIT_RECEIVE,
          ].includes(newVal)
        ) {
          initStoreMineOrderList();
        } else if (newVal == OrderStatusEnum.REFUND) {
          /** 退款/售后另外接口 */
          initStoreMineOrderRefundList();
        }
      },
      {
        immediate: true,
      },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    activeTabRef,
    orderStatusList,
    isPageLoadingRef,
    storeMineOrderList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onLoad,
    onRefresh,
    initStoreMineOrderList,
  };
}
