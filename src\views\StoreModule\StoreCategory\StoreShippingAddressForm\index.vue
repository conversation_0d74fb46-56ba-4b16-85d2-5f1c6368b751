<template>
  <div class="store_signup_wrapper" style="background: #FFFFFF;">
    <BannerContainer style="background: #FFFFFF;padding: 0px;">
      <!-- 收件人 -->
      <div class="field_item_wrapper">
        <div class="field_label_container">
          <span class="tip">*</span>
          <span class="label_title">收件人</span>
        </div>
        <VanField class="field" :border="false" v-model="model.name" placeholder="请输入姓名" />
      </div>
      <!-- 手机号 -->
      <div class="field_item_wrapper">
        <div class="field_label_container">
          <span class="tip">*</span>
          <span class="label_title">手机号+86</span>
        </div>
        <VanField class="field" :border="false" v-model="model.mobile" placeholder="请输入手机号" />
      </div>
      <!-- 所在地区 -->
      <div class="field_item_wrapper" style="position: relative;">
        <div class="field_label_container">
          <span class="tip">*</span>
          <span class="label_title">所在地区</span>
        </div>
        <VanField
          class="field"
          :border="false"
          v-model="addressStr"
          rows="1"
          autosize
          readonly
          type="textarea"
          placeholder="请输入收货地址"
          @click="showAddress = true"
          style="padding-right: 24px;"
        />
        <VanIcon name="arrow" class="icon-wrapper" />
      </div>
      <!-- 详细地址 -->
      <div class="field_item_wrapper" style="align-items: flex-start;">
        <div class="field_label_container">
          <span class="tip">*</span>
          <span class="label_title">详细地址</span>
        </div>
        <VanField
          class="field"
          :border="false"
          rows="4"
          autosize
          type="textarea"
          v-model="model.address"
          placeholder="请输入详细地址"
        />
      </div>
      <!-- 设置默认地址 -->
      <div class="field_item_wrapper" style="display: flex; justify-content: space-between; align-items: center">
        <div class="field_label_container" style="width: 86px;">
          <span class="label_title" style="width: 90px;">设置默认地址</span>
        </div>
        <VanSwitch v-model="model.isDefault" :active-value="1" :inactive-value="0" active-color="#EF1115" size="20px" />
      </div>
    </BannerContainer>
    <!-- 提交 -->
    <div class="footer">
      <VanRow justify="space-between" gutter="8">
        <VanCol span="6">
          <VanButton round type="default" @click="handleCancel" style="width: 100%;height: 36px;">取消</VanButton>
        </VanCol>
        <VanCol span="18">
          <VanButton type="danger" @click="handleSaveAddress" round block style="width: 100%;height: 36px;">
            保存
          </VanButton>
        </VanCol>
      </VanRow>
    </div>
    <!-- 地址选择 -->
    <JStoreAddress v-model:show="showAddress" @confirm="handleAddressConfirm" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import { AddressModeEnum } from "@/views/StoreModule/enums";
import { useUserAddress } from "../hooks";
import { addAddress, updateAddress } from "@/services/storeApi";
/** 相关组件 */
import BannerContainer from "@/views/StoreModule/components/BannerContainer.vue";
import JStoreAddress from "@/views/StoreModule/components/JStoreAddress.vue";

defineOptions({ name: 'StoreSignup' });

/** props */
const props = defineProps<{
  type: AddressModeEnum;
  id?: string;
  customerId?:string
}>();

const router = useRouter();
const { createMessageError, createMessageSuccess } = useMessages();
const { queryAddressById } = useUserAddress();

const initParams = {
  id: null, // 地址id
  name: '', // 收件人姓名
  mobile: '', // 联系电话
  address: '', // 详细地址
  isDefault: false,
  province: '',
  provinceId: null,
  cityName: '',
  cityId: null,
  area: '',
  areaId: null,
  town: '',
  townId: null,
};
const model = ref({...initParams});

const showAddress = ref(false);
function handleAddressConfirm(addressData: {
  area: string;
  areaId: string;
  city: string;
  cityId: string;
  province: string;
  provinceId: string;
  town: string;
  townId: string;
}) {
  const { province, provinceId, city, cityId, area, areaId, town, townId } = addressData;
  Object.assign(model.value, {
    province,
    provinceId,
    cityName: city,
    cityId,
    area,
    areaId,
    town,
    townId
  });
}

/** 收货区域 */
const addressStr = computed(() => {
  const { province, cityName, area, town } = model.value;
  return [province, cityName, area, town].filter(Boolean).join('/');
});

/** 获取参数 */
function _getParams() {
  const { id, name, mobile, address, isDefault, provinceId, province, cityId, cityName, areaId, area, townId, town } = model.value;
  return {
    id: props.type == AddressModeEnum.EDIT ? id : undefined,
    name: name.trim(),
    mobile: mobile.trim(),
    address: address.trim(),
    isDefault: isDefault ? 1 : 0,
    province: province,
    provinceId: provinceId,
    cityName,
    cityId,
    areaId: areaId,
    area: area,
    town,
    townId,
    customerId: props.customerId
  };
}

/** 取消 */
function handleCancel() {
  router.back();
}

/** 保存 */
async function handleSaveAddress() {
  try {
    // 校验收件人姓名
    if (!model.value.name || model.value.name.trim() === '') {
      showToast('请输入收件人姓名');
      return;
    }

    // 校验手机号格式
    const mobileRegex = /^1[3-9]\d{9}$/;
    if (!model.value.mobile || !mobileRegex.test(model.value.mobile)) {
      showToast('请输入正确的手机号码');
      return;
    }

    // 校验所在地区
    if (!model.value.province || model.value.province.trim() === '') {
      showToast('请选择所在地区');
      return;
    }

    // 校验详细地址
    if (!model.value.address || model.value.address.trim() === '') {
      showToast('请输入详细地址');
      return;
    }

    const _params = _getParams();

    if (props.type == AddressModeEnum.ADD) {
      await addAddress(_params);
    } else {
      await updateAddress(_params);
    }
    showToast('保存地址成功');
    router.back();
  } catch (error) {
    console.error('保存地址失败:', error);
    createMessageError('保存地址失败：' + error);
  }
}

/** 监听 */
watch(() => props.type, async (newVal) => {
  if (newVal) {
    if (newVal == AddressModeEnum.ADD) document.title = '新增收货地址';
    if (newVal == AddressModeEnum.EDIT) {
      document.title = '编辑收货地址';
      const result = await queryAddressById(props.id);
      if (result) {
        Object.assign(model.value, result);
      }
    };
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.store_signup_wrapper {
  width: 100%;
  height: 100vh;
  background: #F8F8F8;
  padding: 12px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .field_item_wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 18px;
    .field_label_container {
      width: 76px;
      .tip {
        color: #FF3E3E;
        margin-right: 2px;
      }
      .label_title {
        width: 72px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .field {
      min-height: 36px;
      flex: 1;
      padding: 4px 8px;
      background: #f8f8f8;
      border-radius: 4px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .icon-wrapper {
      position: absolute;
      right: 6px;
    }
    .address_wrapper {
      display: flex;
      align-items: center;
    }
  }
  .footer {
    box-sizing: border-box;
    :deep(.van-button__text) {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }
  }
}
:deep(.van-field__label) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 8px;
}
:deep(.van-field__control) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
