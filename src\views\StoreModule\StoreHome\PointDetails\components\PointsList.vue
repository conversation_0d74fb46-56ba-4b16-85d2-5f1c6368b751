<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="point-list-content">
      <template v-if="pointsList.length">
        <VanList v-model:loading="isLoadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
          <PointCard v-for="item in pointsList" :key="item.id" :pointType="pointTypeRef" :pointInfo="item" />
        </VanList>
      </template>
      <template v-else>
        <EmptyData />
      </template>
    </VanPullRefresh>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch, onMounted } from "vue";
import useGetPoints from "../hooks/useGetPoints";
import { IntegralEnum, StoreIntegralRouteTypeEnum } from "@/views/StoreModule/enums";
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import PointCard from "./PointCard.vue";

defineOptions({ name: 'CouponDetails' });

/** props */
const props = defineProps<{
  pointType: IntegralEnum;
  userId: string;
  type: StoreIntegralRouteTypeEnum;
}>();

const { pointType: pointTypeRef } = toRefs(props);

const {
  isPageLoadingRef,
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  pointsList,
  model: modelRef,
  onRefresh,
  onLoad,
  getPointsDetailsList
} = useGetPoints({
  io: props.pointType,
  customerId: props.userId,
  requestPageSource: props.type
});

/** 组件挂载 */
onMounted(() => {
  getPointsDetailsList();
});

/** 监听 */
watch(() => props.pointType, (newVal) => {
  if (newVal) {
    modelRef.value.io = newVal;
    onRefresh();
  }
});

defineExpose({
  onRefresh
});
</script>

<style lang="less" scoped>
.point-list-content {
  height: 100%;
  padding: 0px 12px 0px 12px;
  box-sizing: border-box;
  overflow-y: auto;
}
</style>
