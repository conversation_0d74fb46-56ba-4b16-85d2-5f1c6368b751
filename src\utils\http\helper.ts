import { useUserInfoStoreWithoutSetup } from '@/stores/modules/user'
import { clearAllStorage } from '@/utils/cache/storage'
import { navigateTo } from '@/routes/utils'
import { RoutesName } from '@/routes/enums/routeNameEnum'

export function logoutHandler() {
  clearAllStorage()
  const userStore = useUserInfoStoreWithoutSetup()
  userStore.$reset()
  uni.showToast({
    title: `当前登录已失效`,
    icon: 'none',
  })
  navigateTo({
    url: RoutesName.StoreLogin,
  })
}
