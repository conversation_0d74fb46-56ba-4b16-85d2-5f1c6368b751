<template>
  <div class="warrper">
    <JLoadingWrapper :show="isPageLoadingRef">
      <div class="search">
        <div class="searchWarpper">
          <div style="display: flex;align-items: center;gap: 8px;">
            <div class="vertical-line"></div>
            <span class="text">查询会员的领用情况</span>
          </div>

          <div class="screen" @click="changeSelect">
            <span>筛选</span>
            <SvgIcon name="dropDown" style="font-size: 18px;" :class="{'rotate-180': showSearchTag, 'icon': true }" />
          </div>
        </div>
      </div>
      <van-pull-refresh
        class="content-wrapper"
        v-model="groupMgrListStatusReactive.isPullLoading"
        @refresh="onGroupMgrListRefresh"
      >
        <van-list
          :offset="50"
          v-if="listData.length"
          v-model:loading="groupMgrListStatusReactive.isNextPageLoading"
          @load="onGroupMgrListNextPageLoad"
          :finished="groupMgrListStatusReactive.isNextPageFinished"
        >
          <ListCard v-for="item in listData" :data="item" :searchParams="params" :showDetail="true"></ListCard>
        </van-list>
        <EmptyData v-else />
      </van-pull-refresh>
    </JLoadingWrapper>
    <TagSearchPopup v-model:show="showSearchTag" @update:search="updateSearch" />
  </div>
</template>

<script setup lang="ts">
import { ref,reactive,watch,onMounted } from "vue";
import { getCouponStatDataPage } from "@/services/storeApi";
import {useMessages} from "@/hooks/useMessage"
/**  相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import TagSearchPopup from "./components/TabSearchPopup/index.vue";
import ListCard from './components/ListCard/index.vue';
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

const {createMessageError,createMessageSuccess} = useMessages()
const showSearchTag = ref(false)
const changeSelect =()=>{
    showSearchTag.value = true
}
const isPageLoadingRef = ref(false)
const listData = ref([]);
const welfarePageVO = {
    current:1,
    size:30,
    total:1
}
onMounted(async()=>{
    await loadData()
})
let params = reactive({
    staffShortId:'',
    csShortId:'',
})
const loadData = async() =>{
    isPageLoadingRef.value = true
    groupMgrListStatusReactive.isNextPageLoading = true
    let param = {
        data: {...params},
        pageVO: {
            current:welfarePageVO.current,
            size: welfarePageVO.size
        },
    }
    try {
        const {current,size,total,records} = await getCouponStatDataPage(param)
        welfarePageVO.current = Number(current)
        welfarePageVO.size = Number(size)
        welfarePageVO.total = Number(total)
        listData.value.push(...records)
        if(Number(welfarePageVO.current) * Number(welfarePageVO.size) >=  Number(welfarePageVO.total)){
            groupMgrListStatusReactive.isNextPageFinished = true
        }
    } catch (error) {
        createMessageError("获取福利券统计数据异常")
    }finally{
        // groupMgrListStatusReactive.isNextPageFinished = true
        groupMgrListStatusReactive.isNextPageLoading = false
        groupMgrListStatusReactive.isPullLoading = false
        isPageLoadingRef.value = false
    }
}
const groupMgrListStatusReactive = reactive({
        isPullLoading:false,
        isNextPageLoading:false,
        isNextPageFinished:false
})
function onGroupMgrListRefresh(){
    listData.value = []
    groupMgrListStatusReactive.isPullLoading = true
    loadData()
}
function onGroupMgrListNextPageLoad(){
    if(Number(welfarePageVO.current) * Number(welfarePageVO.size) <  Number(welfarePageVO.total)){
        welfarePageVO.current++
        groupMgrListStatusReactive.isNextPageLoading = true
        loadData()
    }
}
function updateSearch(data){
    params.staffShortId = data.staffShortId
    params.csShortId = data.csShortId
    welfarePageVO.current = 1
    listData.value = []
    loadData()
}
</script>
<style lang="less" scoped>
.warrper{
    width:100%;
    height:100vh;
    // background-color: white;
    .search{
        width: 100%;
        font-size: 15px;
        .searchWarpper{
            display:flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            .vertical-line{
                width: 4px;
                height: 16px;
                background: #EF1115;
                border-radius: 99px;
            }
            .text{
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 18px;
                color: #333333;
                line-height: 28px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .screen{
                display:flex;
                align-items: center;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 20px;
                text-align: right;
                font-style: normal;
                text-transform: none;
                .search-icon{
                    margin-left: 4px;
                    width: 0;
                    height: 0;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 6px solid black;
                }
            }
        }
    }
    .content-wrapper{
        width:100%;
        height: calc(100% - 52px);
        overflow-y: scroll;
    }
}
</style>
