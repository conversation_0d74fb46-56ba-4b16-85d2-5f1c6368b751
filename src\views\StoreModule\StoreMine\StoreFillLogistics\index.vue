<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <div class="store_fill_logistics_wrapper">
      <!-- 表单 -->
      <div class="store_fill_logistics_form">
        <!-- 物流公司 -->
        <div class="form-item">
          <div class="form-item-label">
            <span class="tip">*</span>
            <span style="margin-right: 8px;width: 56px;">物流公司</span>
          </div>
          <VanField
            v-model="formValue.logisticsCompany"
            placeholder="请选择"
            readonly
            :maxlength="200"
            :border="false"
            @click="showLogisticsCompany = true"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
          <span class="logistics">
            <VanIcon name="arrow-down" />
          </span>
        </div>
        <!-- 物流单号 -->
        <div class="form-item">
          <div class="form-item-label">
            <span class="tip">*</span>
            <span style="margin-right: 8px;width: 56px;">物流单号</span>
          </div>
          <VanField
            v-model="formValue.trackingNumber"
            placeholder="请输入物流单号"
            :maxlength="200"
            :border="false"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
        </div>
        <!-- 物流费用（元） -->
        <div class="form-item">
          <div class="form-item-label">
            <span class="tip">*</span>
            <span style="margin-right: 8px;width: 120px;">物流费用（元）</span>
          </div>
          <VanField
            v-model="formValue.logisticsFee"
            placeholder="请输入费用金额"
            :max="10000"
            type="number"
            :border="false"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
            :formatter="formatDecimal"
          />
        </div>
        <!-- 其它费用类型 -->
        <div class="form-item">
          <div class="form-item-label">
            <span style="margin-right: 8px;width: 120px;">其它费用类型</span>
          </div>
          <VanField
            v-model="formValue.otherFeeType"
            placeholder="请输入其它费用类型"
            :maxlength="200"
            :border="false"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
        </div>
        <!-- 其它费用（元） -->
        <div class="form-item">
          <div class="form-item-label">
            <span style="margin-right: 8px;width: 120px;">其它费用（元）</span>
          </div>
          <VanField
            v-model="formValue.otherFee"
            placeholder="请输入费用金额"
            :max="10000"
            type="number"
            :border="false"
            :formatter="formatDecimal"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
        </div>
        <!-- 备注 -->
        <div class="form-item">
          <div class="form-item-label">
            <span style="margin-right: 8px;width: 56px;">备注</span>
          </div>
          <VanField
            v-model="formValue.remark"
            placeholder="选填"
            :maxlength="200"
            :border="false"
            style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
          />
        </div>
        <!-- 上传凭证（物流信息/货品装箱照片） -->
        <div class="form-item">
          <div class="form-item-label">
            <span class="tip">*</span>
            <span style="margin-right: 8px;width: 260px;">上传凭证（物流信息/货品装箱照片）</span>
          </div>
          <VanUploader
            v-model="tempAfterSaleImgVOList"
            :after-read="handleAfterRead"
            @delete="handleDeleteUploader"
            :max-count="9"
            multiple
          />
        </div>
      </div>
      <!-- 提交 -->
      <div class="footer">
        <VanButton
          type="danger"
          block
          round
          @click="handleSubmit"
          :disabled="isPageLoadingRef"
          style="width: 100%;height: 36px;"
        >
          提交
        </VanButton>
      </div>
    </div>
    <!-- 物流公司 -->
    <VanActionSheet v-model:show="showLogisticsCompany" title="选择物流公司" :closeable="false">
      <div class="refund_reason_list">
        <div
          class="refund_reason_item"
          v-for="item in logisticsCompanyList"
          :key="item.value"
          @click="onSelectRefundReason(item.value, item.label)"
        >
          <span :class="{'active_title': formValue.logisticsCompany == item.label }">{{ item.label }}</span>
        </div>
      </div>
    </VanActionSheet>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs } from "vue";
import { useRouter } from "vue-router";
import { showToast } from 'vant';
import { useMessages } from "@/hooks/useMessage";
import { isArray } from "@/utils/isUtils";
import { StoreLogisticsRouteTypeEnum } from "@/views/StoreModule/enums";
import { getLogisticsCompany, submitLogistics, uploadBase64ImgApi, confirmReturn } from "@/services/storeApi";
/** 相关组件 */
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

defineOptions({ name: "StoreFillLogistics" });

/** props */
const props = defineProps<{
  afterSalesOrderId: string; // 售后单id，多个以逗号隔开
  routeType: StoreLogisticsRouteTypeEnum; // 路由类型
}>();

const { routeType: propsRouteType } = toRefs(props);
const router = useRouter();
const { createMessageError, createMessageSuccess } = useMessages();
/** 售后单ID */
const afterSalesOrderId = ref<string[]>(props.afterSalesOrderId.split(","));
const isPageLoadingRef = ref<boolean>(false);

/** 表单参数 */
const initparams = {
  logisticsCode: null, // 物流公司编码
  logisticsCompany: null, // 物流公司
  trackingNumber: null, // 物流单号
  logisticsFee: null, // 物流费用
  otherFeeType: null, // 其他费用类型
  otherFee: null, // 其他费用
  remark: null, // 备注
  afterSaleImgVOList: [], // 物流凭证图片
};
const formValue = ref({ ...initparams });
const tempAfterSaleImgVOList = ref([]); // 物流凭证图片

function  formatDecimal(value) {
  // 先去除非数字和点的字符
  value = value.replace(/[^\d.]/g, '');

  // 保留两位小数
  const parts = value.split('.');
  if (parts.length > 1) {
    parts[1] = parts[1].slice(0, 2);
    value = parts.join('.');
  }

  return value;
}

/** 验证表单 */
function _validateForm() {
  const requiredFields = [
    { field: 'logisticsCompany', message: '请选择物流公司' },
    { field: 'trackingNumber', message: '请输入物流单号' },
    { field: 'logisticsFee', message: '请输入物流费用' },
    { field: 'afterSaleImgVOList', message: '请上传物流凭证图片' }
  ];

  for (const { field, message } of requiredFields) {
    if (!formValue.value[field] || (Array.isArray(formValue.value[field]) && formValue.value[field].length === 0)) {
      showToast(message);
      return false;
    }
  }

  // 额外的验证逻辑，例如费用必须是数字
  if (isNaN(Number(formValue.value.logisticsFee))) {
    showToast('物流费用必须是数字');
    return false;
  }

  if (isNaN(Number(formValue.value.otherFee))) {
    showToast('其他费用必须是数字');
    return false;
  }

  return true;
}

/** 获取参数 */
function _getParams() {
  return {
    returnLogisticsVO: {
      ...formValue.value,
      logisticsFee: Number(formValue.value.logisticsFee) * 100,
      otherFee: Number(formValue.value.otherFee) * 100,
      afterSaleImgVOList: formValue.value.afterSaleImgVOList.map((item, index) => ({
        img: item?.img,
        path: item?.path
      })),
    },
    idList: afterSalesOrderId.value
  }
}

/** 提交 */
async function handleSubmit() {
  try {
    // 先验证表单
    let valid =  _validateForm();
    if (!valid) return;
    isPageLoadingRef.value = true;
    // 确认收货并填写物流信息
    if (propsRouteType.value == StoreLogisticsRouteTypeEnum.CONFIRM_AND_FILL_LOGISTICS) {
      await confirmReturn(_getParams());
    }
    // 填写物流信息
    else if (propsRouteType.value == StoreLogisticsRouteTypeEnum.FILL_LOGISTICS) {
      await submitLogistics(_getParams());
    }
    showToast("提交成功");
    isPageLoadingRef.value = false;
    router.back();
  } catch (error) {
    createMessageError("提交失败：" + error);
    isPageLoadingRef.value = false;
  }
}

/** 物流公司显隐 */
const showLogisticsCompany = ref(false);
const logisticsCompanyList = ref([]);
/** 选择物流公司 */
function onSelectRefundReason(value: string, label: string) {
  formValue.value.logisticsCompany = label;
  formValue.value.logisticsCode = value;
  showLogisticsCompany.value = false;
}

/** 查询物流公司 */
async function getLogisticsCompanyList() {
  try {
    let _params = {
      pageVO:{
        current: 1,
        size: 100
      }
    };
    const { records } = await getLogisticsCompany(_params);
    if (records?.length > 0 ) {
      logisticsCompanyList.value = records.map((item: any) => {
        return { label: item.name, value: item.code };
      });
    }
  } catch (error) {
    console.log("error", error);
  }
}

function removeExtension(filename) {
  return filename.replace(/\.(png|jpg|jpeg|gif|webp)$/i, '');
}

/** 删除图片 */
function handleDeleteUploader(field, { index }) {
  // 实际存储的文件数据中删除
  formValue.value.afterSaleImgVOList.splice(index, 1);
}

/** 文件读取完成后的回调函数 */
async function handleAfterRead(file) {
  try {
    if (isArray(file)) {
      // 如果是数组，遍历每个文件并依次上传
      for (const singleFile of file) {
        await handleSingleFile(singleFile);
      }
    } else {
      // 如果不是数组，直接处理单个文件
      await handleSingleFile(file);
    }
  } catch (error) {
    createMessageError("上传图片失败：" + error);
  }
}

/** 上传单个文件 */
async function handleSingleFile(file) {
  try {
    file.status = "uploading";
    file.message = "上传中...";
    if (file.content) {
    const base64Data = file.content;
    // 去掉可能的数据URL前缀
    const pureBase64 = base64Data.replace(/^data:image\/\w+;base64,/, '');
    let _params = {
      fileName: removeExtension(file?.file?.name ?? "未知文件"),
      base64: pureBase64,
    };
    const imgFilePath = await uploadBase64ImgApi(_params);
    if (imgFilePath) {
      formValue.value.afterSaleImgVOList.push({
        img: removeExtension(file?.file?.name ?? "未知文件"),
        path: imgFilePath,
      });
      file.status = "done";
    }
  }
  } catch (error) {
    file.status = "failed";
    file.message = "上传失败";
  }
}

/** 组件挂载 */
onMounted(async () => {
  /** 获取物流公司 */
  await getLogisticsCompanyList();
});
</script>

<style lang="less" scoped>
.store_fill_logistics_wrapper {
    width: 100%;
    height: 100vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .store_fill_logistics_form {
        flex: 1;
        height: calc(100% - 36px);
        padding: 12px 12px 0px 12px;
        box-sizing: border-box;
        overflow-y: auto;
        .form-item {
            width: 100%;
            display: flex;
            flex-direction: column;
            margin-bottom: 16px;
            position: relative;
            .form-item-label {
                margin-bottom: 8px;
                .tip {
                  color: #FF3E3E;
                  margin-top: 4px;
                }

                span {
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 400;
                  font-size: 14px;
                  color: #333333;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                }
            }
            &:last-child {
                margin-bottom: 0px;
            }
            .logistics {
                position: absolute;
                top: 36px;
                right: 12px;
            }
        }
    }
    .footer {
        padding: 12px;
        box-sizing: border-box;
        padding-bottom: env(safe-area-inset-bottom);
        :deep(.van-button__text) {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 14px;
          color: #FFFFFF;
          line-height: 24px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
    }
}
.refund_reason_list {
    height: 60vh;
    .refund_reason_item {
        width: 100%;
        height: 54px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #E5E6EB;
        box-sizing: border-box;
        &:first-child {
            border-top: 1px solid #E5E6EB;
        }
    }
}
.active_title {
  color: #4DA4FF;
}
</style>
