<template>
  <div class="address_wrapper">
    <div class="address_info_container">
      <!-- 图片 -->
      <img :src="addressSrc" alt="" />
      <div class="address_info">
        <div class="address_info_top">
          <span class="name">{{ addressInfoRef?.name ?? '-' }}</span>
          <span class="phone">{{ addressInfoRef?.mobile ?? '-' }}</span>
        </div>
        <p class="address">{{ addressStr }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, computed } from "vue";
/** 静态资源 */
import addressSrc from "@/assets/storeImage/storeMine/address.png";

defineOptions({ name: 'StoreDeliveryInfo' });
/** props */
const props = defineProps<{
    addressInfo?: {
        id?: string;
        name?: string;
        mobile?: string;
        companyId?: string;
        company?: string;
        provinceId?: string;
        province?: string;
        cityId?: string;
        cityName?: string;
        areaId?: string;
        area?: string;
        townId?: string;
        town?: string;
        address?: string;
        isDefault?: 0 | 1;
        csWxNickname?: string;
    };
}>();

const { addressInfo: addressInfoRef } = toRefs(props);

const addressStr = computed(() => {
    return `${addressInfoRef.value?.province + addressInfoRef.value?.cityName + addressInfoRef.value?.area + addressInfoRef.value?.town + addressInfoRef.value?.address}`;
});
</script>

<style lang="less" scoped>
.address_wrapper {
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 10px 12px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .address_info_container {
        flex: 1;
        display: flex;
        gap: 8px;
        img {
            width: 32px;
            height: 32px;
        }
        .address_info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            .address_info_top {
                display: flex;
                align-items: center;
                gap: 4px;
                .name,
                .phone {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    font-size: 14px;
                    color: #333333;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }
            }
            .address {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                line-height: 18px;
                color: #666666;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>
