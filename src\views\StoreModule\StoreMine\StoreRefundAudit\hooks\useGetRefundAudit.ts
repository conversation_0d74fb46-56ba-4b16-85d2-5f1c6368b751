import { ref, reactive, watch, effectScope, onScopeDispose } from "vue";
import { AfterSaleStatusEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { getStoreAfterSaleOrderList } from "@/services/storeApi";

export default function useGetRefundAudit() {
  const scope = effectScope();
  const { createMessageSuccess, createMessageError } = useMessages();

  const isPageLoadingRef = ref(false);
  /** 退款审核数据 */
  const refundAuditListRef = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 搜索值 */
  const searchValueRef = ref("");

  /** 其他状态类型 */
  const otherStatusRef = ref(AfterSaleStatusEnum.CUSTOMER_WITHDRAWN);

  /** 分页 */
  const pageVO = reactive({
    size: 30,
    current: 1,
    total: 0,
  });

  /** tab */
  const activeTabRef = ref(AfterSaleStatusEnum.ALL);
  /** 退款审核状态 */
  const refundAuditStatusList = [
    {
      label: "全部",
      value: AfterSaleStatusEnum.ALL,
    },
    {
      label: "待受理",
      value: AfterSaleStatusEnum.PENDING_MERCHANT,
    },
    {
      label: "待收货",
      value: AfterSaleStatusEnum.PENDING_MERCHANT_RECEIPT,
    },
    {
      label: "其他状态",
      value: AfterSaleStatusEnum.OTHER,
    },
  ];

  /** 其他状态菜单选项 */
  const otherStatusList = [
    {
      text: "客户撤回申请",
      value: AfterSaleStatusEnum.CUSTOMER_WITHDRAWN,
    },
    {
      text: "商家拒绝退款",
      value: AfterSaleStatusEnum.MERCHANT_REFUSED,
    },
    {
      text: "待客户退货",
      value: AfterSaleStatusEnum.PENDING_RETURN,
    },
    {
      text: "退货退款关闭",
      value: AfterSaleStatusEnum.RETURN_CLOSED,
    },
    {
      text: "退款中",
      value: AfterSaleStatusEnum.REFUNDING,
    },
    {
      text: "待打款",
      value: AfterSaleStatusEnum.PENDING_PAYMENT,
    },
    {
      text: "退款完成",
      value: AfterSaleStatusEnum.REFUND_COMPLETED,
    },
    {
      text: "商家同意取消订单",
      value: AfterSaleStatusEnum.CANCEL_APPROVED,
    },
    {
      text: "商家拒绝取消订单",
      value: AfterSaleStatusEnum.CANCEL_REJECTED,
    },
  ];

  /** 获取搜索参数 */
  function getSearchParams() {
    const status = activeTabRef.value === AfterSaleStatusEnum.ALL ? null : activeTabRef.value;
    const searchValue = searchValueRef.value.trim();
    const otherStatus = activeTabRef.value === AfterSaleStatusEnum.OTHER ? otherStatusRef.value : undefined;

    // 基础参数结构
    const params = {
      data: {
        recordNo: searchValue,
        state: activeTabRef.value === AfterSaleStatusEnum.OTHER ? otherStatus : status,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };

    return params;
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getStoreRefundReviewAfterSaleOrders();
    }
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    initStoreRefundReviewAfterSaleOrderList();
  }

  /** 初始化 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 退款审核订单列表 */
  async function getStoreRefundReviewAfterSaleOrders() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getStoreAfterSaleOrderList(_params);

      // 更新订单列表
      if (current === 1) {
        refundAuditListRef.value = records;
      } else if (records.length) {
        refundAuditListRef.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < Number(total);

      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 店铺售后订单数据初始化 */
  async function initStoreRefundReviewAfterSaleOrderList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreRefundReviewAfterSaleOrders();
    isPageLoadingRef.value = false;
  }

  /** 在作用域内运行监听器 */
  scope.run(() => {
    /** 监听 */
    watch(
      () => activeTabRef.value,
      newVal => {
        initStoreRefundReviewAfterSaleOrderList();
      },
      {
        immediate: true,
      },
    );
  });

  /** 作用域销毁时清理 */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    isPageLoadingRef,
    activeTabRef,
    refundAuditStatusList,
    refundAuditListRef,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    searchValueRef,
    otherStatusList,
    otherStatusRef,
    onLoad,
    initStoreRefundReviewAfterSaleOrderList,
    onRefresh
  };
}
