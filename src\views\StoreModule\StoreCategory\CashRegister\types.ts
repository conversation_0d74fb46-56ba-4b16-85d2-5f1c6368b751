export const enum PayTypeEnum {
    /**在线支付 */
    OnlinePay=1,
    /**物流代收 */
    LogisticsPay,
    /**定价支付 */
    EarnestPay,
    /**积分支付 */
    PointPay,
    /**现金和积分支付 */
    PointAndOnlinePay
}
export interface OrderPayTypeDTO {
    /**
     * 物流代收金额，单位分
     */
    cashOnDelivery?: number | null;
    /**
     * 商品总金额，单位分
     */
    goodsAmount?: number | null;
    /**
     * 主键
     */
    id?: number | null;
    /**
     * 订单总金额，单位分
     */
    money?: number | null;
    /**
     * 在线支付金额，单位分
     */
    onlinePayment?: number | null;
    /**
     * 订单编号
     */
    orderCode?: null | string;
    /**
     * 支付方式。0=未支付；1=在线支付；2=物流代收；3=支付定金；
     */
    payType?: number | null;
    /**
     * 运费，单位分
     */
    shippingFee?: number | null;
    /**
     * 修改时间
     */
    updateTime?: null | string;
    orderItemList:AppletOrderItemDTO[]
}

export interface AppletOrderItemDTO {
    /**
     * 物流代收金额
     */
    cashOnDelivery?: number | null;
    /**
     * 数量
     */
    count?: number | null;
    /**
     * 创建时间
     */
    createTime?: null | string;
    /**
     * 定金支付单价
     */
    downPayment?: number | null;
    /**
     * 主键
     */
    id?: number | null;
    /**
     * 是否支持物流代收。0=否；1=是
     */
    isCashOnDelivery?: number | null;
    /**
     * 是否支持定金支付。0=否；1=是
     */
    isDownPayment?: number | null;
    /**
     * 是否处方药
     */
    isPres?: number | null;
    /**
     * 在线支付金额
     */
    onlinePayment?: number | null;
    /**
     * 订单 ID
     */
    orderId?: number | null;
    /**
     * 支付方式。0=未支付；1=在线支付；2=物流代收；3=支付定金；
     */
    payType?: number | null;
    /**
     * 单价。单位分
     */
    price?: number | null;
    /**
     * 商品 ID
     */
    productId?: number | null;
    /**
     * 图片路径
     */
    productImgPath?: null | string;
    /**
     * 商品名称
     */
    productName?: null | string;
    /**
     * 规格 ID
     */
    specId?: number | null;
    /**
     * 规格名称
     */
    specName?: null | string;
    /**
     * 商品类别。1=药品；2=疗法
     */
    type?: number | null;
    /**
     * 修改时间
     */
    updateTime?: null | string;
    /**
     * 每次购买数量上限
     */
    upper?: number | null;
    /**
     * 版本号
     */
    version?: number | null;
    isVirtual?:0|1;
}