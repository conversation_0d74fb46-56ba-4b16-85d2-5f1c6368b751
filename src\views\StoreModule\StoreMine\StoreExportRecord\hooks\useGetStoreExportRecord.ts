import { ref, reactive } from "vue";
import { showToast } from "vant";
import { useMessages } from "@/hooks/useMessage";
import { pageExportRecord } from "@/services/storeApi";

export default function useGetStoreExportRecord() {
  const isPageLoadingRef = ref(false);
  const { createMessageSuccess, createMessageError } = useMessages();

  /** 导出记录列表数据 */
  const storeExportRecordList = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 初始化参数 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 获取搜索参数 */
  function getSearchParams() {
    return {
      data: {},
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };
  }

  /** 获取导出记录列表数据 */
  async function getStoreExportRecordList() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await pageExportRecord(_params);

      // 更新订单列表
      if (current === 1) {
        storeExportRecordList.value = records;
      } else if (records.length) {
        storeExportRecordList.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 数据初始化 */
  async function initStoreExportRecordList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getStoreExportRecordList();
    isPageLoadingRef.value = false;
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getStoreExportRecordList();
    }
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    initStoreExportRecordList();
  }

  return {
    isPageLoadingRef,
    storeExportRecordList,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    onRefresh,
    onLoad,
    initStoreExportRecordList,
  };
}
