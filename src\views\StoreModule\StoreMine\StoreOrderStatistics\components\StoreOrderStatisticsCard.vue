<template>
  <div class="order-card">
    <div class="order-card__product">
      <span class="product-name">{{ `${productName}(${specName})` }}</span>
    </div>
    <div class="order-card__quantity">
      <span>{{ productNum }}</span>
    </div>
    <div class="order-card__price">
      <span>{{ orderNum }}</span>
    </div>
    <div class="order-card__amount">
      <span>{{ verificationOrderNum }}</span>
    </div>
    <div class="order-card__other">
      <span>{{ verificationProductNum }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from 'vue';

defineOptions({ name: 'StoreOrderStatisticsCard' });

/** props */
const props = defineProps<{
  orderInfo: {
    productName?: string;
    specName?: string;
    productNum?: string;
    orderNum?: string;
    totalAmount?: string;
    verificationOrderNum?: string;
    verificationProductNum?: string;
  };
}>();

const {
  productName,
  specName,
  productNum,
  orderNum,
  totalAmount,
  verificationOrderNum,
  verificationProductNum
} = toRefs(props.orderInfo);
</script>

<style lang="less" scoped>
.order-card {
  display: flex;
  align-items: center;
  height: 64px;
  width: 100%;

  &>div {
    box-sizing: border-box;
    padding: 0 8px;

    span {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 16px;
      text-align: left;
      display: block;
    }
  }

  &__product {
    width: 96px;
    padding-left: 8px;

    .product-name {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      height: 100%;
      word-break: break-all;
    }
  }

  &__quantity,
  &__price,
  &__amount,
  &__other {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
