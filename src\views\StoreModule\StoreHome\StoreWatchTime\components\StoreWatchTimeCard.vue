<template>
  <div class="store-watch-time-wrapper">
    <div class="left">
      <!-- 头像 -->
      <div class="avatar">
        <img :src="watchTimeInfoRef?.img ? watchTimeInfoRef?.img : defaultAvatar" alt="" />
      </div>
      <!-- 个人信息 -->
      <div class="info">
        <div class="info_name">{{ watchTimeInfoRef?.nickname }}</div>
        <div class="info_id">{{`ID：${watchTimeInfoRef?.shortId}`}}</div>
      </div>
    </div>

    <div class="right">
      <!-- 观看时长 -->
      <div class="watch-time">
        <div class="watch-time_title">{{ splitDateTime(watchTimeInfoRef?.watchDate)['date'] }}</div>
        <div class="watch-time_time">{{`${splitDateTime(watchTimeInfoRef?.firstWatchTime)['time']} - ${splitDateTime(watchTimeInfoRef?.lastWatchTime)['time']}`}}</div>
      </div>
      <div class="watch-time_count">
        <span>{{`${watchTimeInfoRef?.totalWatchMinutes}分钟`}}</span>
        <VanIcon name="arrow" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from "vue";
/** 相关静态资源 */
import defaultAvatar from "@/assets/image/system/account/defaultAvatar.jpg";

defineOptions({ name: "StoreWatchTime" });

/** props */
const props = defineProps<{
  watchTimeInfo: {
    nickname?: string;
    shortId?: string;
    img?: string;
    watchDate?: string;
    firstWatchTime?: string;
    lastWatchTime?: string;
    totalWatchMinutes?: number;
  }
}>();

const { watchTimeInfo: watchTimeInfoRef } = toRefs(props);

function splitDateTime(datetimeStr) {
  if (!datetimeStr) return { date: null, time: null };
  
  const [date, time] = datetimeStr.split(' ');
  return { date, time };
}
</script>

<style lang="less" scoped>
.store-watch-time-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    box-sizing: border-box;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    .left {
        display: flex;
        align-items: center;
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 12px;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            .info_name {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .info_id {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                color: #333333;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }
    .right {
        display: flex;
        align-items: center;
        gap: 12px;
        .watch-time {
            display: flex;
            flex-direction: column;
            gap: 4px;
            .watch-time_title {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 14px;
                color: #333333;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
            .watch-time_time {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 400;
                font-size: 12px;
                color: #333333;
                line-height: 20px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
        .watch-time_count {
            display: flex;
            align-items: center;
            gap: 4px;
            span {
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                text-align: right;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>
