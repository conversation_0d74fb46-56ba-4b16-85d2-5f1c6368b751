<template>
  <div class="member-overview">
    <div class="member-overview-container">
      <div class="member-overview-container-left">
        <div class="label">总客户数</div>
        <div class="value">{{ memberOverviewRef.totalCount }}</div>
      </div>
      <div class="member-overview-container-right">
        <div class="item">
          <div class="label">今日新增</div>
          <div class="value">{{ memberOverviewRef.todayNew }}</div>
        </div>
        <div class="item">
          <div class="label">昨日新增</div>
          <div class="value">{{ memberOverviewRef.yesterdayNew }}</div>
        </div>
        <div class="item">
          <div class="label">本月新增</div>
          <div class="value">{{ memberOverviewRef.monthNew }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { toRefs } from "vue";

/** props */
const props = defineProps<{
  memberOverview: {
    totalCount: number;
    todayNew: number;
    yesterdayNew: number;
    monthNew: number;
  };
}>();

const { memberOverview: memberOverviewRef } = toRefs(props);
</script>

<style scoped lang="less">
.member-overview {
  width: 100vw;
  padding: 0 10px;
  height: 120px;
  background: #fff;
  box-sizing: border-box;
  &-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #fff;
    background-image: url("@/assets/store0602Image/memberOverviewBg.png");
    background-repeat: no-repeat;
    background-size: 100% calc(100% - 6px);
    &-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: 6px;
      .label {
        margin-bottom: 4px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 18px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .value {
        line-height: 58px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: Bebas Neue, Bebas Neue;
        font-weight: 400;
        font-size: 48px;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    &-right {
      padding-bottom: 6px;
      .item {
        display: flex;
        margin-bottom: 8px;
        .label {
          margin-right: 24px;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 14px;
          color: #FFFFFF;
          line-height: 18px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .value {
          max-width: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 14px;
          color: #FFFFFF;
          line-height: 18px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
</style>
