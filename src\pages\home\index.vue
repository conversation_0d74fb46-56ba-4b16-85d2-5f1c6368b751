<template>
  <TabbarLayouts>
    <view>
      健康商城
      <van-button type="primary" @click="goLogin">去登录</van-button>
    </view>
  </TabbarLayouts>
</template>

<script lang="ts" setup name="">
import { onShow } from '@dcloudio/uni-app'
import { RoutesName } from '@/routes/enums/routeNameEnum'
import { routesMap } from '@/routes/maps'
import { useTabbar } from '@/components/Tabbar/hooks/useTabbar'
/** 相关组件 */
import TabbarLayouts from '@/layouts/tabbar.vue'
import { useRouter } from '@/hooks/common'
const { routerPushByKey } = useRouter()
const { setSelectedTabKey, setTabbarDisplay } = useTabbar()

onShow(() => {
  setSelectedTabKey(routesMap[RoutesName.StoreHome].path)
  setTabbarDisplay(true)
})
const goLogin = () => {
  routerPushByKey('Login')
}
</script>

<style lang="scss" scoped></style>
