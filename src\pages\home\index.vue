<template>
  <TabbarLayouts>
    <view>健康商城</view>
  </TabbarLayouts>
</template>

<script lang="ts" setup name="">
import { onShow } from '@dcloudio/uni-app'
import { RoutesName } from '@/routes/enums/routeNameEnum'
import { routesMap } from '@/routes/maps'
import { useTabbar } from '@/components/Tabbar/hooks/useTabbar'
/** 相关组件 */
import TabbarLayouts from '@/layouts/tabbar.vue'

const { setSelectedTabKey, setTabbarDisplay } = useTabbar()

onShow(() => {
  setSelectedTabKey(routesMap[RoutesName.StoreHome].path)
  setTabbarDisplay(true)
})
</script>

<style lang="scss" scoped></style>
