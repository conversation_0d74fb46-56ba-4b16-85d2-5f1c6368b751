<template>
  <!-- <router-view 
    v-slot="{ Component }"
  >
    <keep-alive :include="keepAliveRouteListRef">
      <component :is="Component" :key="$route.fullPath" />
    </keep-alive>
  </router-view> -->
  <router-view />
</template>
<script setup lang="ts">
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';

const {keepAliveRouteListRef} = useKeepAliveRoute()
console.log('enter');

</script>
