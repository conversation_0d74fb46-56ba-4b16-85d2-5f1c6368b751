import { ref,reactive } from 'vue';
import { getStoreLogisticsList } from "@/services/storeApi/store-logistics";
import {useMessages} from "@/hooks/useMessage"
const {createMessageError,createMessageSuccess} = useMessages()
import { useUserRole } from "@/views/StoreModule/hooks";
const { storeId } = useUserRole();
interface PageVo {
    current: number;
    size: number;
    total: number;
  }
const groupMgrListStatusReactive = reactive({
    isPullLoading:false,
    isNextPageLoading:false,
    isNextPageFinished:false
}) 
const listData = ref([])
const isPageLoadingRef = ref(false);
export function StoreLogistics(){
const pageVo = reactive<PageVo>({
    current: 1,
    size: 50,
    total: 0,
});
const getLogisticsPage = async(data,isClear) =>{
    const params = {
        data:{
            storeId:storeId.value,
            productId:data
        },
        pageVo:{
            size:pageVo.size,
            current:pageVo.current
        }
    }
    try {
        const { current, size, total, records } = await getStoreLogisticsList(params);
        isClear && (listData.value = []);
        pageVo.current = Number(current);
        pageVo.size = Number(size);
        pageVo.total = Number(total);
        listData.value.push(...records);
        if (Number(pageVo.current) * Number(pageVo.size) >= Number(pageVo.total)) {
          groupMgrListStatusReactive.isNextPageFinished = true;
        }
      } catch (error) {
        createMessageError("获取门店物流列表异常");
      } finally {
        // groupMgrListStatusReactive.isNextPageFinished = true;
        groupMgrListStatusReactive.isNextPageLoading = false;
        groupMgrListStatusReactive.isPullLoading = false;
        isPageLoadingRef.value = false;
      }
}
function onGroupMgrListRefresh(keyword){
    listData.value = []
    pageVo.current = 1
    groupMgrListStatusReactive.isPullLoading = true
    getLogisticsPage(keyword,true)
}
function onGroupMgrListNextPageLoad(keyword){
    if(Number(pageVo.current) * Number(pageVo.size) <  Number(pageVo.total)){
        pageVo.current++
        groupMgrListStatusReactive.isNextPageLoading = true
        getLogisticsPage(keyword,false)
    }
}
return{
    getLogisticsPage,
    listData,
    onGroupMgrListRefresh,
    groupMgrListStatusReactive,
    onGroupMgrListNextPageLoad,
    isPageLoadingRef,
}

}